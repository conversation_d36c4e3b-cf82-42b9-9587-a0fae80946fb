'use client'
import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Loading from '@/app/components/base/loading'
import { getOperToken } from '@/utils/user'
import ChatWithHistoryWrap from '@/app/components/base/chat/chat-with-history'

const Chat = ({ params }: { params: { token: string } }) => {
  const router = useRouter()
  const { token } = params
  const operToken = getOperToken()

  useEffect(() => {
    if (operToken)
      router.replace(`/square/market-chat/${token}${location.search}`)
  }, [operToken, router, token])

  if (operToken) {
    return (
      <Loading type='app' />
    )
  }
  else {
    return <ChatWithHistoryWrap fromMarket></ChatWithHistoryWrap>
  }
}

export default React.memo(Chat)
