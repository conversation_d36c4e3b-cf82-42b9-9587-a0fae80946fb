'use client'
import React, { useState } from 'react'
import type { FC } from 'react'
import { useAsyncEffect } from 'ahooks'
// 公共组件
import Loading from '../components/base/loading'
import AppUnavailable from '../components/base/app-unavailable'
import SwrInitor from '../components/swr-initor'
import { checkOrSetAccessToken } from '@/utils/share'
import { usePrivateSystemContext } from '@/context/private-context'

const BaseLayout: FC<{
  children: React.ReactNode
}> = ({ children }) => {
  return (
    <div className="w-full h-full overflow-x-auto">
      <div className='min-w-[300px] h-full'>
        {children}
      </div>
    </div>
  )
}

const Layout: FC<{
  children: React.ReactNode
}> = ({ children }) => {
  // 访问应用是否需要鉴权——私有化
  const { isNeedAppAuth } = usePrivateSystemContext()
  // 应用是否初始化
  const [initialized, setInitialized] = useState(false)
  // 应用不可用
  const [appUnavailable, setAppUnavailable] = useState<boolean>(false)
  // 是否未知原因导致
  const [isUnknownReason, setIsUnknownReason] = useState<boolean>(false)

  useAsyncEffect(async () => {
    if (!initialized) {
      // 检查应用token
      try {
        await checkOrSetAccessToken()
      }
      catch (e: any) {
        if (e.status === 404) {
          setAppUnavailable(true)
        }
        else {
          setIsUnknownReason(true)
          setAppUnavailable(true)
        }
      }
      setInitialized(true)
    }
  }, [])

  if (!initialized)
    return <Loading />

  if (appUnavailable)
    return <AppUnavailable isUnknownReason={isUnknownReason} />

  if (isNeedAppAuth) {
    return <SwrInitor>
      <BaseLayout>
        {children}
      </BaseLayout>
    </SwrInitor>
  }

  return (
    <BaseLayout>
      {children}
    </BaseLayout>
  )
}

export default Layout
