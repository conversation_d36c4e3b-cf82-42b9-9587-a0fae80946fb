'use client'

import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useAppContext } from '@/context/app-context'

export default function DatasetsLayout({ children }: { children: React.ReactNode }) {
  const { isSuperUser } = useAppContext()
  const router = useRouter()
  useEffect(() => {
    if (!isSuperUser)
      return router.replace('/square')
  }, [isSuperUser, router])

  return (
    <>
      {children}
    </>
  )
}
