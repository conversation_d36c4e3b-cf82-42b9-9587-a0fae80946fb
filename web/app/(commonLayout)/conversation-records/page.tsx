'use client'

import { useEffect, useState } from 'react'
import {
  Button,
  DatePicker,
  Pagination,
  Select,
  Space,
  Table,
  Tag,
} from 'antd'
import { useTranslation } from 'react-i18next'
import type { ColumnsType } from 'antd/es/table'
import { DownloadOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import styles from './styles/page.module.css'
import { getConversationRecords } from '@/service/statistics'
import { API_PREFIX } from '@/config'
const { RangePicker } = DatePicker
export default function ConversationRecordsPage() {
  const [selectedDataSource, setSelectedDataSource] = useState<string>('')
  const [selectedStatus, setSelectedStatus] = useState<string>('')
  const [currPage, setCurrPage] = useState<number>(1)
  const [pageSize, setPageSize] = useState<number>(10)
  const [total, setTotal] = useState<number>(0)
  const [loading, setLoading] = useState<any>(true)
  const [dateRanges, setDateRange] = useState<any>([
    dayjs().subtract(7, 'day'),
    dayjs(),
  ])
  const { t } = useTranslation()
  // 数据来源映射函数
  const getDataSourceColor = (source: string) => {
    const colorMap: { [key: string]: string } = {
      market: 'green',
      api: 'blue',
      web: 'orange',
      embed: 'red',
      default: 'default',
    }
    return colorMap[source] || colorMap.default
  }

  // 数据来源显示文本映射
  const getDataSourceText = (source: string) => {
    const textMap: { [key: string]: string } = {
      market: '应用广场',
      api: '后端API',
      web: '前端页面',
      embed: '嵌入网站',
    }
    return textMap[source] || source
  }

  // 将消息数组转换为字符串的函数
  const formatMessagesToString = (messages: any[]): string => {
    if (!Array.isArray(messages))
      return String(messages || '')

    return messages
      .map((message, index) => {
        const keys = Object.keys(message)
        if (keys.length === 0)
          return ''

        const key = keys[0]
        const value = message[key]
        return `${key}: ${value}`
      })
      .join('\n')
  }

  const columns: ColumnsType<any> = [
    {
      title: t('conversation.columns.app_id'),
      dataIndex: 'app_id',
      key: 'app_id',
    },
    {
      title: t('conversation.columns.app_name'),
      dataIndex: 'app_name',
      key: 'app_name',
    },
    {
      title: t('conversation.columns.conversation_name'),
      dataIndex: 'conversation_name',
      key: 'conversation_name',
    },
    {
      title: t('conversation.columns.messages'),
      dataIndex: 'messages',
      key: 'messages',
      width: 300,
      render: (messages: any[]) => (
        <div
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            maxWidth: 280,
          }}
        >
          {formatMessagesToString(messages)}
        </div>
      ),
    },
    {
      title: t('conversation.columns.from_source_user_id'),
      dataIndex: 'from_source_user_id',
      key: 'from_source_user_id',
    },
    {
      title: t('conversation.columns.from_source'),
      dataIndex: 'from_source',
      key: 'from_source',
      render: (source: string | string[]) => {
        if (Array.isArray(source)) {
          return (
            <>
              {source.map(item => (
                <Tag key={item} color={getDataSourceColor(item)}>
                  {getDataSourceText(item)}
                </Tag>
              ))}
            </>
          )
        }
        return (
          <Tag color={getDataSourceColor(source)}>
            {getDataSourceText(source)}
          </Tag>
        )
      },
    },
    {
      title: t('conversation.columns.message_count'),
      dataIndex: 'message_count',
      key: 'message_count',
    },
    {
      title: t('conversation.columns.user_like_count'),
      dataIndex: 'user_like_count',
      key: 'user_like_count',
      render: (value: { user_like: number; user_dislike: number }) => {
        if (!value)
          return ''
        return (
          <div>
            <div>{t('conversation.feedback.like', { count: value.user_like ?? 0 })}</div>
            <div>{t('conversation.feedback.dislike', { count: value.user_dislike ?? 0 })}</div>
          </div>
        )
      },
    },
    {
      title: t('conversation.columns.admin_like_count'),
      dataIndex: 'admin_like_count',
      key: 'admin_like_count',
      render: (value: { admin_like: number; admin_dislike: number }) => {
        if (!value)
          return ''
        return (
          <div>
            <div>{t('conversation.feedback.like', { count: value.admin_like ?? 0 })}</div>
            <div>{t('conversation.feedback.dislike', { count: value.admin_dislike ?? 0 })}</div>
          </div>
        )
      },
    },
    {
      title: t('conversation.columns.content'),
      dataIndex: 'content',
      key: 'content',
      render: (source: string | string[]) => {
        if (Array.isArray(source)) {
          return (
            <>
              {source.map(item => (
                <Tag key={item} color={getDataSourceColor(item)}>
                  {getDataSourceText(item)}
                </Tag>
              ))}
            </>
          )
        }
        return (
          <Tag color={getDataSourceColor(source)}>
            {getDataSourceText(source)}
          </Tag>
        )
      },
    },
    {
      title: t('conversation.columns.updated_at'),
      dataIndex: 'updated_at',
      key: 'updated_at',
    },
    {
      title: t('conversation.columns.created_at'),
      dataIndex: 'created_at',
      key: 'created_at',
    },
  ]
  // Sample data
  const [data, setData] = useState<
  any
  >([])

  const getCollectionList = async () => {
    const res = await getConversationRecords({
      start_date: dayjs(dateRanges[0]).format('YYYY-MM-DD'),
      end_date: dayjs(dateRanges[1]).format('YYYY-MM-DD'),
      page: currPage,
      limit: pageSize,
      rating: selectedStatus || '',
      from_source: selectedDataSource || '',
    })
    setData(res.result.data)
    setTotal(res.result.total)
    setLoading(false)
    console.log(res)
  }
  useEffect(() => {
    getCollectionList()
  }, [selectedStatus, selectedDataSource, currPage, pageSize, dateRanges])
  const handleDownload = async () => {
    // Implement download logic here
    const param = {
      start_date: dayjs(dateRanges[0]).format('YYYY-MM-DD'),
      end_date: dayjs(dateRanges[1]).format('YYYY-MM-DD'),
      export: 'true',
      rating: selectedStatus || '',
      from_source: selectedDataSource || '',
    }
    const token = localStorage.getItem('console_token') || ''
    const queryParams = new URLSearchParams(param)
    const apiUrl = `${API_PREFIX}/data-conversation-overview?${queryParams}`
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
        operToken: localStorage.getItem('sso_token')!,
        Accept:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
    })

    if (response.ok) {
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${t('conversation.page.title')}.xlsx`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
    else {
      console.error('下载失败:', response.status)
    }
  }

  return (
    <div className={styles.container}>
      <h1>{t('conversation.page.title')}</h1>

      <div className={styles.filterSection}>
        <Space size="middle">
          <Select
            style={{ width: 200 }}
            placeholder={t('conversation.filter.source')}
            onChange={value => setSelectedDataSource(value)}
            allowClear
          >
            <Select.Option value="all">{t('conversation.options.all')}</Select.Option>
            <Select.Option value="market">{t('conversation.options.market')}</Select.Option>
            <Select.Option value="api">{t('conversation.options.api')}</Select.Option>
            <Select.Option value="web">{t('conversation.options.web')}</Select.Option>
            <Select.Option value="embed">{t('conversation.options.embed')}</Select.Option>
          </Select>

          <Select
            style={{ width: 200 }}
            placeholder={t('conversation.filter.rating')}
            onChange={value => setSelectedStatus(value)}
            allowClear
          >
            <Select.Option value="all">{t('conversation.options.all')}</Select.Option>
            <Select.Option value="like">{t('conversation.options.like')}</Select.Option>
            <Select.Option value="dislike">{t('conversation.options.dislike')}</Select.Option>
            <Select.Option value="like_and_not_dislike">
              {t('conversation.options.like_and_not_dislike')}
            </Select.Option>
            <Select.Option value="dislike_and_not_like">
              {t('conversation.options.dislike_and_not_like')}
            </Select.Option>
            <Select.Option value="not_like_and_not_dislike">
              {t('conversation.options.not_like_and_not_dislike')}
            </Select.Option>
          </Select>

          <RangePicker
            onChange={(dates) => {
              if (dates)
                setDateRange([dates[0]!, dates[1]!])
            }}
            value={dateRanges}
            style={{ width: 300 }}
          />

          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleDownload}
          >
            {t('conversation.action.download')}
          </Button>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        scroll={{ x: 'max-content' }}
        pagination={false}
        rowKey="agentId"
      />
      <Pagination
        className="mt-4 mb-4"
        total={total}
        current={currPage}
        pageSize={pageSize}
        showSizeChanger
        showTotal={total => t('conversation.pagination.total', { total })}
        pageSizeOptions={['10', '20', '50']}
        onChange={(page, pageSize) => {
          setCurrPage(page)
          setPageSize(pageSize)
        }}
        align="center"
      />
    </div>
  )
}
