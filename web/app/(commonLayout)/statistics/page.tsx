'use client'

// import { Metadata } from 'next'
import { Card } from 'antd'
import { useSearchParams } from 'next/navigation'
import { useState } from 'react'

import { useTranslation } from 'react-i18next'
import SmartServiceOverview from '@/app/components/statistics/SmartServiceOverview'
import SmartServiceOverviewDetails from '@/app/components/statistics/SmartServiceOverviewDetails'
import ModelTemplateOverview from '@/app/components/statistics/ModelTemplateOverview'
import ModelTemplateDetailsPersonal from '@/app/components/statistics/ModelTemplateDetailsPersonal'
import EvaluationData from '@/app/components/statistics/EvaluationData'
import WorkEvaluationData from '@/app/components/statistics/WorkEvaluationData'
import WorkEvaluationDataOverview from '@/app/components/statistics/WorkEvaluationDataOverview'
import SmartServiceDetails from '@/app/components/statistics/SmartServiceDetails'
import ModelTemplateDetails from '@/app/components/statistics/ModelTemplateDetails'
import CustomQuery from '@/app/components/statistics/CustomQuery'
import StatisticsTable from '@/app/components/statistics/common/index'
import SmartOverview from '@/app/components/statistics/SmartOverview'
import DataOverview from '@/app/components/statistics/DataOverview'
import { useAppContext } from '@/context/app-context'
// export const metadata: Metadata = {
//   title: '数据统计',
//   description: '数据统计和消耗详情',
// }

export default function StatisticsPage() {
  const { t } = useTranslation()
  const searchParams = useSearchParams()
  const view = searchParams.get('view')
  const [dateRange, setDateRange] = useState<[string, string]>(['', ''])
  const { canAdmin, isSuperUser } = useAppContext()

  const [customQueryData, setCustomQueryData] = useState<any>(null)
  const [table_list, setTableList] = useState<any>(null)
  const handleCustomQueryData = (data: any, list: any) => {
    setCustomQueryData(data)
    setTableList(list)
    console.log(list)
    // 可以在这里做其他处理
  }
  const renderContent = () => {
    if (view) {
      return <StatisticsTable dateRange={view} columnsList={customQueryData} tableList={table_list} />
    }
    else {
      return (
        <>
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-2xl font-bold">{t('statistics.title')}</h1>
          </div>
          <h3 className=" font-bold" style={{ margin: '0px 0px 10px 0px' }}>
            {t('statistics.overview')}
          </h3>
          {/* 模型类型tab */}
          {/* <Tabs
            className="mb-2"
            activeKey={activeTab}
            items={tabList}
            onChange={(value) => setActiveTab(value as any)}
          ></Tabs> */}
          {canAdmin && isSuperUser && (
            <div>
              <Card className="mb-4">
                <DataOverview dateRange={dateRange} />
              </Card>
              <Card className="mb-4">
                <SmartServiceOverview dateRange={dateRange} />
              </Card>
              <Card className="mb-4">
                <ModelTemplateOverview dateRange={dateRange} />
              </Card>
              <h3 className=" font-bold" style={{ margin: '0px 0px 10px 0px' }}>
                {t('statistics.details')}
              </h3>
              <Card className="mb-4">
                <EvaluationData dateRange={dateRange} />
              </Card>
              <Card className="mb-4">
                <WorkEvaluationData dateRange={dateRange} />
              </Card>
              <Card className="mb-4">
                <SmartServiceDetails dateRange={dateRange} />
              </Card>
              <Card className="mb-4">
                <ModelTemplateDetails dateRange={dateRange} />
              </Card>
              <h3 className=" font-bold" style={{ margin: '0px 0px 10px 0px' }}>
                {t('statistics.custom_query')}
              </h3>
              <Card>
                <CustomQuery onDataUpdate={handleCustomQueryData} />
              </Card>
            </div>
          )}
          {!isSuperUser && (
            <div>
              <SmartOverview />
              <Card className="mb-4">
                <SmartServiceOverviewDetails dateRange={dateRange} />
              </Card>
              <Card className="mb-4">
                <WorkEvaluationDataOverview dateRange={dateRange} />
              </Card>
              <Card className="mb-4">
                <ModelTemplateDetailsPersonal dateRange={dateRange} />
              </Card>
            </div>
          )}
        </>
      )
    }
  }

  return <div className="p-6">{renderContent()}</div>
}
