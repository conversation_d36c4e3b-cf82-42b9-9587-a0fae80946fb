'use client'

import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useMount } from 'ahooks'
import type { DataSet, DataSetListResponse } from '@/models/datasets'
import { fetchDatasets } from '@/service/datasets'
/* 公共组件 */
import CardList from '@/app/components/base/card-list'
import DatasetCard from '@/app/components/datasets/card'
import { APP_NAME } from '@/config'

// 获取知识库卡片接口参数
const getKey = (
  pageIndex: number,
  tags: string[],
  keyword: string,
) => {
  const params: any = {
    url: 'datasets',
    params: {
      page: pageIndex,
      limit: 30,
    },
  }
  if (tags.length)
    params.params.tag_ids = tags
  if (keyword)
    params.params.keyword = keyword
  return params
}

type Props = {
  tags: string[]
  keywords: string
}

const Datasets = forwardRef(({
  tags,
  keywords,
}: Props, ref) => {
  const { t } = useTranslation()
  // 是否正在加载
  const [loading, setLoading] = useState(true)
  // 分页信息
  const [pagination, setPagination] = useState({
    page: 1,
    total: 0,
  })
  // 卡片列表
  const [cardList, setCardList] = useState<Array<DataSet>>([])
  // 是否还有未加载的部分
  const hasMore = useMemo(() => {
    return pagination.total > (pagination.page * 30)
  }, [pagination])

  // 获取知识库卡片列表
  const fetchDatasetCard = useCallback(async (page: number) => {
    const param = getKey(page, tags, keywords)
    setLoading(true)
    await (fetchDatasets(param) as unknown as Promise<DataSetListResponse>).then((res) => {
      setCardList([...cardList, ...res.data])
      setPagination({
        page,
        total: res.total,
      })
    }).finally(() => {
      setLoading(false)
    })
  }, [cardList, keywords, tags])
  // 重置知识库卡片列表
  const resetDatasetCard = async (params?: { currentTag: string[]; currentKeyword: string }) => {
    const param = getKey(1, params ? params.currentTag : tags, params ? params.currentKeyword : keywords)
    await (fetchDatasets(param) as unknown as Promise<DataSetListResponse>).then((res) => {
      setCardList([...res.data])
      setPagination({
        page: 1,
        total: res.total,
      })
    }).finally(() => {
      setLoading(false)
    })
  }
  // 获取下一页
  const nextDatasetCard = async () => {
    if (hasMore) {
      setPagination({
        page: pagination.page + 1,
        total: pagination.total,
      })
      await fetchDatasetCard(pagination.page + 1)
    }
  }

  useImperativeHandle(ref, () => ({
    resetDatasetCard,
  }))
  useMount(() => {
    resetDatasetCard()
  })
  // 调整document信息
  useEffect(() => {
    if (typeof window !== 'undefined')
      document.title = `${t('dataset.title')} - ${APP_NAME}`
  }, [t])

  return (
    <CardList
      type='scroll'
      loadFunc={nextDatasetCard}
      loading={loading}
      layout='line'
      emptyText={t('dataset.notify.noDataset')}
      className='!shrink h-full px-8 py-6 overflow-auto'
    >
      {cardList.map(dataset => (
        <DatasetCard key={dataset.id} dataset={dataset} onSuccess={resetDatasetCard} />),
      )}
    </CardList>
  )
})

Datasets.displayName = 'Datasets'

export default Datasets
