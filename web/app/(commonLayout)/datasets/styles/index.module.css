.app-list-header {
  @apply flex items-center justify-between;
  padding: 34px 32px 0px;
  margin-bottom: 30px;
}
.header-title {
  @apply text-gray-G1 text-S7 leading-H6.25;
  font-weight: 600;
}
.app-list-filter {
  position: relative;
  margin: 0 32px;
  padding-bottom: 14px;
  border-bottom: 1px solid rgba(199, 195, 207, 0.6);
  display: flex;
  justify-content: flex-end;
}
/* .search-criteria {
  @apply flex items-center absolute right-0 bottom-[-16px];
} */
 .search-criteria {
  @apply flex items-center;
 }