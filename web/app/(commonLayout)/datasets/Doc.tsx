'use client'

import type { FC } from 'react'
import { useContext } from 'use-context-selector'
import cn from 'classnames'
import TemplateEn from './template/template.en.mdx'
import TemplateZh from './template/template.zh.mdx'
import I18n from '@/context/i18n'
import { LanguagesSupported } from '@/i18n/language'

type DocProps = {
  apiBaseUrl: string
}
const Doc: FC<DocProps> = ({
  apiBaseUrl,
}) => {
  const { locale } = useContext(I18n)
  return (
    <div className='p-8 h-full shrink! overflow-auto'>
      <article className={cn('cardWrap', 'prose py-8')}>
        {
          locale !== LanguagesSupported[1]
            ? <TemplateEn apiBaseUrl={apiBaseUrl} />
            : <TemplateZh apiBaseUrl={apiBaseUrl} />
        }
      </article>
    </div>

  )
}

export default Doc
