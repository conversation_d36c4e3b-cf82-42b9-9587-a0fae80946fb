'use client'
import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { useDatasetDetailContext } from '@/context/dataset-detail'
// 公共组件
import AppBasic from '@/app/components/app-detail/app-basic'
import TopNavbar from '@/app/components/layout/top-navbar'

export type IAppDetailLayoutProps = {
  children: React.ReactNode
  params: { datasetId: string }
}

const TabLayout: FC<IAppDetailLayoutProps> = (props) => {
  const {
    children,
    params: { datasetId },
  } = props
  const { t } = useTranslation()
  const router = useRouter()
  const { dataset } = useDatasetDetailContext()

  // 导航栏
  const navigation = [
    { name: t('dataset.datasetMenus.documents'), href: `/datasets/${datasetId}/documents` },
    { name: t('dataset.datasetMenus.hitTesting'), href: `/datasets/${datasetId}/hitTesting` },
    // { name: 'api & webhook', href: `/datasets/${datasetId}/api`, icon: CommandLineIcon, selectedIcon: CommandLineSolidIcon },
    { name: t('dataset.datasetMenus.settings'), href: `/datasets/${datasetId}/settings` },
  ]
  // embedding模型是否可用
  const embeddingAvailable = dataset?.embedding_available

  return (
    <>
      {/* 顶栏 */}
      <TopNavbar
        navigation={navigation}
        leftContent={
          <AppBasic
            name={dataset?.name || '--'}
            type={dataset?.description || '--'}
            tooltip={embeddingAvailable ? '' : t('dataset.notify.embeddingExpire')!}
          ></AppBasic>
        }
        backFunc={() => router.push('/datasets')}
      />
      <div className='h-[calc(100%-60px)]'>
        {children}
      </div>
    </>

  )
}
export default React.memo(TabLayout)
