.itemWrapper {
  @apply flex items-center w-full h-10 rounded-lg hover:bg-gray-50 cursor-pointer;
}
.appInfo {
  @apply truncate text-gray-700 text-sm font-normal;
}
.iconWrapper {
  @apply relative w-6 h-6 rounded-lg;
}
.statusPoint {
  @apply flex justify-center items-center absolute -right-0.5 -bottom-0.5 w-2.5 h-2.5 bg-white rounded;
}
.subTitle {
  @apply uppercase text-xs text-gray-500 font-semibold px-3 pb-2 pt-4;
}
.emptyIconDiv {
  @apply h-7 w-7 bg-gray-50 border border-[#EAECF5] inline-flex justify-center items-center rounded-lg;
}
