'use client'
import type { FC } from 'react'
import React, { useEffect } from 'react'
import useSWR from 'swr'
import { fetchDatasetDetail } from '@/service/datasets'
import DatasetDetailContext from '@/context/dataset-detail'
// 公共组件
import Loading from '@/app/components/base/loading'
import { APP_NAME } from '@/config'

export type IAppDetailLayoutProps = {
  children: React.ReactNode
  params: { datasetId: string }
}

const DatasetDetailLayout: FC<IAppDetailLayoutProps> = (props) => {
  const {
    children,
    params: { datasetId },
  } = props
  // 当前知识库数据
  const { data: datasetRes, error, mutate: mutateDatasetRes } = useSWR({
    url: 'fetchDatasetDetail',
    datasetId,
  }, apiParams => fetchDatasetDetail(apiParams.datasetId))

  useEffect(() => {
    if (datasetRes)
      document.title = `${datasetRes.name || 'Dataset'} - ${APP_NAME}`
  }, [datasetRes])

  if (!datasetRes || error)
    return <Loading />

  return (
    <div className='overflow-hidden flex flex-col h-full w-full'>
      <DatasetDetailContext.Provider value={{
        indexingTechnique: datasetRes?.indexing_technique,
        dataset: datasetRes,
        mutateDatasetRes: () => mutateDatasetRes(),
      }}>
        {children}
      </DatasetDetailContext.Provider>
    </div>

  )
}
export default React.memo(DatasetDetailLayout)
