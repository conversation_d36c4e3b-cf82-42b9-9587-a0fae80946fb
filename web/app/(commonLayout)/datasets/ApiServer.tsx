'use client'

import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import style from './styles/api.module.css'
import SecretKeyButton from '@/app/components/develop/secret-key/secret-key-button'
import SecrectKeyExhibition from '@/app/components/develop/secret-key/secret-key-exhibition'

type ApiServerProps = {
  apiBaseUrl: string
}
const ApiServer: FC<ApiServerProps> = ({
  apiBaseUrl,
}) => {
  const { t } = useTranslation()

  return (
    <div className='flex gap-3 items-center flex-wrap'>
      <div className='flex items-center px-2 py-[6px] h-[36px] text-xs text-[#2FBD81] leading-[20px] rounded border border-[#2FBD81]'>
        <span className={cn(style.dot, '!bg-[#2FBD81]')}></span>
        {t('appApi.ok')}
      </div>
      <SecrectKeyExhibition value={apiBaseUrl}>
        <div className='mr-2 px-[8px] py-[6px] h-[24px] rounded-sm border !border-gray-G5 bg-gray-G6 leading-[12px] text-gray-G2'>{t('appApi.apiServer')}</div>
      </SecrectKeyExhibition>
      <SecretKeyButton variant={'secondary-accent'} className='!bg-transparent'/>
    </div>
  )
}

export default ApiServer
