.app-list-header {
  @apply flex items-center justify-between;
  padding: 34px 32px 0px;
  margin-bottom: 30px;
}
.header-title {
  @apply text-gray-G1 text-S7 leading-H6.25;
  font-weight: 600;
}
.app-list-filter {
  position: relative;
  margin: 0 32px;
  display: flex;
  justify-content: space-between;  
}
.search-criteria {
  @apply flex items-center shrink-0 pb-[14px] border-b;
  border-color: rgba(190, 195, 207, 0.6);
  padding-left: 100px;
}
.search-tab {
  @apply grow shrink overflow-hidden;
  padding-top: 21px;
}
.card-list {
  @apply h-full px-8 py-6;
  overflow: auto;
}