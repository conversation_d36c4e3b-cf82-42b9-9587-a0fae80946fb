'use client'

import { useC<PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { ConfigProvider, Tabs } from 'antd'
import { useMount } from 'ahooks'

/* 公共钩子及配置 */
import useAppsQueryState from './hooks/useAppsQueryState'
import s from './styles/index.module.css'
import type { AppListResponse } from '@/models/app'
import { type App, AppEventEmitterType } from '@/types/app'
import { fetchAppList } from '@/service/apps'
import { useAppContext } from '@/context/app-context'
import { useTabSearchParams } from '@/hooks/use-tab-searchparams'
import { GRAY } from '@/themes/var-define'
import { useProviderContext } from '@/context/provider-context'
import { useModalContext } from '@/context/modal-context'
import { useEventEmitterContextContext } from '@/context/event-emitter'
/* 公共组件 */
import SearchInput from '@/app/components/base/input/search-input'
import AppCard from '@/app/components/app/common/card'
import CardList from '@/app/components/base/card-list'
import Button from '@/app/components/base/button'
import Toast from '@/app/components/base/toast'
import { APP_NAME } from '@/config'

// 生成获取应用列表接口参数
const getKey = (
  pageIndex: number,
  activeTab: string,
  tags: string[],
  keywords: string,
) => {
  const params: any = { url: 'apps', params: { page: pageIndex, limit: 30, name: keywords } }

  if (activeTab !== 'all')
    params.params.mode = activeTab
  else
    delete params.params.mode

  if (tags.length)
    params.params.tag_ids = tags

  return params
}

const Apps = () => {
  const { t } = useTranslation()
  const { onPlanInfoChanged, plan } = useProviderContext()
  const { setShowCreateAppModal } = useModalContext()
  const { eventEmitter } = useEventEmitterContextContext()
  const router = useRouter()
  const isAppsFull = plan.usage.buildApps >= plan.total.buildApps
  const { canCreateApp, canViewApp } = useAppContext()

  // 当前激活tab
  const [activeTab, setActiveTab] = useTabSearchParams({
    defaultTab: 'all',
  })
  // 是否正在加载
  const [loading, setLoading] = useState(true)
  // 分页信息
  const [pagination, setPagination] = useState({
    page: 1,
    total: 0,
  })
  // 卡片列表
  const [cardList, setCardList] = useState<Array<App>>([])
  // 路由query参数
  const { query: { tagIDs = [], keywords = '' }, setQuery } = useAppsQueryState()

  // 应用页面tab列表
  const tabList = [{
    key: 'all',
    label: t('app.types.all'),
  }, {
    key: 'agent-chat',
    label: t('app.newApp.basic'),
  }, {
    key: 'chat',
    label: t('app.newApp.advanced'),
  }]
  const cardListRef = useRef({
    scrollTop: () => {},
  })
  // 是否还有未加载的部分
  const hasMore = useMemo(() => {
    return pagination.total > (pagination.page * 30)
  }, [pagination])

  // 获取应用列表接口
  const fetchAppCard = useCallback(async (type: 'normal' | 'clear' = 'normal', params?: any) => {
    const param = params || getKey(pagination.page, activeTab, tagIDs, keywords)
    setLoading(true)
    await (fetchAppList(param) as unknown as Promise<AppListResponse>).then((res) => {
      setCardList(type === 'normal' ? [...cardList, ...res.data] : [...res.data])
      setPagination({
        page: param.params.page,
        total: res.total,
      })
    }).finally(() => {
      setLoading(false)
    })
  }, [activeTab, cardList, keywords, pagination.page, tagIDs])
  // 重置应用列表
  const resetAppCard = useCallback((
    tab = activeTab,
    tags = tagIDs,
    name = keywords,
  ) => {
    const param = getKey(1, tab, tags, name)
    fetchAppCard('clear', param)
  }, [activeTab, fetchAppCard, keywords, tagIDs])
  // 获取下一页
  const nextAppCard = async () => {
    if (hasMore) {
      setPagination({
        page: pagination.page + 1,
        total: pagination.total,
      })
      await fetchAppCard('normal', getKey(pagination.page + 1, activeTab, tagIDs, keywords))
    }
  }
  // 变更关键字方法
  const changeKeyWord = (value: string) => {
    setQuery(prev => ({ ...prev, keywords: value }))
    resetAppCard(activeTab, tagIDs, value)
  }
  // 变更tab方法
  const changeTab = (value: string) => {
    cardListRef.current?.scrollTop()
    setActiveTab(value)
    resetAppCard(value, tagIDs, keywords)
  }
  // 创建应用
  const createApp = () => {
    if (isAppsFull) {
      Toast.notify({
        type: 'error',
        message: t('app.notify.appFull', { num: plan.total.buildApps }),
      })
    }
    else {
      setShowCreateAppModal({
        onClose: () => {},
        onSuccess: () => {
          resetAppCard()
          onPlanInfoChanged()
        },
      })
    }
  }

  useMount(() => {
    resetAppCard()
  })

  useEffect(() => {
    if (typeof window !== 'undefined')
      document.title = `${t('app.title')} - ${APP_NAME}`
    if (!canViewApp)
      return router.replace('/square')
  }, [canViewApp, router, t])

  eventEmitter?.useSubscription((v: any) => {
    if (v.type === AppEventEmitterType.Refresh)
      resetAppCard()
  })

  return (
    <>
      {/* 头部信息 */}
      <div className={s['app-list-header']}>
        <span className={s['header-title']}>{t('app.title')}</span>
        {canCreateApp && (
          <Button variant={'primary'} onClick={() => createApp()}>
            {t('app.action.createApp')}
          </Button>
        )}
      </div>
      {/* 筛选条件 */}
      <div className={s['app-list-filter']}>
        <Tabs
          activeKey={activeTab}
          items={tabList}
          onChange={changeTab}
          className={s['search-tab']}
        ></Tabs>
        <div className={s['search-criteria']}>
          <ConfigProvider
            theme={{
              components: {
                Input: {
                  colorBgContainer: 'transparent',
                  colorBorder: GRAY['G3-5'],
                },
              },
            }}
          >
            <SearchInput value={keywords} className='w-[220px]' onChange={changeKeyWord} />
          </ConfigProvider>
        </div>
      </div>
      {/* 卡片列表 */}
      <CardList
        ref={cardListRef}
        loading={loading}
        emptyText={t('app.notify.noApp')}
        loadFunc={nextAppCard}
        type='scroll'
        layout='line'
        className={s['card-list']}
      >
        {cardList?.map(app => (
          <AppCard key={app.id} app={app} onRefresh={resetAppCard} />
        ))}
      </CardList>
    </>
  )
}

export default Apps
