'use client'
import { useTranslation } from 'react-i18next'
import React, { useMemo } from 'react'

import { useStore as useAppStore } from '@/app/components/app/store'

import TabMainContainer from '@/app/components/base/tab-main-container'
import AppPublishConfig from '@/app/components/app/app-publish-config'
import AppPublishHistory from '@/app/components/app/app-publish-history'
import ToolPublishHistory from '@/app/components/tools/tool-publish-history'
import ToolPublishConfig from '@/app/components/tools/app-publish-config'

const enum TabType {
  config = 'config',
  history = 'history',
}

const Publish = () => {
  const { t } = useTranslation()
  const appDetail = useAppStore(state => state.appDetail!)

  // 是否未工作流工具
  const isWorkflowTool = useMemo(() => {
    return appDetail.mode === 'workflow'
  }, [appDetail.mode])
  // tab选项
  const options = useMemo(() => {
    return [
      { key: 'config', label: t('app.publish.config'), tabChildren: (!isWorkflowTool ? <AppPublishConfig appId={appDetail.id} /> : <ToolPublishConfig appId={appDetail.id} />) },
      { key: 'history', label: t('app.publish.history'), tabChildren: (!isWorkflowTool ? <AppPublishHistory appId={appDetail.id} appDetail={appDetail} /> : <ToolPublishHistory appId={appDetail.id} appDetail={appDetail} />) },
    ]
  }, [appDetail, isWorkflowTool, t])
  return (
    <TabMainContainer options={options} defaultTab={TabType.config} />
  )
}

export default Publish
