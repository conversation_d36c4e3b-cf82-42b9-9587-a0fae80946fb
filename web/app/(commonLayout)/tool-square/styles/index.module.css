.app-square-list-header {
  padding: 36px 32px 0px;
}
.header-title {
  @apply text-gray-G1 text-S7 leading-H6.25;
  font-weight: 600;
}
.banner {
  @apply lg:!h-[257px];
  @apply h-[170px];
  @apply mt-[36px] mx-8;
}
.app-square-list-filter {
  position: relative;
  margin: 32px 32px 0px;
  display: flex;
  justify-content: space-between;  
}
.search-criteria {
  @apply flex items-center shrink-0 pb-[14px] border-b;
  border-color: rgba(190, 195, 207, 0.6);
  padding-left: 100px;
}
.search-tab {
  @apply grow shrink overflow-hidden;
  padding-top: 21px;
}
.base-card-list {
  @apply lg:!min-h-[432px];
  @apply min-h-[345px];
  @apply !shrink px-8 py-6;
  @apply flex items-center;
}
.card-list {
  @apply !grid content-start grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 grow shrink-0 overflow-x-hidden relative;
}
.fix-content {
  background: url(/assets/background/background.png);
  width: calc(100% - 240px);
}