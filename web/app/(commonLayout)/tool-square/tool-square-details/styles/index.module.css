/* 工具列表头部 */
.tool-list-header {
  @apply flex items-center justify-between;
  padding: 34px 32px 0px;
  margin-bottom: 50px;
}
/* 工具列表筛选条件 */
.tool-list-criteria {
  position: relative;
  margin: 0 32px;
} 
/* 工具列表内容 */
.tool-list-content {
  @apply flex w-full grow h-0 relative;
}

/* 页面标题 */
.header-title {
  @apply text-gray-G1 text-S7 leading-H6.25;
  font-weight: 600;
}
/* 搜索条件右侧 */
.search-criteria-right {
  @apply flex items-center absolute right-0 bottom-[14px];
}
/* 卡片列表 */
.card-list {
  @apply !shrink h-full;
  padding: 24px 32px;
  overflow: auto;
}
/* 工具集合详情 */
.detail-drawer {
  @apply bg-gray-G8 shrink-0 w-0 overflow-y-auto transition-all duration-200 ease-in-out;
}
/* 工具集合关闭按钮 */
.detail-drawer-close {
  @apply absolute top-[28px] right-5 cursor-pointer;
}

.empty {
  @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
}
.fix-content {
  background: url(/assets/background/background.png);
  width: calc(100% - 240px);
}
.appTopbarSquare{
  @apply shrink-0 flex items-center w-full h-[60px] px-6 gap-4 border-gray-G6 !border-none;
  padding: 40px 32px 0px 32px;
  background: unset !important;
}