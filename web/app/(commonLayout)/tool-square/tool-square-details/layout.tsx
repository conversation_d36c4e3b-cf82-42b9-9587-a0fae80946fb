'use client'
import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter, useSearchParams } from 'next/navigation'
// import { useTeamContext } from '@/app/components/account-setting/team-page/team-context'
import s from './styles/index.module.css'
// 公共组件
// import AppBasicVariant from '@/app/components/app-detail/app-basic'
import AppBasicVariant from '@/app/components/app-detail/app-basic-variant'
import TopNavbar from '@/app/components/layout/top-navbar'
const i18nPrefix = 'toolMarket'

export type IAppDetailLayoutProps = {
  children: React.ReactNode
  params: { datasetId: string }
}
const TabLayout: FC<IAppDetailLayoutProps> = (props) => {
  const {
    children,
    params: { datasetId },
  } = props
  const { t } = useTranslation()
  const router = useRouter()
  const searchParams = useSearchParams()
  const team_name = searchParams.get('team_name')
  const title = t(`${i18nPrefix}.details.title`)
  // const { teamDataObj } = useTeamContext()

  return (
    <>
      {/* 顶栏 */}
      <TopNavbar
        navigation={[]}
        leftContent={<AppBasicVariant name={title || '--'}></AppBasicVariant>}
        backFunc={() => router.push('/tool-square')}
        className={s.appTopbarSquare}
      />
      <div className="h-[calc(100%-60px)]">{children}</div>
    </>
  )
}
export default React.memo(TabLayout)
