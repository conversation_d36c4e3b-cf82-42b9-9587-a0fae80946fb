'use client'
import { Confi<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, List, } from 'antd'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { useMount } from 'ahooks'
import s from './styles/index.module.css'
import cn from '@/utils/classnames'
import { GRAY } from '@/themes/var-define'
import type { AppMarket } from '@/types/app-market'
import { AppMarketFeature, AppMarketSort } from '@/types/app-market'
import { fetchMarketAppspublic, fetchMarketCategories } from '@/service/market'
import { useAppContext } from '@/context/app-context'
import ToolSquareDetail from '@/app/components/tool-square/details'
/* 公共组件 */
import Banner from '@/app/components/app-square/banner'
import SearchInput from '@/app/components/base/input/search-input'
// import AppSquareCard from '@/app/components/app-square/card'
import ToolSquareCard from '@/app/components/tool-square/card'
import IntersectScrollbar from '@/app/components/base/scrollbar/intersect-scrollbar'
import Empty from '@/app/components/base/empty'
import Select from '@/app/components/base/select'
import { APP_NAME } from '@/config'

import TextButton from '@/app/components/base/button/text-button'
import { ArrowLeft } from '@/app/components/base/icons/src/vender/line/arrows'
import Avatar from '@/app/components/base/avatar'

const i18nPrefix = 'toolMarket'

// 生成获取应用广场列表接口参数
const getKey = (
  pageIndex: number,
  activeTab: string,
  sort: AppMarketSort,
  keywords: string,
) => {
  const params: any = { page: pageIndex, limit: 30, name: keywords, sort_by: sort, featured: AppMarketFeature.Normal }

  if (activeTab !== 'all')
    params.category_id = activeTab
  else
    delete params.category_id
  return params
}

const ToolSquare = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { isCurrentWorkspaceDatasetOperator } = useAppContext()

  

  return (
    <>
      <ToolSquareDetail/>
    </>
  )
}

export default ToolSquare
