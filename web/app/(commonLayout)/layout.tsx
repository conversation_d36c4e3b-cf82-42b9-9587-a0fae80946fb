import React from 'react'
import type { ReactNode } from 'react'
import BasicLayout from '../components/layout'
import SwrInitor from '@/app/components/swr-initor'
import { AppContextProvider } from '@/context/app-context'
import { EventEmitterContextProvider } from '@/context/event-emitter'
import { ProviderContextProvider } from '@/context/provider-context'
import { ModalContextProvider } from '@/context/modal-context'
import LayoutContextProvider from '@/context/layout-context'
import { APP_NAME } from '@/config'
import { ConfigContextProvider } from '@/context/config-context'

const Layout = ({ children }: { children: ReactNode }) => {
  return (
    <SwrInitor>
      <AppContextProvider>
        <EventEmitterContextProvider>
          <ProviderContextProvider>
            <ConfigContextProvider>
              <ModalContextProvider>
                <LayoutContextProvider>
                  <BasicLayout>{ children }</BasicLayout>
                </LayoutContextProvider>
              </ModalContextProvider>
            </ConfigContextProvider>
          </ProviderContextProvider>
        </EventEmitterContextProvider>
      </AppContextProvider>
    </SwrInitor>
  )
}

export const metadata = {
  title: APP_NAME,
}

export default Layout
