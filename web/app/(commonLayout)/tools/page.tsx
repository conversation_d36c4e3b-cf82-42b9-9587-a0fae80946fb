'use client'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { ConfigProvider, Tabs } from 'antd'
import { useContext } from 'use-context-selector'
import s from './styles/index.module.css'
import {
  deleteWorkflowTool,
  fetchCollectionList,
  removeCustomCollection, removeMCPCollection,
} from '@/service/tools'
import { useTabSearchParams } from '@/hooks/use-tab-searchparams'
import { GRAY } from '@/themes/var-define'
import { useProviderContext } from '@/context/provider-context'
import { getLanguage } from '@/i18n/language'
import I18n from '@/context/i18n'
import { APP_NAME } from '@/config'

import { CollectionType } from '@/types/tools'
import type { Collection, WorkflowCollectionDetail } from '@/types/tools'
import type { WorkflowToolProviderRequest } from '@/types/api/tools'
import ConfigCredential from '@/app/components/tools/detail/build-in/config-credentials'
import ToolDetailModal from '@/app/components/tools/detail/tool-detail-modal'
import CreateToolModal from '@/app/components/tools/create-tool-modal'
import WorkflowToolModal from '@/app/components/tools/detail/workflow-tool'
import {
  getCollectionDetail,
  isCustomTool,
  isMCPTool,
  isOpenapiShemaTool,
  isWorkflowTool,
} from '@/app/components/tools/utils'
import ReleaseWorkflowModal from '@/app/components/tools/detail/workflow-tool/release'

/* 公共组件 */
import { useAppContext } from '@/context/app-context'
import cn from '@/utils/classnames'
import SearchInput from '@/app/components/base/input/search-input'
import CardList from '@/app/components/base/card-list'
import ProviderCard from '@/app/components/tools/common/card'
import EditCustomToolModal from '@/app/components/tools/detail/custom-tool'
import MCPToolModal from '@/app/components/tools/detail/mcp-tool'
import Button from '@/app/components/base/button'
import Toast from '@/app/components/base/toast'
import Confirm from '@/app/components/base/confirm'
import { useLayoutContext } from '@/context/layout-context'
import { usePrivateSystemContext } from '@/context/private-context'

const Tools = () => {
  const { t } = useTranslation()
  // 是否为应用解耦——私有化
  const { isAppDecoupled } = usePrivateSystemContext()
  const router = useRouter()
  const { canViewApp, canEditApp } = useAppContext()
  const { locale } = useContext(I18n)
  const language = getLanguage(locale)
  const { setAppLoading } = useLayoutContext()
  const { plan, onPlanInfoChanged } = useProviderContext()
  const isToolFull = plan.usage.buildTools >= plan.total.buildTools
  // 当前激活tab
  const [activeTab, setActiveTab] = useTabSearchParams({
    // defaultTab: CollectionType.builtIn,
    defaultTab: CollectionType.all,
  })
  // 关键字
  const [keywords, setKeywords] = useState<string>('')
  // 工具集列表
  const [collectionList, setCollectionList] = useState<Collection[]>([])
  // 当前选中工具集
  const [currentCollection, setCurrentCollection] = useState<Collection>()
  // 是否显示自定义工具集弹窗
  const [showCustomCollectionModal, setShowCustomCollectionModal] = useState(false)
  // 是否显示创建MCP弹窗
  const [showMCPToolModal, setShowMCPToolModal] = useState(false)
  // 是否显示工具集详情弹窗
  const [showCollectionDetailModal, setShowCollectionDetailModal]
    = useState(false)
  // 是否显示删除弹窗
  const [showConfirmDelete, setShowConfirmDelete] = useState(false)
  // 是否显示授权弹窗
  const [showAuthModal, setShowAuthModal] = useState(false)
  // 加载中
  const [loading, setLoading] = useState(true)

  // 编辑工作流
  const [showEditWorkflowTool, setShowEditWorkflowTool] = useState(false)
  // 发布
  const [showRelease, setShowRelease] = useState(false)
  // 更新发布
  const [isUpdateRelease, setIsUpdateRelease] = useState(false)
  // 是否创建工具集弹窗
  const [showCollectionModal, setShowCollectionModal] = useState(false)

  // 搜索条件tab列表
  const tabList = useMemo(() => {
    if (isAppDecoupled) {
      return [
        { key: CollectionType.all, label: t('tools.type.all') },
        { key: CollectionType.openapi_shema, label: t('tools.type.openAPISchema') },
      ]
    }
    return [
      { key: CollectionType.all, label: t('tools.type.all') },
      { key: CollectionType.workflow, label: t('tools.type.workflow') },
      { key: CollectionType.openapi_shema, label: t('tools.type.openAPISchema') },
      { key: CollectionType.mcp, label: t('tools.type.mcp') },
    ]
  }, [isAppDecoupled, t])

  // 过滤后的工具集列表
  const filteredCollectionList = useMemo(() => {
    return collectionList.filter((collection) => {
      let filtered = true
      if (keywords) {
        filtered
          = collection.name.toLowerCase().includes(keywords.toLowerCase())
          || collection.label[language]
            .toLowerCase()
            .includes(keywords.toLowerCase())
      }
      // 旧 勿删
      // if (filtered && activeTab !== CollectionType.all) {
      //   if (activeTab === CollectionType.builtIn)
      //     filtered = collection.type === CollectionType.builtIn
      //   else
      //     filtered = collection.type !== CollectionType.builtIn
      // }
      // 新
      if (filtered && activeTab !== CollectionType.all) {
        if (activeTab === CollectionType.workflow)
          filtered = collection.type === CollectionType.workflow
        // OpenAPI schema TODO
        else if (activeTab === CollectionType.openapi_shema)
          filtered = collection.type === CollectionType.openapi_shema
        else if (activeTab === CollectionType.mcp)
          filtered = collection.type === CollectionType.mcp
      }
      // else if (filtered && activeTab === CollectionType.all && isAppDecoupled) {
      //   // 全部只展示 openapi_shema
      //   filtered = collection.type === CollectionType.openapi_shema
      // }
      else if (filtered && activeTab === CollectionType.all) {
        // 全部，不展示内置工具
        filtered = collection.type !== CollectionType.builtIn
      }
      return filtered
    })
  }, [collectionList, keywords, language, activeTab])

  // 获取工具集列表
  const getCollectionList = useCallback(async () => {
    setLoading(true)
    const list = await fetchCollectionList()
    setLoading(false)
    setCollectionList([...list])
  }, [])
  // 完成变更自定义工具
  const handleCustomCollection = async () => {
    getCollectionList()
    setShowCustomCollectionModal(false)
  }
  // 完成变更MCP工具
  const handleMCPCollection = async () => {
    getCollectionList()
    setShowMCPToolModal(false)
  }
  // 完成创建工具
  const handleCreateCollection = useCallback(
    async (
      data?: WorkflowToolProviderRequest &
      Partial<{
        workflow_app_id: string
        workflow_tool_id: string
      }>,
    ) => {
      getCollectionList()
      if (data?.workflow_app_id)
        router.push(`/app/${data.workflow_app_id}/workflow`)
    },
    [],
  )
  // 创建工具集
  const createCollection = async () => {
    if (isToolFull) {
      Toast.notify({
        type: 'error',
        message: t('tools.notify.toolFull', { num: plan.total.buildTools }),
      })
    }
    else {
      setShowCollectionModal(true)
    }
  }
  // 删除自定义工具
  const deleteCustomCollection = async () => {
    await removeCustomCollection(currentCollection!.name)
    getCollectionList()
    Toast.notify({
      type: 'success',
      message: t('tools.notify.successDeleteCustom'),
    })
    onPlanInfoChanged()
  }
  // 删除工作流工具
  const deleteWorkflowCollection = async () => {
    await deleteWorkflowTool(currentCollection!.id)
    getCollectionList()
    Toast.notify({
      type: 'success',
      message: t('tools.notify.successDeleteWorkflow'),
    })
    onPlanInfoChanged()
  }
  // 删除MCP工具
  const deleteMCPCollection = async () => {
    await removeMCPCollection(currentCollection!.name)
    getCollectionList()
    Toast.notify({
      type: 'success',
      message: t('tools.notify.successDeleteMCP'),
    })
    onPlanInfoChanged()
  }
  // 删除工具
  const handleConfirmDelete = async () => {
    if (isCustomTool(currentCollection!))
      await deleteCustomCollection()
    if (isWorkflowTool(currentCollection!))
      await deleteWorkflowCollection()
    if (isMCPTool(currentCollection!))
      await deleteMCPCollection()
    setShowConfirmDelete(false)
  }
  // 完成工具授权、移除
  const handleToolAuth = async () => {
    getCollectionList()
    setShowAuthModal(false)
  }

  // 监听授权变化
  const onChangeAuth = async (collection: Collection) => {
    setCurrentCollection(collection)
    setShowAuthModal(true)
  }
  // 监听编辑工具
  const onEditTool = async (collection: Collection) => {
    setCurrentCollection(collection)
    if (isCustomTool(collection))
      setShowCustomCollectionModal(true)
    if (isWorkflowTool(collection)) {
      setCurrentCollection(collection)
      setShowEditWorkflowTool(true)
    }
    if (isMCPTool(collection))
      setShowMCPToolModal(true)
  }
  // 监听删除工具
  const onDeleteTool = async (collection: Collection) => {
    setCurrentCollection(collection)
    setShowConfirmDelete(true)
  }
  // 详情
  const onDetails = async (collection: Collection) => {
    setCurrentCollection(collection)
    setShowCollectionDetailModal(true)
  }
  // 发布
  const onRelease = async (collection: Collection) => {
    setCurrentCollection(collection)
    setShowRelease(true)
    setIsUpdateRelease(false)
  }
  // 更新发布
  const onUpdateRelease = async (collection: Collection) => {
    setCurrentCollection(collection)
    // TODO：显示发布弹窗
    setShowRelease(true)
    // setShowUpdateRelease(true)
    setIsUpdateRelease(true)
  }
  // 监听卡片点击
  const onClickCard = async (collection: Collection) => {
    if (isOpenapiShemaTool(collection) || isMCPTool(collection))
      return false
    if (isWorkflowTool(collection)) {
      if (isWorkflowTool(collection)) {
        setAppLoading(true)
        try {
          const payload = await getCollectionDetail(collection)
          setAppLoading(false)
          window.open(
            `/app/${
              (payload as WorkflowCollectionDetail).workflow_app_id
            }/workflow`,
            '_blank',
            'noreferrer',
          )
        }
        catch (e) {
          setAppLoading(false)
        }
      }
    }
    else {
      // 详情
      onDetails(collection)
    }
  }

  useEffect(() => {
    getCollectionList()
  }, [getCollectionList])
  useEffect(() => {
    if (typeof window !== 'undefined')
      document.title = `${t('tools.title')} - ${APP_NAME}`
    if (!canViewApp)
      return router.replace('/square')
  }, [canViewApp, router, t])

  return (
    <>
      {/* 工具列表头部 */}
      <div className={s['tool-list-header']}>
        <span className={s['header-title']}>{t('tools.title')}</span>
        {/* {canEditApp && (
          <Dropdown
            items={createDropdown}
            placement="bottomRight"
            onSelect={({ value }) => createCollection(value)}
            renderTrigger={(open) => (
              <Button variant={"primary"}>
                {t("tools.action.createCollection")}
                <ArrowDown className={open ? "rotate-180" : ""}></ArrowDown>
              </Button>
            )}
          ></Dropdown>
        )} */}
        { canEditApp && <Button
          variant={'primary'}
          onClick={createCollection}
        >
          {t('tools.action.createCollection')}
        </Button> }
      </div>
      {/* 工具筛选条件 */}
      <div className={s['tool-list-criteria']}>
        <Tabs
          activeKey={activeTab}
          items={tabList}
          onChange={value => setActiveTab(value as CollectionType)}
        ></Tabs>
        <div className={s['search-criteria-right']}>
          <ConfigProvider
            theme={{
              components: {
                Input: {
                  colorBgContainer: 'transparent',
                  colorBorder: GRAY['G3-5'],
                },
              },
            }}
          >
            <SearchInput className="w-[220px]" onChange={setKeywords} />
          </ConfigProvider>
        </div>
      </div>
      {/* 工具列表 */}
      <CardList
        loading={loading}
        className={cn(s['card-list'])}
        layout="line"
        emptyText={t('tools.notify.noTool')}
      >
        {filteredCollectionList.map(collection => (
          <ProviderCard
            type={collection.type}
            onSelect={() => onClickCard(collection)}
            onAuth={() => onChangeAuth(collection)}
            onEdit={() => onEditTool(collection)}
            onDelete={() => onDeleteTool(collection)}
            onDetails={() => onDetails(collection)}
            key={collection.id}
            collection={collection}
            onRelease={() => onRelease(collection)}
            onUpdateRelease={() => onUpdateRelease(collection)}
          />
        ))}
      </CardList>

      {/* 工具详情弹窗 */}
      {showCollectionDetailModal && (
        <ToolDetailModal
          collection={currentCollection!}
          onCancel={() => setShowCollectionDetailModal(false)}
        ></ToolDetailModal>
      )}
      {/* 授权弹窗 */}
      {showAuthModal && (
        <ConfigCredential
          collection={currentCollection!}
          onCancel={() => setShowAuthModal(false)}
          onSaved={handleToolAuth}
        />
      )}
      {/* 删除工具弹窗 */}
      {showConfirmDelete && (
        <Confirm
          title={t('tools.notify.deleteToolConfirmTitle')}
          content={t('tools.notify.deleteToolConfirmContent')}
          isShow={showConfirmDelete}
          onConfirm={handleConfirmDelete}
          onCancel={() => setShowConfirmDelete(false)}
        />
      )}

      {/* 编辑自定义工具弹窗 */}
      {showCustomCollectionModal && (
        <EditCustomToolModal
          collection={currentCollection}
          onHide={() => setShowCustomCollectionModal(false)}
          onAdd={handleCustomCollection}
          onEdit={handleCustomCollection}
        />
      )}
      {/* 编辑工作流弹窗 */}
      {showEditWorkflowTool && (
        <WorkflowToolModal
          collection={currentCollection}
          onHide={() => {
            setShowEditWorkflowTool(false)
          }}
          onAfterSave={async () => {
            getCollectionList()
            setShowEditWorkflowTool(false)
          }}
        />
      )}
      {/* 编辑MCP弹窗 */}
      {showMCPToolModal && (
        <MCPToolModal
          collection={currentCollection}
          onHide={() => setShowMCPToolModal(false)}
          onAdd={handleMCPCollection}
          onEdit={handleMCPCollection}
        />
      )}
      {/* TODO: 发布弹窗 */}
      {showRelease && (
        <ReleaseWorkflowModal
          collection={currentCollection}
          onHide={() => setShowRelease(false)}
          onSave={() => {
            setShowRelease(false)
            getCollectionList()
          }}
          isUpdateRelease={isUpdateRelease}
        />
      )}
      {/* 创建 */}
      {showCollectionModal && (
        <CreateToolModal
          collection={currentCollection}
          onHide={() => setShowCollectionModal(false)}
          onCreate={handleCreateCollection}
        />
      )}
    </>
  )
}
export default Tools
