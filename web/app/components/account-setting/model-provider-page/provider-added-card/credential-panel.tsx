import { type FC } from 'react'
import { Divider } from 'antd'
import { useTranslation } from 'react-i18next'
import type { ModelItem, ModelProvider } from '../declarations'
import {
  CustomConfigurationStatusEnum,
  EventEmitterType,
} from '../declarations'
import { useEventEmitterContextContext } from '@/context/event-emitter'
import { useAppContext } from '@/context/app-context'
// 公共组件
import Tooltip from '@/app/components/base/tooltip'
import Indicator from '@/app/components/base/indicator'
import TextButton from '@/app/components/base/button/text-button'
import { Delete, Setting } from '@/app/components/base/icons/src/vender/line/action'
import { Clear } from '@/app/components/base/icons/src/vender/line/general'

type CredentialPanelProps = {
  model?: ModelItem
  provider: ModelProvider
  personalTool?: boolean
}
const CredentialPanel: FC<CredentialPanelProps> = ({
  provider,
  personalTool,
  model,
}) => {
  const { t } = useTranslation()
  const { canAdmin } = useAppContext()
  const { eventEmitter } = useEventEmitterContextContext()
  const customConfig = provider.custom_configuration
  const customConfiged = customConfig.status === CustomConfigurationStatusEnum.active
  const customConfigfg = model?.active
  // 设置模型供应商
  const setUpProvider = () => {
    eventEmitter?.emit({
      type: EventEmitterType.setUpProvider,
      provider,
      model,
    } as any)
  }
  // 删除模型供应商
  const deleteProvider = () => {
    eventEmitter?.emit({
      type: EventEmitterType.deleteProvider,
      provider,
      model,
    } as any)
  }

  return (
    <div className='relative px-2 py-1 rounded border border-gray-G5 inline-flex items-center'>
      {
        (provider.provider_credential_schema || personalTool) && <>
          <div className='text-S1 leading-H1 text-gray-G2 mr-1'>{personalTool ? 'APP-KEY' : 'API-KEY'}</div>
          {(!personalTool) && <Indicator className='w-1.5 h-1.5' color={customConfiged ? 'green' : 'gray'} />}
          {(personalTool) && <Indicator className='w-1.5 h-1.5' color={customConfigfg ? 'green' : 'red'} />}
          {
            (canAdmin || personalTool) && <>
              <Divider type='vertical' className='!mx-2'></Divider>
              <Tooltip
                popupContent={t('common.operation.settings')}
              >
                <TextButton onClick={setUpProvider} variant='hover'>
                  <Setting className='w-4 h-4' />
                </TextButton>
              </Tooltip>
              <div className='w-3'></div>
            </>
          }
        </>
      }
      {
        (canAdmin || personalTool) && <Tooltip
          popupContent={ personalTool ? model?.active ? '清除' : t('common.status.notAuthorized') : t('common.operation.delete') }
        >
          <TextButton disabled={!model?.active && personalTool} onClick={deleteProvider} variant='hover'>
            {(!personalTool && <Delete className='w-4 h-4 my-1'></Delete>)}
            {(personalTool && <Clear className='w-4 h-4 my-1'></Clear>)}
          </TextButton>
        </Tooltip>
      }
    </div>
  )
}

export default CredentialPanel
