import { memo, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { useDebounceFn } from 'ahooks'
import { Switch, Tooltip } from 'antd'
import type { ModelItem, ModelProvider } from '../declarations'
import { EventEmitterType, ModelStatusEnum } from '../declarations'
import ModelIcon from '../model-icon'
import ModelName from '../model-name'
import CredentialPanel from './credential-panel'
import classNames from '@/utils/classnames'
import { disableModel, enableModel } from '@/service/common'
import { useAppContext } from '@/context/app-context'
// 公共组件
import { Delete, Setting } from '@/app/components/base/icons/src/vender/line/action'
import TextButton from '@/app/components/base/button/text-button'
import { useEventEmitterContextContext } from '@/context/event-emitter'

export type ModelListItemProps = {
  model: ModelItem
  provider: ModelProvider
  isConfigurable: Boolean
  showOperation?: Boolean
  personalTool?: Boolean
}

const ModelListItem = ({ model, provider, isConfigurable, showOperation = true, personalTool }: ModelListItemProps) => {
  const { t } = useTranslation()
  const { eventEmitter } = useEventEmitterContextContext()
  const { canAdmin } = useAppContext()
  // 设置模型
  const setUpModel = () => {
    eventEmitter?.emit({
      type: EventEmitterType.setUpModel,
      provider,
      model,
    } as any)
  }
  // 删除模型
  const deleteModel = () => {
    eventEmitter?.emit({
      type: EventEmitterType.deleteModel,
      provider,
      model,
    } as any)
  }
  // 勾选模型启用
  const toggleModelEnablingStatus = useCallback(async (enabled: boolean) => {
    if (enabled)
      await enableModel(`/workspaces/current/model-providers/${provider.provider}/models/enable`, { model: model.model, model_type: model.model_type })
    else
      await disableModel(`/workspaces/current/model-providers/${provider.provider}/models/disable`, { model: model.model, model_type: model.model_type })
  }, [model.model, model.model_type, provider.provider])

  const { run: onEnablingStateChange } = useDebounceFn(toggleModelEnablingStatus, { wait: 500 })

  return (
    <div
      key={model.model}
      className={classNames(
        'group flex items-center px-4 h-8 mt-2',
        isConfigurable && 'hover:bg-gray-50',
        model.deprecated && 'opacity-60',
      )}
    >
      <ModelIcon
        className='shrink-0 mr-2'
        provider={provider}
        model={model}
        modelName={model.model}
      />
      <ModelName
        className='grow text-sm font-normal text-gray-G2'
        modelItem={model}
        showModelType
        showMode
        showContextSize
      />

      {
        showOperation && !personalTool && (
          <div className='shrink-0 flex items-center gap-2'>
            {/* 操作按钮 */}
            {
              isConfigurable && canAdmin && (
                <>
                  <Tooltip
                    title={t('common.operation.settings')}
                  >
                    <TextButton variant='hover' onClick={setUpModel}>
                      <Setting className='w-4 h-4' />
                    </TextButton>
                  </Tooltip>
                  <Tooltip
                    title={t('common.operation.delete')}
                  >
                    <TextButton onClick={deleteModel} variant='hover'>
                      <Delete className='w-4 h-4'></Delete>
                    </TextButton>
                  </Tooltip>
                </>
              )
            }
            {/* 是否能够启用 */}
            {
              model.deprecated
                ? (
                  <Tooltip title={t('account.modelProvider.modelHasBeenDeprecated')}>
                    <Switch defaultValue={false} disabled size='small' />
                  </Tooltip>
                )
                : (canAdmin && (
                  <Switch
                    defaultValue={model?.status === ModelStatusEnum.active}
                    disabled={![ModelStatusEnum.active, ModelStatusEnum.disabled].includes(model.status)}
                    size='small'
                    onChange={onEnablingStateChange}
                  />
                ))
            }
          </div>)
      }
      {/* 工具箱 */}
      {personalTool && <CredentialPanel personalTool model={model} provider={provider}/>}
    </div>
  )
}

export default memo(ModelListItem)
