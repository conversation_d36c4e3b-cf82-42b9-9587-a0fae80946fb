export type FormValue = Record<string, any>

export type TypeWithI18N<T = string> = {
  en_US: T
  zh_Hans: T
  [key: string]: T
}

// 表单类型枚举
export enum FormTypeEnum {
  textInput = 'text-input',
  textNumber = 'number-input',
  secretInput = 'secret-input',
  nameInput = 'name-input',
  select = 'select',
  radio = 'radio',
  boolean = 'boolean',
  files = 'files',
  file = 'file',
}
// 表单项
export type FormOption = {
  label: TypeWithI18N
  value: string
  show_on: FormShowOnObject[]
}
// 模型或供应商配置表单项
export type CredentialFormSchemaBase = {
  variable: string
  label: TypeWithI18N
  type: FormTypeEnum
  required: boolean
  default?: string
  tooltip?: TypeWithI18N
  show_on: FormShowOnObject[]
  url?: string
}
export type CredentialFormSchemaTextInput = CredentialFormSchemaBase & { max_length?: number; placeholder?: TypeWithI18N }
export type CredentialFormSchemaNumberInput = CredentialFormSchemaBase & { min?: number; max?: number; placeholder?: TypeWithI18N }
export type CredentialFormSchemaSelect = CredentialFormSchemaBase & { options: FormOption[]; placeholder?: TypeWithI18N }
export type CredentialFormSchemaRadio = CredentialFormSchemaBase & { options: FormOption[] }
export type CredentialFormSchemaSecretInput = CredentialFormSchemaBase & { placeholder?: TypeWithI18N }
export type CredentialFormSchemaNameInput = CredentialFormSchemaBase & { placeholder?: TypeWithI18N; icon_variable: string; icon_default: string }
export type CredentialFormSchema = CredentialFormSchemaTextInput | CredentialFormSchemaSelect | CredentialFormSchemaRadio | CredentialFormSchemaSecretInput

// 模型类型枚举
export enum ModelTypeEnum {
  textGeneration = 'llm',
  textEmbedding = 'text-embedding',
  rerank = 'rerank',
  speech2text = 'speech2text',
  moderation = 'moderation',
  tts = 'tts',
}
export enum modelSourceEnum {
  Official = 'Official',
  Local = 'Local',
  ThirdParty = 'ThirdParty',
  Personal = 'Personal',
  // 废弃
  Telechat = 'Telechat',
  PublicCloud = 'PublicCloud',
  Localai = 'Localai',
}
export const ModelSourceLabel: { [k: string]: TypeWithI18N } = {
  Official: {
    zh_Hans: '内置模型',
    en_US: 'Official Model',
  },
  Local: {
    zh_Hans: '本地模型',
    en_US: 'Local Model',
  },
  ThirdParty: {
    zh_Hans: '第三方模型',
    en_US: 'Third Party Model',
  },
  Personal: {
    zh_Hans: '个人模型',
    en_US: 'Personal Model',
  },
  // 废弃
  Telechat: {
    zh_Hans: '内置模型',
    en_US: 'Official Model',
  },
  PublicCloud: {
    zh_Hans: '公有云模型',
    en_US: 'Public Cloud Model',
  },
  Localai: {
    zh_Hans: '本地模型',
    en_US: 'Local Model',
  },
}
// 模型类型对应文本
export const MODEL_TYPE_TEXT = {
  [ModelTypeEnum.textGeneration]: 'LLM',
  [ModelTypeEnum.textEmbedding]: 'Text Embedding',
  [ModelTypeEnum.rerank]: 'Rerank',
  [ModelTypeEnum.speech2text]: 'Speech2text',
  [ModelTypeEnum.moderation]: 'Moderation',
  [ModelTypeEnum.tts]: 'TTS',
}
// 模型状态枚举
export enum ModelStatusEnum {
  active = 'active',
  noConfigure = 'no-configure',
  quotaExceeded = 'quota-exceeded',
  noPermission = 'no-permission',
  disabled = 'disabled',
}
// 模型状态对应文本
export const MODEL_STATUS_TEXT: { [k: string]: TypeWithI18N } = {
  'no-configure': {
    en_US: 'No Configure',
    zh_Hans: '未配置凭据',
  },
  'quota-exceeded': {
    en_US: 'Quota Exceeded',
    zh_Hans: '额度不足',
  },
  'no-permission': {
    en_US: 'No Permission',
    zh_Hans: '无使用权限',
  },
  'disabled': {
    en_US: 'Disabled',
    zh_Hans: '已禁用',
  },
}
// 模型特征枚举
export enum ModelFeatureEnum {
  toolCall = 'tool-call',
  multiToolCall = 'multi-tool-call',
  agentThought = 'agent-thought',
  vision = 'vision',
}
// 模型特征文本枚举
export enum ModelFeatureTextEnum {
  toolCall = 'Tool Call',
  multiToolCall = 'Multi Tool Call',
  agentThought = 'Agent Thought',
  vision = 'Vision',
}
export type ModelItem = {
  model: string
  model_label?: string
  icon?: string
  label: TypeWithI18N
  model_type: ModelTypeEnum
  features?: ModelFeatureEnum[]
  fetch_from: ConfigurationMethodEnum
  status: ModelStatusEnum
  model_properties: Record<string, string | number>
  load_balancing_enabled: boolean
  deprecated?: boolean
  active?: boolean
  is_person?: boolean
}
export type Model = {
  provider: string
  provider_label?: string
  icon_large: TypeWithI18N
  icon_small: TypeWithI18N
  label: TypeWithI18N
  models: ModelItem[]
  status: ModelStatusEnum
}

// 模型供应商类型
export enum ProviderType {
  offical = 'offical',
  thirdParty = 'third-party',
  personal= 'personal',
}
// 模型供应商标签
export enum ProviderLabel {
  Official = 'official',
  Local = 'local',
  ThirdParty = 'third_party',
}
// 模型供应商
export type ModelProvider = {
  provider: string
  provider_label: ProviderLabel
  label: TypeWithI18N
  description?: TypeWithI18N
  help?: {
    title: TypeWithI18N
    url: TypeWithI18N
  }
  icon_small: TypeWithI18N
  icon_large: TypeWithI18N
  background?: string
  supported_model_types: ModelTypeEnum[]
  configurate_methods: ConfigurationMethodEnum[]
  provider_credential_schema?: {
    credential_form_schemas: CredentialFormSchema[]
  }
  model_credential_schema: {
    model: {
      label: TypeWithI18N
      placeholder: TypeWithI18N
    }
    credential_form_schemas: CredentialFormSchema[]
  }
  preferred_provider_type: PreferredProviderTypeEnum
  custom_configuration: {
    status: CustomConfigurationStatusEnum
  }
  system_configuration: {
    enabled: boolean
    current_quota_type: CurrentSystemQuotaTypeEnum
    quota_configurations: QuotaConfiguration[]
  }
}

// 配置方法枚举
export enum ConfigurationMethodEnum {
  predefinedModel = 'predefined-model',
  customizableModel = 'customizable-model',
  fetchFromRemote = 'fetch-from-remote',
  personalModel= 'personal-model',
}
// 事件总线枚举
export enum EventEmitterType {
  // 删除provider
  deleteProvider = 'delete-provider',
  // 设置provider
  setUpProvider = 'set-up-provider',
  // 删除模型
  deleteModel = 'delete-model',
  // 设置模型
  setUpModel = 'set-up-model',
  // 添加模型
  addModel = 'add-model',
  // 更新模型列表
  updateModelList = 'update-model-list',
  // 新增供应商
  addProvider = 'add-provider',
}
export enum CustomConfigurationStatusEnum {
  active = 'active',
  noConfigure = 'no-configure',
}

export type FormShowOnObject = {
  variable: string
  value: string
}
export enum PreferredProviderTypeEnum {
  system = 'system',
  custom = 'custom',
}
export enum CurrentSystemQuotaTypeEnum {
  trial = 'trial',
  free = 'free',
  paid = 'paid',
}
export enum QuotaUnitEnum {
  times = 'times',
  tokens = 'tokens',
  credits = 'credits',
}
export type QuotaConfiguration = {
  quota_type: CurrentSystemQuotaTypeEnum
  quota_unit: QuotaUnitEnum
  quota_limit: number
  quota_used: number
  last_used: number
  is_valid: boolean
}

export type DefaultModelResponse = {
  model: string
  model_type: ModelTypeEnum
  provider: {
    provider: string
    icon_large: TypeWithI18N
    icon_small: TypeWithI18N
  }
}

export type DefaultModel = {
  provider: string
  model: string
}

export type CustomConfigurationModelFixedFields = {
  __model_name: string
  __model_type: ModelTypeEnum
}

export type ModelParameterRule = {
  default?: number | string | boolean | string[]
  default_creative?: number | string | boolean | string[]
  default_normal?: number | string | boolean | string[]
  default_precise?: number | string | boolean | string[]
  help?: TypeWithI18N
  label: TypeWithI18N
  min?: number
  max?: number
  name: string
  precision?: number
  required: false
  type: string
  use_template?: string
  options?: string[]
  tagPlaceholder?: TypeWithI18N
}

export type ModelLoadBalancingConfig = {
  enabled: boolean
  configs: ModelLoadBalancingConfigEntry[]
}

export type ModelLoadBalancingConfigEntry = {
  /** model balancing config entry id */
  id?: string
  /** is config entry enabled */
  enabled?: boolean
  /** config entry name */
  name: string
  /** model balancing credential */
  credentials: Record<string, string | undefined | boolean>
  /** is config entry currently removed from Round-robin queue */
  in_cooldown?: boolean
  /** cooldown time (in seconds) */
  ttl?: number
}
