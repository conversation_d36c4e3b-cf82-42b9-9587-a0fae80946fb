import type {
  CredentialFormSchemaNameInput,
  CredentialFormSchemaRadio,
  CredentialFormSchemaTextInput,
  FormValue,
  ModelProvider,
} from './declarations'
import {
  ConfigurationMethodEnum,
  FormTypeEnum,
  MODEL_TYPE_TEXT,
  ModelTypeEnum,
  ProviderLabel,
} from './declarations'
import {
  deleteModelProvider,
  setModelProvider,
} from '@/service/common'

export const isNullOrUndefined = (value: any) => {
  return value === undefined || value === null
}

// 判断是否为官方模型
export const isOfficalProvider = (item: ModelProvider) => {
  return item.provider_label === ProviderLabel.Official
}
// 判断是否为本地模型
export const isLocalProvider = (item: ModelProvider) => {
  return item.provider_label === ProviderLabel.Local
}
// 判断是否为公有云模型
export const isCloudProvider = (item: ModelProvider) => {
  return item.provider_label === ProviderLabel.ThirdParty
}

// 判断provider配置方式为custom
export const judgeCustomConfigProvider = (item: ModelProvider) => {
  return item.configurate_methods.includes(ConfigurationMethodEnum.customizableModel)
}
// 判断provider配置方式为predfefine
export const judgePredefineConfigProvider = (item: ModelProvider) => {
  return item.configurate_methods.includes(ConfigurationMethodEnum.predefinedModel)
}
// 删除模型
export const removeModel = async (provider: string, v: FormValue) => {
  let url = ''
  let body

  if (v) {
    const { __model_name, __model_type } = v
    body = {
      model: __model_name,
      model_type: __model_type,
    }
    url = `/workspaces/current/model-providers/${provider}/models`
  }

  return deleteModelProvider({ url, body })
}
// 删除模型供应商
export const removeProvider = async (provider: string) => {
  const url = `/workspaces/current/model-providers/${provider}`
  return deleteModelProvider({ url })
}
// 删除个人模型appkey
export const removePersonal = async (provider: string,model:any) => {
  const url = `/workspaces/current/personal-model-providers/${provider}/models`
  const body = {
   model:model.model,
   model_type:model.model_type
  }
  return deleteModelProvider({ url, body })
}
// 保存模型或模型提供商
export const saveCredentials = async (predefined: boolean, provider: string, v: FormValue, isEdit: Boolean, loadBalancing?: any) => {
  let body, url

  if (predefined) {
    body = {
      config_from: ConfigurationMethodEnum.predefinedModel,
      credentials: v,
      load_balancing: loadBalancing,
    }
    url = `/workspaces/current/model-providers/${provider}`
  }
  else {
    const { __model_name, __model_type, ...credentials } = v
    body = {
      model: __model_name,
      model_type: __model_type,
      icon: credentials.model_icon,
      credentials,
      load_balancing: loadBalancing,
      action: isEdit ? 'mod' : 'add',
    }
    url = `/workspaces/current/model-providers/${provider}/models`
  }

  return setModelProvider({ url, body })
}
// 保存个人模型appkay
export const savePersonal = async (model: any, provider: string, v: FormValue) => {
  let body, url
    body = {
      ...v,
      model:model.model,
      model_type:model.model_type
    }
    url = `/workspaces/current/personal-model-providers/${provider}/models`

  return setModelProvider({ url, body })
}
// 生成模型类型表单项
export const genModelTypeFormSchema = (modelTypes: ModelTypeEnum[]) => {
  return {
    type: FormTypeEnum.radio,
    label: {
      zh_Hans: '模型类型',
      en_US: 'Model Type',
    },
    variable: '__model_type',
    default: modelTypes[0],
    required: true,
    show_on: [],
    options: modelTypes.map((modelType: ModelTypeEnum) => {
      return {
        value: modelType,
        label: {
          zh_Hans: MODEL_TYPE_TEXT[modelType],
          en_US: MODEL_TYPE_TEXT[modelType],
        },
        show_on: [],
      }
    }),
  } as CredentialFormSchemaRadio
}
// 模型类型格式化
export const modelTypeFormat = (modelType: ModelTypeEnum) => {
  if (modelType === ModelTypeEnum.textEmbedding)
    return 'TEXT EMBEDDING'

  return modelType.toLocaleUpperCase()
}
// 生成模型名称表单项
export const genModelNameFormSchema = (model?: Pick<CredentialFormSchemaTextInput, 'label' | 'placeholder'>) => {
  return {
    icon_variable: 'model_icon',
    icon_default: '/assets/avatar/model.svg',
    type: FormTypeEnum.nameInput,
    label: model?.label || {
      zh_Hans: '模型名称',
      en_US: 'Model Name',
    },
    variable: '__model_name',
    required: true,
    show_on: [],
    placeholder: model?.placeholder || {
      zh_Hans: '请输入模型名称',
      en_US: 'Please enter model name',
    },
  } as CredentialFormSchemaNameInput
}

export const sizeFormat = (size: number) => {
  const remainder = Math.floor(size / 1000)
  if (remainder < 1)
    return `${size}`
  else
    return `${remainder}K`
}

export const savePredefinedLoadBalancingConfig = async (provider: string, v: FormValue, loadBalancing?: any) => {
  const { __model_name, __model_type, ...credentials } = v
  const body = {
    config_from: ConfigurationMethodEnum.predefinedModel,
    model: __model_name,
    model_type: __model_type,
    credentials,
    load_balancing: loadBalancing,
  }
  const url = `/workspaces/current/model-providers/${provider}/models`

  return setModelProvider({ url, body })
}
