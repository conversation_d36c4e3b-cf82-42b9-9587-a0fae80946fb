import type { FC } from 'react'
import type {
  Model,
  ModelItem,
} from '../declarations'
import {
  MODEL_STATUS_TEXT,
  ModelStatusEnum,
} from '../declarations'
import { useLanguage } from '../hooks'
import ModelIcon from '../model-icon'
import ModelName from '../model-name'
import { WarningTriangle } from '@/app/components/base/icons/src/vender/line/tip'
import Tooltip from '@/app/components/base/tooltip'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'

type ModelTriggerProps = {
  open: boolean
  provider: Model
  model: ModelItem
  className?: string
  readonly?: boolean
}
const ModelTrigger: FC<ModelTriggerProps> = ({
  open,
  provider,
  model,
  className,
  readonly,
}) => {
  const language = useLanguage()

  return (
    <div
      className={`
        group flex items-center px-3 py-2 h-9 rounded bg-transparent border border-gray-G5 hover:border-primary-P1
        ${!readonly && 'cursor-pointer'}
        ${className}
        ${open && '!bg-transparent !border-primary-P1'}
        ${model.status !== ModelStatusEnum.active && '!bg-[#FFFAEB]'}
      `}
    >
      <ModelIcon
        className='shrink-0 mr-1 text-gray-G2'
        provider={provider}
        modelName={model.model}
        model={model}
      />
      <ModelName
        className='grow !text-S3'
        modelItem={model}
        showMode
        showFeatures
      />
      {!readonly && (
        <div className='shrink-0 flex items-center justify-center w-4 h-4'>
          {
            model.status !== ModelStatusEnum.active
              ? (
                <Tooltip popupContent={MODEL_STATUS_TEXT[model.status][language]}>
                  <WarningTriangle className='w-4 h-4 text-[#F79009]' />
                </Tooltip>
              )
              : (
                <ArrowDown
                  className='w-4 h-4 text-gray-G2'
                />
              )
          }
        </div>
      )}

    </div>
  )
}

export default ModelTrigger
