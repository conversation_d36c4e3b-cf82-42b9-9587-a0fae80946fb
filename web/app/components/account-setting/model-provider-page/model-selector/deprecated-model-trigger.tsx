import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import ModelIcon from '../model-icon'
import type { ModelItem } from '../declarations'
import { WarningTriangle } from '@/app/components/base/icons/src/vender/line/tip'
import { useProviderContext } from '@/context/provider-context'
import Tooltip from '@/app/components/base/tooltip'

type ModelTriggerProps = {
  modelName: string
  providerName: string
  className?: string
  model?: ModelItem
}
const ModelTrigger: FC<ModelTriggerProps> = ({
  modelName,
  providerName,
  className,
  model,
}) => {
  const { t } = useTranslation()
  const { modelProviders } = useProviderContext()
  const currentProvider = modelProviders.find(provider => provider.provider === providerName)

  return (
    <div
      className={`
        group flex items-center px-2 h-8 rounded bg-[#FFFAEB] cursor-pointer
        ${className}
      `}
    >
      <ModelIcon
        className='shrink-0 mr-1.5'
        provider={currentProvider}
        modelName={modelName}
        model={model}
      />
      <div className='mr-1 text-[13px] font-semibold text-gray-800 truncate'>
        {modelName}
      </div>
      <div className='shrink-0 flex items-center justify-center w-4 h-4'>
        <Tooltip popupContent={t('account.modelProvider.deprecated')}>
          <WarningTriangle className='w-4 h-4 text-[#F79009]' />
        </Tooltip>
      </div>
    </div>
  )
}

export default ModelTrigger
