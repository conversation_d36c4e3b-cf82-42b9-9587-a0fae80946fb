import type { FC } from 'react'
import { useMemo, useState } from 'react'
import type { MenuItemGroupType } from 'antd/es/menu/interface'
import {
  type DefaultModel,
  type Model,
  ModelSourceLabel,
  ModelStatusEnum,
  modelSourceEnum,
} from '../declarations'
import { useCurrentProviderAndModel, useLanguage } from '../hooks'
import ModelIcon from '../model-icon'
import ModelName from '../model-name'
import ModelTrigger from './model-trigger'
import EmptyTrigger from './empty-trigger'
import DeprecatedModelTrigger from './deprecated-model-trigger'

import PopoverSelect from '@/app/components/base/select/popover-select'

type ModelSelectorProps = {
  loading?: boolean
  defaultModel?: DefaultModel
  modelList: Model[]
  triggerClassName?: string
  wrapperClassName?: string
  popupClassName?: string
  onSelect?: (model: DefaultModel) => void
  readonly?: boolean
  onFetch?: () => void
}
const ModelSelector: FC<ModelSelectorProps> = ({
  loading = false,
  defaultModel,
  modelList,
  triggerClassName,
  wrapperClassName,
  onSelect,
  readonly,
  onFetch,
}) => {
  const [open, setOpen] = useState(false)
  // 能在模型列表中，找到符合条件的默认模型
  const {
    currentProvider,
    currentModel,
  } = useCurrentProviderAndModel(
    modelList,
    defaultModel,
  )
  const language = useLanguage()

  const handleSelect = (value: string) => {
    const [provider, model] = value.split(',')
    setOpen(false)

    if (onSelect)
      onSelect({ provider, model })
  }

  // 模型下拉框打开时重新拉取模型列表
  const handleOpen = (open: boolean) => {
    if (open && onFetch)
      onFetch()
    setOpen(open)
  }

  // 下拉选择器选项
  const options: MenuItemGroupType[] = useMemo(() => modelList.map((model) => {
    return {
      type: 'group',
      key: `${model.provider_label},${model.provider}`,
      label: model.label[language] || model.label.en_US,
      children: model.models.map((modelItem) => {
        return {
          key: `${model.provider},${modelItem.model}`,
          disabled: modelItem?.status === ModelStatusEnum.disabled,
          is_person: modelItem?.is_person,
          label: <>
            <ModelIcon
              className={`
                  shrink-0 mr-2 w-4 h-4
                  ${modelItem.status !== ModelStatusEnum.active && 'opacity-60'}
                `}
              provider={model}
              model={modelItem}
              modelName={modelItem.model}
            />
            <ModelName
              className={`
                  grow text-sm font-normal
                  ${modelItem.status !== ModelStatusEnum.active && 'opacity-60'}
                `}
              modelItem={modelItem}
              showMode
              showFeatures
              tagStyle={'ml-2 !bg-[#F0F3FA] !text-gray-G1 !border-none'}
            />
          </>,
          title: modelItem.label[language].toLowerCase(),
        }
      }),
    }
  }), [language, modelList])

  const optionsWithSource: MenuItemGroupType[] = useMemo(() => {
    const getLabel = (key: modelSourceEnum) => {
      return ModelSourceLabel[key][language]
    }
    const getGroupChildren = (providerLabel: string) => {
      const group = options.filter((item) => {
        const key = item.key as string
        const provider = key.split(',')[0]
        return provider === providerLabel
      })
      return group.flatMap(item => item.children || []) || []
    }
    const getPersonalChildren = (providerLabel: string) => {
      const group = options.filter((item) => {
        const key = item.key as string
        const provider = key.split(',')[0]
        return provider === providerLabel
      })

      //  const personal = group.flatMap(item => item.children || []) || []
      return group.flatMap(item => item.children || []) || []
    }
    // 官方模型
    const officialGroupChildren = getGroupChildren('official')
    const officialPersonalChildren = getPersonalChildren('personal')
    // 本地模型
    const localGroupChildren = getGroupChildren('local')
    return [
      {
        type: 'group',
        label: getLabel(modelSourceEnum.Personal),
        key: modelSourceEnum.Personal,
        children: [...officialPersonalChildren],
      },
      // 官方模型
      {
        type: 'group',
        label: getLabel(modelSourceEnum.Official),
        key: modelSourceEnum.Official,
        // children: [...telechatGroupChildren, ...tongyiGroupChildren],
        children: [...officialGroupChildren],
      },
      // 第三方模型
      {
        type: 'group',
        label: getLabel(modelSourceEnum.ThirdParty),
        key: modelSourceEnum.ThirdParty,
        children: [
          // 本地模型
          {
            type: 'group',
            label: getLabel(modelSourceEnum.Local),
            key: modelSourceEnum.Local,
            children: localGroupChildren,
          },
          // 公有云模型
          {
            type: 'group',
            label: getLabel(modelSourceEnum.PublicCloud),
            key: modelSourceEnum.PublicCloud,
            children: options.filter((item) => {
              const key = item.key as string
              const provider_label = key.split(',')[0]
              return provider_label !== 'official' && provider_label !== 'local' && provider_label !== 'personal'
            }),
          },
        ],
      },
    ]
  }, [language, options])

  return (
    <PopoverSelect
      loading={loading}
      options={optionsWithSource}
      defaultValue={`${defaultModel?.provider},${defaultModel?.model}` || ''}
      onChange={handleSelect}
      onOpenChange={handleOpen}
      search={true}
      readonly={readonly}
      overlayClassName={'ModelSelector'}
    >
      {/* 触发器 */}
      <div className={wrapperClassName}>
        {/* 能在模型列表中找到符合条件的当前模型 */}
        {
          currentModel && currentProvider && (
            <ModelTrigger
              open={open}
              provider={currentProvider}
              model={currentModel}
              className={triggerClassName}
              readonly={readonly}
            />
          )
        }
        {/* 无法找到符合条件的模型，但是模型存在，说明过期 */}
        {
          !currentModel && defaultModel && (
            <DeprecatedModelTrigger
              modelName={defaultModel?.model || ''}
              providerName={defaultModel?.provider || ''}
              className={triggerClassName}
              model={defaultModel as any}
            />
          )
        }
        {/* 如果默认模型不存在 */}
        {
          !defaultModel && (
            <EmptyTrigger
              open={open}
              className={triggerClassName}
            />
          )
        }
      </div>
    </PopoverSelect>
  )
}

export default ModelSelector
