import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { CubeOutline } from '@/app/components/base/icons/src/vender/line/others'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'

type ModelTriggerProps = {
  open: boolean
  className?: string
}
const ModelTrigger: FC<ModelTriggerProps> = ({
  open,
  className,
}) => {
  const { t } = useTranslation()
  return (
    <div
      className={`
        flex items-center px-3 py-2 h-9 rounded border border-gray-G5 cursor-pointer
        ${className}
      `}
    >
      <div className='flex items-center grow'>
        <div className='mr-1.5 flex items-center justify-center w-4 h-4 rounded-[5px] border border-dashed border-black/5'>
          <CubeOutline className='w-3 h-3 text-gray-400' />
        </div>
        <div
          className='text-[13px] text-gray-500 truncate'
          title='Select model'
        >
          {t('account.modelProvider.action.selectModel')}
        </div>
      </div>
      <div className='flex items-center justify-center w-4 h-4 shrink-0'>
        <ArrowDown className='w-4 h-4 text-gray-G2' />
      </div>
    </div>
  )
}

export default ModelTrigger
