import type { FC } from 'react'
import { useEffect, useState } from 'react'
import { Form, Input, InputNumber, Switch } from 'antd'
import type { InputNumberProps } from 'antd'
import type { ModelParameterRule } from '../declarations'
import { useLanguage } from '../hooks'
import { isNullOrUndefined } from '../utils'
import Slider from '@/app/components/base/slider'
import Radio from '@/app/components/base/radio'
import TagInput from '@/app/components/base/tag-input'
import Select from '@/app/components/base/select'

export type ParameterValue = number | string | string[] | boolean | undefined

type ParameterItemProps = {
  modelId?: string
  parameterRule: ModelParameterRule
  value?: ParameterValue
  onChange?: (value: ParameterValue) => void
  className?: string
  onSwitch?: (checked: boolean, assignValue: ParameterValue) => void
  isInWorkflow?: boolean
}
const ParameterItem: FC<ParameterItemProps> = ({
  modelId,
  parameterRule,
  value,
  onChange,
  className,
  onSwitch,
  isInWorkflow,
}) => {
  const language = useLanguage()
  const [localValue, setLocalValue] = useState(value)
  // const [inputValue, setInputValue] = useState<number>()

  const getDefaultValue = () => {
    let defaultValue: ParameterValue

    if (parameterRule.type === 'int' || parameterRule.type === 'float')
      defaultValue = isNullOrUndefined(parameterRule.default) ? (parameterRule.min || 0) : parameterRule.default
    else if (parameterRule.type === 'string' || parameterRule.type === 'text')
      defaultValue = parameterRule.options?.length ? (parameterRule.default || '') : (parameterRule.default || '')
    else if (parameterRule.type === 'boolean')
      defaultValue = !isNullOrUndefined(parameterRule.default) ? parameterRule.default : false
    else if (parameterRule.type === 'tag')
      defaultValue = !isNullOrUndefined(parameterRule.default) ? parameterRule.default : []

    return defaultValue
  }

  const renderValue = value ?? localValue ?? getDefaultValue()

  const handleInputChange = (newValue: ParameterValue) => {
    setLocalValue(newValue)
    if (onChange && (parameterRule.name === 'stop' || !isNullOrUndefined(value) || parameterRule.required))
      onChange(newValue)
  }

  const handleNumberInputChange: InputNumberProps['onChange'] = (value) => {
    if (value === null)
      return
    let num = +value

    if (!isNullOrUndefined(parameterRule.max) && num > parameterRule.max!)
      num = parameterRule.max as number
      // setInputValue(num)

    if (!isNullOrUndefined(parameterRule.min) && num < parameterRule.min!)
      num = parameterRule.min as number

    handleInputChange(num)
    // setInputValue(num)
  }

  const handleSlideChange = (num: number) => {
    if (!isNullOrUndefined(parameterRule.max) && num > parameterRule.max!) {
      handleInputChange(parameterRule.max)
      // setInputValue(parameterRule.max)
      return
    }

    if (!isNullOrUndefined(parameterRule.min) && num < parameterRule.min!) {
      handleInputChange(parameterRule.min)
      // setInputValue(parameterRule.min)
      return
    }

    handleInputChange(num)
    // setInputValue(num)
  }

  const handleRadioChange = (v: number) => {
    handleInputChange(v === 1)
  }

  const handleStringInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    handleInputChange(e.target.value)
  }

  const handleSelect = (value: string) => {
    handleInputChange(value)
  }

  const handleTagChange = (newSequences: string[]) => {
    handleInputChange(newSequences)
  }

  const handleSwitch = (checked: boolean) => {
    if (onSwitch) {
      const assignValue: ParameterValue = localValue || getDefaultValue()

      onSwitch(checked, assignValue)
    }
  }

  useEffect(() => {
    if ((parameterRule.type === 'int' || parameterRule.type === 'float'))
      handleNumberInputChange(Number(renderValue))
  }, [modelId])

  const renderInput = () => {
    const numberInputWithSlide = (parameterRule.type === 'int' || parameterRule.type === 'float')
      && !isNullOrUndefined(parameterRule.min)
      && !isNullOrUndefined(parameterRule.max)

    if (parameterRule.type === 'int' || parameterRule.type === 'float') {
      let step = 100
      if (parameterRule.max) {
        if (parameterRule.max < 10)
          step = 0.1
        else if (parameterRule.max < 100)
          step = 1
        else if (parameterRule.max < 1000)
          step = 10
        else if (parameterRule.max < 10000)
          step = 100
      }

      return (
        <div className='flex items-center justify-between'>
          {numberInputWithSlide && <Slider
            wrapClassName='w-[200px]'
            value={renderValue as number}
            min={parameterRule.min}
            max={parameterRule.max}
            step={step}
            onChange={handleSlideChange}
          />}
          <InputNumber
            value={renderValue as number}
            max={parameterRule.max}
            min={parameterRule.min}
            step={numberInputWithSlide ? step : +`0.${parameterRule.precision || 0}`}
            onChange={handleNumberInputChange}
            placeholder=''
            className='shrink-0'
          />
        </div>
      )
    }

    if (parameterRule.type === 'boolean') {
      return (
        <Radio.Group
          className='w-[200px] flex items-center'
          value={renderValue ? 1 : 0}
          onChange={handleRadioChange}
        >
          <Radio value={1} className='!mr-1 w-[94px]'>True</Radio>
          <Radio value={0} className='w-[94px]'>False</Radio>
        </Radio.Group>
      )
    }

    if (parameterRule.type === 'string' && !parameterRule.options?.length) {
      return (
        <Input
          className={'w-full'}
          value={renderValue as string}
          onChange={handleStringInputChange}
          placeholder=''
        />
      )
    }

    if (parameterRule.type === 'text') {
      return (
        <textarea
          className='w-full h-20 px-1 rounded-lg bg-gray-100 outline-none text-[12px] text-gray-900'
          value={renderValue as string}
          onChange={handleStringInputChange}
        />
      )
    }

    if (parameterRule.type === 'string' && !!parameterRule?.options?.length) {
      return (
        <Select
          className='w-full'
          defaultValue={renderValue as string}
          onChange={handleSelect}
          options={parameterRule.options.map(option => ({ value: option, label: option }))}
        />
      )
    }

    if (parameterRule.type === 'tag') {
      return (
        <TagInput
          className='w-full'
          items={renderValue as string[]}
          onChange={handleTagChange}
          customizedConfirmKey='Tab'
          isInWorkflow={isInWorkflow}
        />
      )
    }

    return null
  }

  return (
    <Form.Item
      layout='vertical'
      label={
        <div className='flex items-center gap-1'>
          <span>{parameterRule.label[language] || parameterRule.label.en_US}</span>
          {
            !parameterRule.required && parameterRule.name !== 'stop' && (
              <Switch
                className='mr-1'
                defaultValue={!isNullOrUndefined(value)}
                onChange={handleSwitch}
                size='small'
              />
            )
          }
        </div>
      }
      tooltip={parameterRule.help ? (parameterRule.help[language] || parameterRule.help.en_US) : ''}
    >
      {renderInput()}
    </Form.Item>

  )
}

export default ParameterItem
