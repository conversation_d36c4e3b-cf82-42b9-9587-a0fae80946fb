import type {
  ReactNode,
} from 'react'
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import { Divider, Form, Popover } from 'antd'
import useSWR from 'swr'
import { useTranslation } from 'react-i18next'
import type {
  DefaultModel,
  FormValue,
  ModelParameterRule,
} from '../declarations'
import { ModelStatusEnum } from '../declarations'
import ModelSelector from '../model-selector'
import {
  useTextGenerationCurrentProviderAndModelAndModelList,
} from '../hooks'
import Trigger from '../model-parameter-modal/trigger'
import type { TriggerProps } from '../model-parameter-modal/trigger'
import PresetsParameter from '../model-parameter-modal/presets-parameter'
import type { ParameterValue } from './parameter-item'
import ParameterItem from './parameter-item'
import cn from '@/utils/classnames'

import { fetchModelParameterRules } from '@/service/common'
import { useProviderContext } from '@/context/provider-context'
import { TONE_LIST } from '@/config'
import Scrollbar from '@/app/components/base/scrollbar'

export type ModelParameterModalProps = {
  popupClassName?: string
  portalToFollowElemContentClassName?: string
  triggerClassName?: string
  isAdvancedMode: boolean
  mode: string
  modelId: string
  provider: string
  setModel: (model: { modelId: string; provider: string; mode?: string; features?: string[] }) => void
  completionParams: FormValue
  onCompletionParamsChange: (newParams: FormValue) => void
  hideDebugWithMultipleModel?: boolean
  debugWithMultipleModel?: boolean
  onDebugWithMultipleModelChange?: () => void
  renderTrigger?: (v: TriggerProps) => ReactNode
  readonly?: boolean
  isInWorkflow?: boolean
}
const stopParameterRule: ModelParameterRule = {
  default: [],
  help: {
    en_US: 'Up to four sequences where the API will stop generating further tokens. The returned text will not contain the stop sequence.',
    zh_Hans: '最多四个序列，API 将停止生成更多的 token。返回的文本将不包含停止序列。',
  },
  label: {
    en_US: 'Stop sequences',
    zh_Hans: '停止序列',
  },
  name: 'stop',
  required: false,
  type: 'tag',
  tagPlaceholder: {
    en_US: 'Enter sequence and press Tab',
    zh_Hans: '输入序列并按 Tab 键',
  },
}

const PROVIDER_WITH_PRESET_TONE = ['openai', 'azure_openai']
const ModelParameterModalInWorkflow = forwardRef(({
  popupClassName,
  triggerClassName,
  isAdvancedMode,
  modelId,
  provider,
  setModel,
  completionParams,
  onCompletionParamsChange,
  renderTrigger,
  readonly,
  isInWorkflow,
}: ModelParameterModalProps, ref) => {
  const { t } = useTranslation()
  const { isAPIKeySet } = useProviderContext()
  const [open, setOpen] = useState(false)
  const { data: parameterRulesData, isLoading } = useSWR((provider && modelId) ? `/workspaces/current/model-providers/${provider}/models/parameter-rules?model=${modelId}` : null, fetchModelParameterRules)
  const {
    currentProvider,
    currentModel,
    activeTextGenerationModelList,
    mutateTextGenerationModelList,
    isTextGenerationModelListLoading,
  } = useTextGenerationCurrentProviderAndModelAndModelList(
    { provider, model: modelId },
  )
  const hasDeprecated = !currentProvider || !currentModel
  const modelDisabled = currentModel?.status !== ModelStatusEnum.active
  const disabled = !isAPIKeySet || hasDeprecated || modelDisabled

  // 模型参数
  const completionParamsRef = useRef(completionParams)
  useEffect(() => {
    completionParamsRef.current = completionParams
  }, [completionParams])

  const parameterRules: ModelParameterRule[] = useMemo(() => {
    return parameterRulesData?.data || []
  }, [parameterRulesData])

  useImperativeHandle(ref, () => ({
    getOutputFormat: () => {
      const outputConfig = parameterRules.find(item => item.name === 'response_format')
      return outputConfig ? outputConfig.options : []
    },
  }))

  const handleParamChange = (key: string, value: ParameterValue) => {
    const newCompletionParams = {
      ...completionParamsRef.current,
      [key]: value,
    }
    onCompletionParamsChange(newCompletionParams)
    completionParamsRef.current = newCompletionParams
  }

  const handleChangeModel = ({ provider, model }: DefaultModel) => {
    const targetProvider = activeTextGenerationModelList.find(modelItem => modelItem.provider === provider)
    const targetModelItem = targetProvider?.models.find(modelItem => modelItem.model === model)
    setModel({
      modelId: model,
      provider,
      mode: targetModelItem?.model_properties.mode as string,
      features: targetModelItem?.features || [],
    })
  }

  const handleSwitch = (key: string, value: boolean, assignValue: ParameterValue) => {
    if (!value) {
      const newCompletionParams = { ...completionParamsRef.current }
      delete newCompletionParams[key]

      onCompletionParamsChange(newCompletionParams)
      completionParamsRef.current = newCompletionParams
    }
    if (value) {
      const newCompletionParams = {
        ...completionParamsRef.current,
        [key]: assignValue,
      }
      onCompletionParamsChange(newCompletionParams)
      completionParamsRef.current = newCompletionParams
    }
  }

  const handleSelectPresetParameter = (toneId: number) => {
    const tone = TONE_LIST.find(tone => tone.id === toneId)
    if (tone) {
      const newCompletionParams = {
        ...completionParamsRef.current,
        ...tone.config,
      }
      onCompletionParamsChange(newCompletionParams)
      completionParamsRef.current = newCompletionParams
    }
  }

  return (
    <Popover
      open={open}
      arrow={false}
      trigger={'click'}
      onOpenChange={(v) => { !v && setOpen(v) }}
      placement={'bottom'}
      content={(
        <div className={cn(popupClassName, 'max-h-[480px] w-[494px] py-4 flex flex-col')}>
          <Form.Item className='px-4 !mb-0' layout='vertical' label={t('account.modelProvider.modelSelect').toLocaleUpperCase()}>
            <ModelSelector
              loading={isTextGenerationModelListLoading}
              defaultModel={(provider || modelId) ? { provider, model: modelId } : undefined}
              modelList={activeTextGenerationModelList}
              onSelect={handleChangeModel}
              triggerClassName='max-w-[425px]'
              onFetch={mutateTextGenerationModelList}
            />
          </Form.Item>
          <Divider className='!my-3 !mx-4'></Divider>
          <Scrollbar loading={isLoading} className='px-4'>
            {
              !isLoading && !!parameterRules.length && (
                <div className='flex items-center justify-between mb-4'>
                  <div className={cn('font-semibold text-gray-G1', isInWorkflow && 'text-[14px]')}>{t('account.modelProvider.parameters')}</div>
                  {
                    PROVIDER_WITH_PRESET_TONE.includes(provider) && (
                      <PresetsParameter onSelect={handleSelectPresetParameter} />
                    )
                  }
                </div>
              )
            }
            {
              !isLoading && !!parameterRules.length && (
                [
                  ...parameterRules,
                  ...(isAdvancedMode ? [stopParameterRule] : []),
                ].map(parameter => (
                  <ParameterItem
                    key={`${modelId}-${parameter.name}`}
                    className='mb-4'
                    parameterRule={parameter}
                    value={completionParamsRef.current?.[parameter.name]}
                    onChange={v => handleParamChange(parameter.name, v)}
                    onSwitch={(checked, assignValue) => handleSwitch(parameter.name, checked, assignValue)}
                    isInWorkflow={isInWorkflow}
                  />
                ))
              )
            }
          </Scrollbar>
        </div>

      )}
    >
      <div
        onClick={() => {
          if (readonly)
            return
          setOpen(v => !v)
        }}
        className={`relative flex justify-center items-center ${triggerClassName}`}
      >
        {
          renderTrigger
            ? renderTrigger({
              open,
              disabled,
              modelDisabled,
              hasDeprecated,
              currentProvider,
              currentModel,
              providerName: provider,
              modelId,
            })
            : (
              <Trigger
                disabled={disabled}
                isInWorkflow={isInWorkflow}
                modelDisabled={modelDisabled}
                hasDeprecated={hasDeprecated}
                currentProvider={currentProvider}
                currentModel={currentModel}
                providerName={provider}
                modelId={modelId}
              />
            )
        }
      </div>
    </Popover>
  )
})

ModelParameterModalInWorkflow.displayName = 'ModelParameterModalInWorkflow'

export default ModelParameterModalInWorkflow
