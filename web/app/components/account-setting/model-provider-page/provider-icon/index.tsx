import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import type { ModelProvider } from '../declarations'
import { useLanguage } from '../hooks'
import { isLocalProvider } from '../utils'
import { getPurifyHref } from '@/utils'

type ProviderIconProps = {
  provider: ModelProvider
  className?: string
}
const ProviderIcon: FC<ProviderIconProps> = ({
  provider,
  className,
}) => {
  const language = useLanguage()
  const { t } = useTranslation()

  if (isLocalProvider(provider)) {
    return (
      <div className={`inline-flex items-center gap-[6px] ${className}`}>
        <img
          alt='provider-icon'
          src='/assets/avatar/localai.svg'
          className='!w-6 !h-6'
        ></img>
        <span className='text-[#393a3a] tracking-[2px] font-semibold'>{t('account.modelProvider.modelType.localModel')}</span>
      </div>
    )
  }

  if (provider.icon_large) {
    const url = getPurifyHref(`${provider.icon_large[language] || provider.icon_large.en_US}`)
    const src = encodeURI(`${url}?_token=${encodeURIComponent(localStorage.getItem('console_token') as string)}`)
    return (
      <img
        alt='provider-icon'
        src={src}
        className={`!w-auto h-6 ${className}`}
      />
    )
  }

  return (
    <div className={`inline-flex items-center ${className}`}>
      <div className='text-xs font-semibold text-black'>
        {provider.label[language] || provider.label.en_US}
      </div>
    </div>
  )
}

export default ProviderIcon
