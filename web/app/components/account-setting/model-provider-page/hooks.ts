import {
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react'
import useSWR, { useSWRConfig } from 'swr'
import { useContext } from 'use-context-selector'
import type {
  CustomConfigurationModelFixedFields,
  DefaultModel,
  DefaultModelResponse,
  Model,
  ModelProvider,
  ModelTypeEnum,
} from './declarations'
import {
  ConfigurationMethodEnum,
  CustomConfigurationStatusEnum,
  ModelStatusEnum,
} from './declarations'

import I18n from '@/context/i18n'
import {
  fetchDefaultModal,
  fetchModelList,
  fetchModelProviderCredentials,
  fetchModelProviders,
} from '@/service/common'
import { useProviderContext } from '@/context/provider-context'

type UseDefaultModelAndModelList = (
  defaultModel: DefaultModelResponse | undefined,
  modelList: Model[],
) => [DefaultModel | undefined, (model: DefaultModel) => void]

export const useSystemDefaultModelAndModelList: UseDefaultModelAndModelList = (
  defaultModel,
  modelList,
) => {
  const currentDefaultModel = useMemo(() => {
    const currentProvider = modelList.find(provider => provider.provider === defaultModel?.provider.provider)
    const currentModel = currentProvider?.models.find(model => model.model === defaultModel?.model)
    const currentDefaultModel = currentProvider && currentModel && {
      model: currentModel.model,
      provider: currentProvider.provider,
    }

    return currentDefaultModel
  }, [defaultModel, modelList])
  const [defaultModelState, setDefaultModelState] = useState<DefaultModel | undefined>(currentDefaultModel)
  const handleDefaultModelChange = useCallback((model: DefaultModel) => {
    setDefaultModelState(model)
  }, [])
  useEffect(() => {
    setDefaultModelState(currentDefaultModel)
  }, [currentDefaultModel])

  return [defaultModelState, handleDefaultModelChange]
}
export const useLanguage = () => {
  const { locale } = useContext(I18n)
  return locale.replace('-', '_')
}
// 获取model provider设置表单默认值
export const useProviderCredentialsAndLoadBalancing = (
  provider: ModelProvider,
  configType: ConfigurationMethodEnum,
  currentCustomConfigurationModelFixedFields?: CustomConfigurationModelFixedFields,
  model?: any,
) => {
  const configured = provider.custom_configuration.status === CustomConfigurationStatusEnum.active

  /* 获取公有云供应商默认值 */
  const { data: predefinedFormSchemasValue, mutate: mutatePredefined } = useSWR(
    (configType === ConfigurationMethodEnum.predefinedModel && configured)
      ? `/workspaces/current/model-providers/${provider.provider}/credentials`
      : null,
    fetchModelProviderCredentials,
  )
  /* 获取本地模型默认值 */
  const { data: customFormSchemasValue, mutate: mutateCustomized } = useSWR(
    (configType === ConfigurationMethodEnum.customizableModel && currentCustomConfigurationModelFixedFields)
      ? `/workspaces/current/model-providers/${provider.provider}/models/credentials?model=${currentCustomConfigurationModelFixedFields?.__model_name}&model_type=${currentCustomConfigurationModelFixedFields?.__model_type}`
      : null,
    fetchModelProviderCredentials,
  )
  const { data: personalModelFormSchemasValue, mutate: mutatePersonalModel } = useSWR(
    (configType === ConfigurationMethodEnum.personalModel && configured)
      ? `/workspaces/current/personal-model-providers/${provider.provider}/models/credentials?model=${model.model}&model_type=${model.model_type}`
      : null,
    fetchModelProviderCredentials,
  )
  const credentials = useMemo(() => {
    return configType === ConfigurationMethodEnum.predefinedModel
      ? predefinedFormSchemasValue?.credentials
      : customFormSchemasValue?.credentials
        ? {
          ...customFormSchemasValue?.credentials,
          ...currentCustomConfigurationModelFixedFields,
        }
        : configType === ConfigurationMethodEnum.personalModel
          ? personalModelFormSchemasValue
          : undefined
  }, [configType, currentCustomConfigurationModelFixedFields, customFormSchemasValue?.credentials, predefinedFormSchemasValue?.credentials, personalModelFormSchemasValue])
  const mutate = useMemo(() => () => {
    mutatePredefined()
    mutateCustomized()
    mutatePersonalModel()
  }, [mutateCustomized, mutatePredefined, mutatePersonalModel])
  return {
    credentials,
    loadBalancing: (configType === ConfigurationMethodEnum.predefinedModel
      ? predefinedFormSchemasValue
      : customFormSchemasValue
    )?.load_balancing,
    mutate,
  }
}
// 获取模型供应商
export const useModelProviders = () => {
  const { data: providersData, mutate, isLoading } = useSWR('/workspaces/current/model-providers', fetchModelProviders)
  return {
    data: providersData?.data || [],
    mutate,
    isLoading,
  }
}
// 获取模型列表
export const useModelList = (type: ModelTypeEnum) => {
  const { data, mutate, isValidating } = useSWR(`/workspaces/current/models/model-types/${type}`, fetchModelList)

  return {
    data: data?.data || [],
    mutate,
    isValidating,
  }
}
// 查找当前模型类型的默认模型
export const useDefaultModel = (type: ModelTypeEnum) => {
  const { data, mutate, isLoading } = useSWR(`/workspaces/current/default-model?model_type=${type}`, fetchDefaultModal)

  return {
    data: data?.data,
    mutate,
    isLoading,
  }
}
// 获取当前provider和模型
export const useCurrentProviderAndModel = (modelList: Model[], defaultModel?: DefaultModel) => {
  const currentProvider = modelList.find(provider => provider.provider === defaultModel?.provider)
  const currentModel = currentProvider?.models.find(model => model.model === defaultModel?.model)

  return {
    currentProvider,
    currentModel,
  }
}

export const useTextGenerationCurrentProviderAndModelAndModelList = (defaultModel?: DefaultModel) => {
  const { textGenerationModelList, mutateTextGenerationModelList, isTextGenerationModelListLoading } = useProviderContext()
  const activeTextGenerationModelList = textGenerationModelList.filter(model => model.status === ModelStatusEnum.active)
  const {
    currentProvider,
    currentModel,
  } = useCurrentProviderAndModel(textGenerationModelList, defaultModel)

  return {
    currentProvider,
    currentModel,
    textGenerationModelList,
    mutateTextGenerationModelList,
    isTextGenerationModelListLoading,
    activeTextGenerationModelList,
  }
}

export const useModelListAndDefaultModel = (type: ModelTypeEnum) => {
  const { data: modelList, mutate: mutateModelList, isValidating: isModelListLoading } = useModelList(type)
  const { data: defaultModel } = useDefaultModel(type)

  return {
    modelList,
    mutateModelList,
    isModelListLoading,
    defaultModel,
  }
}

export const useModelListAndDefaultModelAndCurrentProviderAndModel = (type: ModelTypeEnum) => {
  const { modelList, mutateModelList, isModelListLoading, defaultModel } = useModelListAndDefaultModel(type)
  const { currentProvider, currentModel } = useCurrentProviderAndModel(
    modelList,
    { provider: defaultModel?.provider.provider || '', model: defaultModel?.model || '' },
  )

  return {
    modelList,
    mutateModelList,
    isModelListLoading,
    defaultModel,
    currentProvider,
    currentModel,
  }
}

// 更新不同类型的模型列表
export const useUpdateModelList = () => {
  const { mutate } = useSWRConfig()
  const updateModelList = useCallback((type: ModelTypeEnum) => {
    mutate(`/workspaces/current/models/model-types/${type}`)
  }, [mutate])
  return updateModelList
}
// 更新模型供应商
export const useUpdateModelProviders = () => {
  const { mutate } = useSWRConfig()

  const updateModelProviders = useCallback(() => {
    mutate('/workspaces/current/model-providers')
  }, [mutate])

  return updateModelProviders
}
// 更新个人模型
export const useUpdatePersonalModelProviders = () => {
  const { mutate } = useSWRConfig()

  const updatePersonalModelProviders = useCallback(() => {
    mutate('/workspaces/current/personal-model-providers')
  }, [mutate])

  return updatePersonalModelProviders
}
