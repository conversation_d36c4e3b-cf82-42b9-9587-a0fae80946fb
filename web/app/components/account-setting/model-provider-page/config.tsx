import type { ModelProvider } from './declarations'
import { ConfigurationMethodEnum, CurrentSystemQuotaTypeEnum, CustomConfigurationStatusEnum, FormTypeEnum, ModelTypeEnum, PreferredProviderTypeEnum, ProviderLabel } from './declarations'

export const MODEL_PROVIDER_QUOTA_GET_PAID = ['anthropic', 'openai', 'azure_openai']

export const DEFAULT_BACKGROUND_COLOR = '#F3F4F6'

// 默认本地localProvider
export const DEFAULT_LOCAL_PROVIDER: ModelProvider = {
  provider: 'localai',
  label: {
    zh_Hans: 'Ollama',
    en_US: 'Ollama',
  },
  icon_small: {
    zh_Hans: '/assets/avatar/localai.svg',
    en_US: '/assets/avatar/localai.svg',
  },
  icon_large: {
    zh_Hans: '/assets/avatar/localai.svg',
    en_US: '/assets/avatar/localai.svg',
  },
  supported_model_types: [
    ModelTypeEnum.textGeneration,
    ModelTypeEnum.textEmbedding,
    ModelTypeEnum.rerank,
  ],
  configurate_methods: [
    ConfigurationMethodEnum.customizableModel,
  ],
  model_credential_schema: {
    model: {
      label: {
        zh_Hans: '模型名称',
        en_US: 'Model Name',
      },
      placeholder: {
        zh_Hans: '输入模型名称',
        en_US: 'Enter your model name',
      },
    },
    credential_form_schemas: [
      {
        variable: 'completion_type',
        label: {
          zh_Hans: 'Completion type',
          en_US: 'Completion type',
        },
        type: FormTypeEnum.select,
        required: false,
        default: 'chat_completion',
        options: [
          {
            label: {
              zh_Hans: '补全',
              en_US: 'Completion',
            },
            value: 'completion',
            show_on: [],
          },
          {
            label: {
              zh_Hans: '对话',
              en_US: 'ChatCompletion',
            },
            value: 'chat_completion',
            show_on: [],
          },
        ],
        placeholder: {
          zh_Hans: '选择对话类型',
          en_US: 'Select completion type',
        },
        max_length: 0,
        show_on: [
          {
            variable: '__model_type',
            value: 'llm',
          },
        ],
      },
      {
        variable: 'server_url',
        label: {
          zh_Hans: '服务器URL',
          en_US: 'Server url',
        },
        type: FormTypeEnum.textInput,
        required: true,
        default: '',
        options: [],
        placeholder: {
          zh_Hans: '在此输入LocalAI的服务器地址，如 http://192.168.1.100:8080',
          en_US: 'Enter the url of your LocalAI, e.g. http://192.168.1.100:8080',
        },
        max_length: 0,
        show_on: [],
      },
      {
        variable: 'context_size',
        label: {
          zh_Hans: '上下文大小',
          en_US: 'Context size',
        },
        type: FormTypeEnum.textInput,
        required: false,
        default: '',
        options: [],
        placeholder: {
          zh_Hans: '输入上下文大小',
          en_US: 'Enter context size',
        },
        max_length: 0,
        show_on: [
          {
            variable: '__model_type',
            value: 'llm',
          },
        ],
      },
    ],
  },
  preferred_provider_type: PreferredProviderTypeEnum.custom,
  custom_configuration: {
    status: CustomConfigurationStatusEnum.active,
  },
  system_configuration: {
    enabled: false,
    current_quota_type: CurrentSystemQuotaTypeEnum.free,
    quota_configurations: [],
  },
  provider_label: ProviderLabel.Local,
}
