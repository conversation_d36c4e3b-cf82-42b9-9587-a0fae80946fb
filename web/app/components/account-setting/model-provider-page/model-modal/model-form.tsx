import type { FC } from 'react'
import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Input, Radio, Switch } from 'antd'
import type {
  CredentialFormSchema,
  CredentialFormSchemaNameInput,
  CredentialFormSchemaNumberInput,
  CredentialFormSchemaRadio,
  CredentialFormSchemaSecretInput,
  CredentialFormSchemaSelect,
  CredentialFormSchemaTextInput,
  FormValue,
} from '../declarations'
import { FormTypeEnum } from '../declarations'
import { useLanguage } from '../hooks'
import cn from '@/utils/classnames'
import Avatar from '@/app/components/base/avatar'
import Select from '@/app/components/base/select'
type ModelFormProps = {
  className?: string
  itemClassName?: string
  value: FormValue
  onChange: (val: FormValue) => void
  formSchemas: CredentialFormSchema[]
  showOnVariableMap: Record<string, string[]>
  isEditMode: boolean
  readonly?: boolean
  isShowDefaultValue?: boolean
  fieldMoreInfo?: (payload: CredentialFormSchema) => JSX.Element | null
}

const ModelForm: FC<ModelFormProps> = ({
  className,
  itemClassName,
  value,
  onChange,
  formSchemas,
  showOnVariableMap,
  isEditMode,
  readonly,
  isShowDefaultValue = false,
  fieldMoreInfo,
}) => {
  const { t } = useTranslation()
  const language = useLanguage()
  // 表单实例
  const [form] = Form.useForm()

  useEffect(() => {
    const val = Object.assign({}, value)
    formSchemas.forEach((formSchema) => {
      const { variable } = formSchema
      const useDefaultValue = isShowDefaultValue && ((val[variable] as string) === '' || val[variable] === undefined || val[variable] === null)
      const newValue = useDefaultValue ? formSchema.default : val[variable]
      // 工具配置boolean类型的数据有的是数字0 1 有的是字符串true false，这里先统一转换成bool 待会提交表单时再按照原来的类型转换回去
      if (formSchema.type === 'boolean') {
        // 额外处理‘false’字符串
        val[variable] = newValue === 'false' ? false : Boolean(newValue)
      }
      else {
        val[variable] = newValue
      }
    })
    form.setFieldsValue(val)
  }, [form, formSchemas, isShowDefaultValue, value])

  // 变更表单值事件
  const handleFormChange = (key: string, val: string | number | boolean) => {
    if (isEditMode && (key === '__model_type' || key === '__model_name'))
      return
    const shouldClearVariable: Record<string, string | undefined> = {}
    if (showOnVariableMap[key]?.length) {
      showOnVariableMap[key].forEach((clearVariable) => {
        shouldClearVariable[clearVariable] = undefined
      })
    }
    onChange({ ...value, [key]: val, ...shouldClearVariable })
  }

  const renderField = (formSchema: CredentialFormSchema) => {
    /* 提示信息 */
    const tooltip = formSchema.tooltip
    const tooltipContent = (tooltip && (tooltip[language] || tooltip.en_US))
    const {
      variable,
      label,
      required,
      show_on,
    } = formSchema
    // 是否禁用
    const disabled = readonly || (isEditMode && (variable === '__model_type' || variable === '__model_name'))
    // 控制表单项展示
    if (show_on.length && !show_on.every(showOnItem => value[showOnItem.variable] === showOnItem.value))
      return null

    // 当表单组件为输入框、搜索输入、文本输入时
    if (formSchema.type === FormTypeEnum.textInput || formSchema.type === FormTypeEnum.secretInput || formSchema.type === FormTypeEnum.textNumber) {
      console.log('formSchema', formSchema)
      const {
        placeholder,
      } = formSchema as (CredentialFormSchemaTextInput | CredentialFormSchemaSecretInput)

      return (
        <Form.Item
          key={variable}
          className={itemClassName}
          required={required}
          label={
            <div className='flex justify-between w-full'>
              {label[language] || label.en_US}
              {fieldMoreInfo && fieldMoreInfo(formSchema)}
            </div>
          }
        >
          <Form.Item
            noStyle
            name={variable}
            validateTrigger='onBlur'
            validateFirst={true}
            rules={[
              {
                required,
                whitespace: required,
                message: t('common.validate.emptyError', {
                  name: label[language] || label.en_US,
                }) as string,
                ...(formSchema.type === FormTypeEnum.textNumber ? { type: 'number' } : {}),
              },
            ]}
          >
            <Input
              onChange={val => handleFormChange(variable, val.target.value)}
              placeholder={placeholder?.[language] || placeholder?.en_US}
              disabled={disabled}
              type={formSchema.type === FormTypeEnum.textNumber ? 'number' : 'text'}
              {...(formSchema.type === FormTypeEnum.textNumber ? { min: (formSchema as CredentialFormSchemaNumberInput).min, max: (formSchema as CredentialFormSchemaNumberInput).max } : {})}
            />
          </Form.Item>
        </Form.Item>
      )
    }
    // 当表单组件为名称输入框
    if (formSchema.type === FormTypeEnum.nameInput) {
      const {
        icon_default,
        icon_variable,
        placeholder,
      } = formSchema as CredentialFormSchemaNameInput
      return (
        <Form.Item
          key={variable}
          className={itemClassName}
          label={label[language] || label.en_US}
          required
        >
          <div className='flex items-center'>
            <Avatar
              className='mr-2 !rounded'
              avatar={icon_default}
              size={36}
              version='old'
              showUpload={true}
              onChange={value => handleFormChange(icon_variable, value)}
            ></Avatar>
            <Form.Item
              validateTrigger='onBlur'
              label={label[language] || label.en_US}
              rules={[
                {
                  required: true,
                  whitespace: true,
                },
              ]}
              noStyle
              name={variable}
            >
              <Input
                onChange={val => handleFormChange(variable, val.target.value)}
                placeholder={placeholder?.[language] || placeholder?.en_US}
                disabled={disabled}
                type='text'
              />
            </Form.Item>
          </div>
        </Form.Item>
      )
    }
    // 当表单组件为radio时
    if (formSchema.type === FormTypeEnum.radio) {
      const { options } = formSchema as CredentialFormSchemaRadio
      const radioOptions = options.filter((option) => {
        if (option.show_on.length)
          return option.show_on.every(showOnItem => value[showOnItem.variable] === showOnItem.value)
        return true
      }).map((option) => {
        return {
          label: option.label[language] || option.label.en_US,
          value: option.value,
          disabled,
        }
      })

      return (
        <Form.Item
          key={variable}
          className={cn(itemClassName)}
          label={label[language] || label.en_US}
          name={variable}
          tooltip={tooltipContent}
          rules={[{ required }]}
        >
          <Radio.Group
            options={radioOptions}
            onChange={val => handleFormChange(variable, val.target.value)}
          ></Radio.Group>
        </Form.Item>
      )
    }
    // 当表单组件为select时
    if (formSchema.type === 'select') {
      const {
        options,
        placeholder,
      } = formSchema as CredentialFormSchemaSelect
      const selectOptions = options.filter((option) => {
        if (option.show_on.length)
          return option.show_on.every(showOnItem => value[showOnItem.variable] === showOnItem.value)

        return true
      }).map(option => ({ value: option.value, label: option.label[language] || option.label.en_US }))

      return (
        <Form.Item
          key={variable} className={itemClassName}
          label={label[language] || label.en_US}
          name={variable}
          tooltip={tooltipContent}
          validateFirst={true}
          rules={[{ required }]}
        >
          <Select
            disabled={readonly}
            options={selectOptions}
            onChange={val => handleFormChange(variable, val)}
            placeholder={placeholder?.[language] || placeholder?.en_US}
          />
        </Form.Item>
      )
    }
    // 当表单组件为布尔型选择时
    if (formSchema.type === 'boolean') {
      return (
        <Form.Item
          key={variable}
          className={itemClassName}
          label={label[language] || label.en_US}
          name={variable}
          tooltip={tooltipContent}
          layout='horizontal'
          validateFirst={true}
          rules={[{ required }]}
        >
          {/* 工具配置default可能是0，1或者true，false，但是设值的时候统一设置为0，1 */}
          <Switch
            onChange={(checked) => {
              handleFormChange(variable, checked ? 1 : 0)
            }}
          />
        </Form.Item>
      )
    }
  }

  return (
    <Form form={form} layout='vertical' className={className}>
      {
        formSchemas.map(formSchema => renderField(formSchema))
      }
    </Form>
  )
}

export default React.memo(ModelForm)
