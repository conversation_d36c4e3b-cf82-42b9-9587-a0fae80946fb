import type { FC } from 'react'
import {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import type {
  CredentialFormSchema,
  CredentialFormSchemaNameInput,
  CredentialFormSchemaRadio,
  CredentialFormSchemaSelect,
  CustomConfigurationModelFixedFields,
  FormValue,
  ModelProvider,
} from '../declarations'
import {
  ConfigurationMethodEnum,
  FormTypeEnum,
} from '../declarations'

import {
  genModelNameFormSchema,
  genModelTypeFormSchema,
  isLocalProvider,
  isOfficalProvider,
  saveCredentials,
  savePersonal
} from '../utils'
import {
  useLanguage,
  useProviderCredentialsAndLoadBalancing,
} from '../hooks'
import Form from './model-form'
import { useAppContext } from '@/context/app-context'
// 公共组件
import Button from '@/app/components/base/button'
import { Lock01 } from '@/app/components/base/icons/src/vender/solid/others'
import Toast, { useToastContext } from '@/app/components/base/toast'
import Modal from '@/app/components/base/modal'
import TextButton from '@/app/components/base/button/text-button'
import { LinkExternal02 } from '@/app/components/base/icons/src/vender/line/general'
import { validateEmpty } from '@/utils/validate'

type ModelModalProps = {
  provider: ModelProvider
  configType: ConfigurationMethodEnum
  currentCustomConfigurationModelFixedFields?: CustomConfigurationModelFixedFields
  model?: any
  onCancel: () => void
  onSave: () => void
  children?: React.ReactNode
}

const ModelModal: FC<ModelModalProps> = ({
  provider,
  configType,
  currentCustomConfigurationModelFixedFields,
  model,
  onCancel,
  onSave,
  children,
}) => {
  const { t } = useTranslation()
  const { notify } = useToastContext()
  const language = useLanguage()
  // 是否为当前工作空间管理者
  const { canAdmin } = useAppContext()
  // 是否正在加载
  const [loading, setLoading] = useState(false)
  // 表单值
  const [value, setValue] = useState<Record<string, string | number>>({})

  // 是否为预定义模型
  const isPredefine = configType === ConfigurationMethodEnum.predefinedModel || configType === ConfigurationMethodEnum.personalModel
  // 表单初始值
  const {
    credentials: formSchemasValue,
    mutate,
  } = useProviderCredentialsAndLoadBalancing(
    provider,
    configType,
    currentCustomConfigurationModelFixedFields,
    model
  )
  // 是否可编辑
  const isEditMode = !!formSchemasValue && canAdmin
  // 表单项
  const formSchemas = useMemo(() => {
    return isPredefine
      ? (provider.provider_credential_schema?.credential_form_schemas || [])
      : [
        genModelTypeFormSchema(provider.supported_model_types),
        genModelNameFormSchema(provider.model_credential_schema?.model),
        ...provider.model_credential_schema.credential_form_schemas,
      ]
  }, [provider, isPredefine])
  // 必填、默认、条件展示表单项
  const [
    requiredFormSchemas,
    defaultFormSchemaValue,
    showOnVariableMap,
  ] = useMemo(() => {
    const requiredFormSchemas: CredentialFormSchema[] = []
    const defaultFormSchemaValue: Record<string, string | number> = {}
    const showOnVariableMap: Record<string, string[]> = {}

    formSchemas.forEach(async (formSchema) => {
      if (formSchema.required)
        requiredFormSchemas.push(formSchema)
      if (formSchema.type === FormTypeEnum.nameInput)
        defaultFormSchemaValue[(formSchema as CredentialFormSchemaNameInput).icon_variable] = ((formSchema as CredentialFormSchemaNameInput).icon_default)
      if (formSchema.default)
        defaultFormSchemaValue[formSchema.variable] = formSchema.default
      if (formSchema.show_on.length) {
        formSchema.show_on.forEach((showOnItem) => {
          if (!showOnVariableMap[showOnItem.variable])
            showOnVariableMap[showOnItem.variable] = []

          if (!showOnVariableMap[showOnItem.variable].includes(formSchema.variable))
            showOnVariableMap[showOnItem.variable].push(formSchema.variable)
        })
      }
      // 如果是选项或者radio，还要对选项进行show_on处理
      if (formSchema.type === FormTypeEnum.select || formSchema.type === FormTypeEnum.radio) {
        (formSchema as (CredentialFormSchemaRadio | CredentialFormSchemaSelect)).options.forEach((option) => {
          if (option.show_on.length) {
            option.show_on.forEach((showOnItem) => {
              if (!showOnVariableMap[showOnItem.variable])
                showOnVariableMap[showOnItem.variable] = []

              if (!showOnVariableMap[showOnItem.variable].includes(formSchema.variable))
                showOnVariableMap[showOnItem.variable].push(formSchema.variable)
            })
          }
        })
      }
    })
    return [
      requiredFormSchemas,
      defaultFormSchemaValue,
      showOnVariableMap,
    ]
  }, [formSchemas])
  // 初始化表单值
  const initialFormSchemasValue: Record<string, string | number> = useMemo(() => {
    return {
      ...defaultFormSchemaValue,
      ...formSchemasValue,
    } as unknown as Record<string, string | number>
  }, [formSchemasValue, defaultFormSchemaValue])
  const extendedSecretFormSchemas = useMemo(
    () =>
      (isPredefine
        ? (provider.provider_credential_schema?.credential_form_schemas || [])
        : [
          genModelTypeFormSchema(provider.supported_model_types),
          genModelNameFormSchema(provider.model_credential_schema?.model),
          ...provider.model_credential_schema.credential_form_schemas,
        ]).filter(({ type }) => type === FormTypeEnum.secretInput),
    [provider, isPredefine],
  )

  // 然后每次默认值变化重新设置值
  useEffect(() => {
    setValue(initialFormSchemasValue)
  }, [initialFormSchemasValue])

  // 这个show_on估计是要要求其他的值也要设置成某些固定的，所以设计的这个样子
  const filteredRequiredFormSchemas = requiredFormSchemas.filter((requiredFormSchema) => {
    if (requiredFormSchema.show_on.length && requiredFormSchema.show_on.every(showOnItem => value[showOnItem.variable] === showOnItem.value))
      return true

    if (!requiredFormSchema.show_on.length)
      return true

    return false
  })
  // 编码秘密值
  const encodeSecretValues = useCallback((v: FormValue) => {
    const result = { ...v }
    extendedSecretFormSchemas.forEach(({ variable }) => {
      if (result[variable] === formSchemasValue?.[variable] && result[variable] !== undefined)
        result[variable] = '[__HIDDEN__]'
    })
    return result
  }, [extendedSecretFormSchemas, formSchemasValue])

  // 保存模型或provider认证
  const handleSave = async () => {
    try {
      let res
      setLoading(true)
      if(model){
       res= await savePersonal(model, provider.provider, encodeSecretValues(value))
      }else{
        res = await saveCredentials(
        isPredefine,
        provider.provider,
        encodeSecretValues(value),
        isEditMode,
        {
          enabled: false,
          configs: [],
        },
      )
      }
      if (res.result === 'success') {
        notify({ type: 'success', message: isEditMode ? t('common.actionMsg.modifiedSuccessfully') : t('common.actionMsg.addedSuccessfully') })
        mutate()
        onSave()
        onCancel()
      }
    }
    finally {
      setLoading(false)
    }
  }
  // 工具函数——渲染表单标题
  const renderTitle = () => {
    const prefix = isEditMode ? t('common.operation.edit') : t('common.operation.add')
    return isLocalProvider(provider) ? `${prefix}${t('account.modelProvider.modelType.localModel')}` : (isOfficalProvider(provider) ? `${prefix}${t('account.modelProvider.modelType.officalModel')}` : `${prefix}${t('account.modelProvider.modelType.cloudModel')}`)
  }
  // 工具函数——渲染表单描述
  const renderDescription = () => {
    return isLocalProvider(provider)
      ? (
        <Toast
          type='info'
          className='!static !my-0'
          message={t('account.modelProvider.localModelTip')}
        ></Toast>
      )
      : null
  }
  return (
    <Modal
      isShow
      closable
      onClose={onCancel}
      className='!w-[600px] form-modal'
      title={renderTitle()}
      description={renderDescription()}
      footer={
        <>
          <div className='flex justify-between items-center w-full mb-9'>
            {
              (provider.help && (provider.help.title || provider.help.url))
                ? (
                  <TextButton className='!text-start' onClick={() => {
                    if (provider.help!.url)
                      window.open(provider.help?.url[language] || provider.help?.url.en_US, '_blank')
                  }}>
                    <span className='whitespace-pre-wrap'>{provider.help.title?.[language] || provider.help.url[language] || provider.help.title?.en_US || provider.help.url.en_US}</span>
                    <LinkExternal02 className='w-4 h-4' />
                  </TextButton>
                )
                : <div />
            }
            <div className='flex gap-2 ml-[40px] shrink-0'>
              <Button
                variant='secondary-accent'
                onClick={onCancel}
              >
                {t('common.operation.cancel')}
              </Button>
              <Button
                variant='primary'
                onClick={handleSave}
                disabled={
                  loading
              || filteredRequiredFormSchemas.some((item) => {
                return (
                  value[item.variable] === undefined || validateEmpty(value[item.variable] as unknown as string)
                )
              })
                }
              >
                {t('common.operation.save')}
              </Button>
            </div>
          </div>
          {/* 注意事项 */}
          <div className='absolute w-full bottom-0 left-0 flex justify-center items-center py-2 bg-gray-G10 text-S3 leading-H1 text-gray-G3'>
            <Lock01 className='mr-1 w-[14px] h-[14px] text-gray-G4' />
            {t('account.modelProvider.encrypted.front')}
            <a
              className='!text-primary-P1 mx-1'
              target='_blank' rel='noopener noreferrer'
              href='https://pycryptodome.readthedocs.io/en/latest/src/cipher/oaep.html'
            >
                  PKCS1_OAEP
            </a>
            {t('account.modelProvider.encrypted.back')}
          </div>
        </>
      }
    >
      {children}
      <Form
        value={value}
        onChange={setValue}
        formSchemas={formSchemas}
        showOnVariableMap={showOnVariableMap}
        isEditMode={isEditMode}
      />
    </Modal>
  )
}

export default memo(ModelModal)
