/* 模型提供商 */
.provider-group-title {
  @apply text-gray-G2 text-S3 leading-H3 mb-3;
  display: inline-block;
}
.provider-item {
  border-radius: 6px;
  border: 1px solid rgba(217, 220, 227, 0.20);
  opacity: 0.8;
  background: linear-gradient(328deg, rgba(22, 119, 255, 0.02) 20.64%, rgba(255, 255, 255, 0.00) 64.68%), #FFF;
  box-shadow: 0px 1px 1px 0px rgba(159, 176, 201, 0.20);
}
.provider-item-header {
  @apply p-4 border-b border-gray-G6 ;
}
.provider-item-content {
  @apply py-2;
}
.provider-item-collapse {
  @apply px-4 flex items-center justify-between text-gray-G2 !text-S1 !leading-H1;
}

/* 添加模型供应商 */
.add-provider-btn {
  @apply text-gray-G1 text-S4 leading-H4 flex items-center h-[64px] p-4 mb-3;
  cursor: pointer;
  border-radius: 6px;
  border: 1px solid rgba(217, 220, 227, 0.20);
  opacity: 0.8;
  background: linear-gradient(328deg, rgba(22, 119, 255, 0.02) 20.64%, rgba(255, 255, 255, 0.00) 64.68%), #FFF;
  box-shadow: 0px 1px 1px 0px rgba(159, 176, 201, 0.20);
  font-weight: 600;
}
.add-provider-btn:hover {
  border: 1px solid #4491FA;
  background: linear-gradient(182deg, rgba(49, 104, 245, 0.15) 2.7%, rgba(255, 255, 255, 0.08) 50.93%, rgba(255, 255, 255, 0.00) 99.16%), linear-gradient(328deg, rgba(22, 119, 255, 0.02) 20.64%, rgba(255, 255, 255, 0.00) 64.68%), #FFF;
  box-shadow: 0px 3px 8px 0px rgba(159, 176, 201, 0.50);
}
.add-provider-btn-icon {
  @apply bg-gray-G6.5 rounded flex items-center justify-center mr-2 w-8 h-8 !text-gray-G3;
}