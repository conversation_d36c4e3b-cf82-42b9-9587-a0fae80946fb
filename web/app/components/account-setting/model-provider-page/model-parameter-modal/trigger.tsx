import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { RiArrowDownSLine } from '@remixicon/react'
import type {
  Model,
  ModelItem,
  ModelProvider,
} from '../declarations'
import { MODEL_STATUS_TEXT } from '../declarations'
import { useLanguage } from '../hooks'
import ModelIcon from '../model-icon'
import ModelName from '../model-name'
import cn from '@/utils/classnames'
import { useProviderContext } from '@/context/provider-context'
import { Slider } from '@/app/components/base/icons/src/vender/line/action'
import { WarningTriangle } from '@/app/components/base/icons/src/vender/line/tip'
import Tooltip from '@/app/components/base/tooltip'

export type TriggerProps = {
  open?: boolean
  disabled?: boolean
  currentProvider?: ModelProvider | Model
  currentModel?: ModelItem
  providerName?: string
  modelId?: string
  hasDeprecated?: boolean
  modelDisabled?: boolean
  isInWorkflow?: boolean
}
const Trigger: FC<TriggerProps> = ({
  disabled,
  currentProvider,
  currentModel,
  providerName,
  modelId,
  hasDeprecated,
  modelDisabled,
  isInWorkflow,
}) => {
  const { t } = useTranslation()
  const language = useLanguage()
  const { modelProviders } = useProviderContext()

  return (
    <div
      className={cn(
        'relative flex items-center w-full px-[10px] py-2 h-9 rounded cursor-pointer border border-gray-G5 bg-transparent hover:border-primary-P1 hover:bg-primary-P4 text-S1 leading-H1 font-normal',
        !isInWorkflow && '',
        !isInWorkflow && (disabled ? 'bg-gray-G10' : ''),
        isInWorkflow && 'pr-[30px]',
      )}
    >
      {
        currentProvider && (
          <ModelIcon
            className='mr-1 !w-5 !h-5'
            provider={currentProvider}
            modelName={currentModel?.model}
            model={currentModel}
          />
        )
      }
      {
        !currentProvider && (
          <ModelIcon
            className='mr-1 !w-5 !h-5'
            provider={modelProviders.find(item => item.provider === providerName)}
            modelName={modelId}
          />
        )
      }
      {
        currentModel && (
          <ModelName
            className='mr-2 text-gray-G2'
            modelItem={currentModel}
            showMode
            modeClassName={cn(!isInWorkflow ? '!text-[#444CE7] !border-[#A4BCFD]' : '!text-gray-G2 !border-black/8')}
            showFeatures
            featuresClassName={cn(!isInWorkflow ? '!text-[#444CE7]' : '!text-gray-G2')}
          />
        )
      }
      {
        !currentModel && (
          <div className='mr-2 truncate text-S1 text-gray-G2'>
            {modelId}
          </div>
        )
      }
      {
        disabled
          ? (
            <Tooltip
              popupContent={
                hasDeprecated
                  ? t('account.modelProvider.deprecated')
                  : (modelDisabled && currentModel)
                    ? MODEL_STATUS_TEXT[currentModel.status as string][language]
                    : ''
              }
            >
              <WarningTriangle className='w-4 h-4 text-[#F79009]' />
            </Tooltip>
          )
          : (
            <Tooltip popupContent={t('account.modelProvider.configureModel')}>
              <Slider className={cn('text-gray-G3 hover:text-gray-G1 shrink-0 w-4 h-4 cursor-pointer')} />
            </Tooltip>
          )
      }
      {isInWorkflow && (<RiArrowDownSLine className='absolute top-[9px] right-2 w-3.5 h-3.5 text-gray-G2' />)}
    </div>
  )
}

export default Trigger
