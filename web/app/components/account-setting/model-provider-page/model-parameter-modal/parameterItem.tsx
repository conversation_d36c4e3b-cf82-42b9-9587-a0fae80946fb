import type { FC } from 'react'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Input, InputNumber, Radio, Switch } from 'antd'
import type { InputNumberProps, RadioChangeEvent } from 'antd'

import type { ModelParameterRule } from '../declarations'
import { useLanguage } from '../hooks'
import { isNullOrUndefined } from '../utils'
import cn from '@/utils/classnames'
import Slider from '@/app/components/base/slider'
import TagInput from '@/app/components/base/tag-input'
import Select from '@/app/components/base/select'
import { AdvancedParamModeEnum, BasicParamModeEnum } from '@/types/model'
export type ParameterValue = number | string | string[] | boolean | undefined

type ParameterItemProps = {
  parameterRule: ModelParameterRule
  value?: ParameterValue
  onChange?: (value: ParameterValue) => void
  className?: string
  // onSwitch?: (checked: boolean, assignValue: ParameterValue) => void
  isInWorkflow?: boolean
  openAdvancedParamMode: boolean
  advancedParamMode?: string
  disabled?: boolean
  basicParamMode?: string
  onBasicParamModeChange?: (value: RadioChangeEvent) => void
}
const ParameterItem: FC<ParameterItemProps> = ({
  parameterRule,
  value,
  onChange,
  className,
  // onSwitch,
  isInWorkflow,
  openAdvancedParamMode,
  advancedParamMode,
  disabled = false,
  basicParamMode,
  onBasicParamModeChange,
}) => {
  const language = useLanguage()
  const { t } = useTranslation()
  const basicParamModeOptions = [
    { value: 'normal', label: t('account.modelProvider.paramMode.normal'), style: { marginRight: 0 } },
    { value: 'precise', label: t('account.modelProvider.paramMode.precise'), style: { marginRight: 0 } },
    { value: 'creative', label: t('account.modelProvider.paramMode.creative'), style: { marginRight: 0 } },
    { value: 'custom', label: t('account.modelProvider.paramMode.custom'), style: { marginRight: 0 } },
  ]
  // const [localValue, setLocalValue] = useState(value)
  // 输入框的值
  const [inputValue, setInputValue] = useState<number>()
  // 生成多样性（温度）默认取常规模式default_normal的值，其他参数按原逻辑取default值
  const getDefaultValue = () => {
    let defaultValue: ParameterValue
    const dufaultKey = parameterRule.name === 'temperature' ? `default_${BasicParamModeEnum.normal}` : 'default'
    if (parameterRule.type === 'int' || parameterRule.type === 'float')
      defaultValue = isNullOrUndefined(parameterRule[dufaultKey]) ? (parameterRule.min || 0) : parameterRule[dufaultKey]
    else if (parameterRule.type === 'string' || parameterRule.type === 'text')
      defaultValue = parameterRule.options?.length ? (parameterRule[dufaultKey] || '') : (parameterRule[dufaultKey] || '')
    else if (parameterRule.type === 'boolean')
      defaultValue = !isNullOrUndefined(parameterRule[dufaultKey]) ? parameterRule[dufaultKey] : false
    else if (parameterRule.type === 'tag')
      defaultValue = !isNullOrUndefined(parameterRule[dufaultKey]) ? parameterRule[dufaultKey] : []

    return defaultValue
  }

  // 渲染值，默认参数模型，只展示默认值
  // eslint-disable-next-line no-mixed-operators
  const renderValue = parameterRule.name !== 'temperature' && advancedParamMode === AdvancedParamModeEnum.normal ? getDefaultValue() : value ?? getDefaultValue()
  // 模型参数值变更
  const handleInputChange = (newValue: ParameterValue) => {
    // setLocalValue(newValue)
    // if (onChange && (parameterRule.name === 'stop' || !isNullOrUndefined(value) || parameterRule.required))
    if (onChange)
      onChange(newValue)
  }
  // 数字输入框变更
  const handleNumberInputChange: InputNumberProps['onChange'] = (value) => {
    if (value === null)
      return
    let num = +value

    if (!isNullOrUndefined(parameterRule.max) && num > parameterRule.max!) {
      num = parameterRule.max as number
      setInputValue(num)
    }

    if (!isNullOrUndefined(parameterRule.min) && num < parameterRule.min!)
      num = parameterRule.min as number

    handleInputChange(num)
    setInputValue(num)
  }
  // 滑块值变更
  const handleSlideChange = (num: number) => {
    if (!isNullOrUndefined(parameterRule.max) && num > parameterRule.max!) {
      handleInputChange(parameterRule.max)
      setInputValue(parameterRule.max)
      return
    }

    if (!isNullOrUndefined(parameterRule.min) && num < parameterRule.min!) {
      handleInputChange(parameterRule.min)
      setInputValue(parameterRule.min)
      return
    }

    handleInputChange(num)
    setInputValue(num)
  }
  // 单选按钮变更
  const handleRadioChange = (checked: boolean) => {
    handleInputChange(checked)
  }
  // 字符串输入框变更
  const handleStringInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    handleInputChange(e.target.value)
  }
  // 下拉框变更
  const handleSelect = (value: string) => {
    handleInputChange(value)
  }
  // 标签变更
  const handleTagChange = (newSequences: string[]) => {
    handleInputChange(newSequences)
  }
  // 变更开关
  // const handleSwitch = (checked: boolean) => {
  //   if (onSwitch) {
  //     const assignValue: ParameterValue = localValue || getDefaultValue()

  //     onSwitch(checked, assignValue)
  //   }
  // }

  useEffect(() => {
    if ((parameterRule.type === 'int' || parameterRule.type === 'float'))
      setInputValue(Number(renderValue))
  }, [renderValue, value])

  const renderInput = () => {
    const numberInputWithSlide = (parameterRule.type === 'int' || parameterRule.type === 'float')
      && !isNullOrUndefined(parameterRule.min)
      && !isNullOrUndefined(parameterRule.max)
    if (parameterRule.type === 'int' || parameterRule.type === 'float') {
      const getStep = (val: number | undefined) => {
        if (!val)
          return 0.1
        if (val < 10)
          return 0.1
        else if (val < 100)
          return 1
        else if (val < 1000)
          return 10
        else if (val < 10000)
          return 100
        else
          return 100
      }
      let step = 100
      if (parameterRule.max)
        step = getStep(parameterRule.max)

      return (
        <div className='flex items-center justify-between'>
          {numberInputWithSlide && <Slider
            wrapClassName='w-[300px]'
            value={renderValue as number}
            min={parameterRule.min}
            max={parameterRule.max}
            step={step}
            onChange={handleSlideChange}
            disabled={disabled}
          />}
          <InputNumber
            value={inputValue}
            max={parameterRule.max}
            min={parameterRule.min}
            step={numberInputWithSlide ? step : parameterRule.precision ? `0.${parameterRule.precision}` : getStep(inputValue)}
            onChange={handleNumberInputChange}
            placeholder=''
            className='shrink-0'
            disabled={disabled}
          />
        </div>
      )
    }
    if (parameterRule.type === 'boolean') {
      return (
        <Switch value={!!renderValue} onChange={handleRadioChange} disabled={disabled} />
      )
    }
    if (parameterRule.type === 'string' && !parameterRule.options?.length) {
      return (
        <Input
          className={cn(isInWorkflow ? 'w-[200px]' : 'w-full', 'ml-4')}
          value={renderValue as string}
          onChange={handleStringInputChange}
          placeholder=''
          disabled={disabled}
        />
      )
    }
    if (parameterRule.type === 'text') {
      return (
        <textarea
          className='w-full h-20 ml-4 px-1 rounded-lg bg-gray-100 outline-none text-[12px] text-gray-900'
          value={renderValue as string}
          onChange={handleStringInputChange}
          disabled={disabled}
        />
      )
    }

    if (parameterRule.type === 'string' && !!parameterRule?.options?.length) {
      return (
        <Select
          className={cn(isInWorkflow ? '!w-[200px]' : 'w-full')}
          value={renderValue as string}
          onChange={handleSelect}
          options={parameterRule.options.map(option => ({ value: option, label: option }))}
          disabled={disabled}/>
      )
    }

    if (parameterRule.type === 'tag') {
      return (
        <div className={cn(isInWorkflow ? 'w-[200px]' : 'w-full', 'ml-4')}>
          <TagInput
            items={renderValue as string[]}
            onChange={handleTagChange}
            customizedConfirmKey='Tab'
            isInWorkflow={isInWorkflow}
          />
        </div>
      )
    }

    return null
  }

  // 当为渲染多样性时
  if (parameterRule.name === 'temperature') {
    return (
      <>
        {/* 生成多样性滑块 */}
        <Form.Item
          layout='vertical'
          label={parameterRule.label[language] || parameterRule.label.en_US}
          tooltip={parameterRule.help ? (parameterRule.help[language] || parameterRule.help.en_US) : ''}
        >
          {renderInput()}
        </Form.Item>
        {/* 参数模式 */}
        {basicParamMode && <Form.Item layout='vertical' label={t('account.modelProvider.info.paramMode')}>
          <Radio.Group options={basicParamModeOptions} className='flex justify-between' value={basicParamMode} onChange={onBasicParamModeChange} />
        </Form.Item>}
      </>
    )
  }
  else if (openAdvancedParamMode) {
    return (
      <Form.Item
        layout='vertical'
        label={parameterRule.label[language] || parameterRule.label.en_US}
        tooltip={parameterRule.help ? (parameterRule.help[language] || parameterRule.help.en_US) : ''}
      >
        {renderInput()}
      </Form.Item>

    )
  }
}

export default ParameterItem
