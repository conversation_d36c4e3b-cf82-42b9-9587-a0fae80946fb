import type { FC } from 'react'
import { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { RiArrowDownSLine } from '@remixicon/react'
import Dropdown from '@/app/components/base/dropdown'
import { Slider } from '@/app/components/base/icons/src/vender/line/action'
import { Brush01 } from '@/app/components/base/icons/src/vender/solid/editor'
import { Scales02 } from '@/app/components/base/icons/src/vender/solid/others'
import { Target04 } from '@/app/components/base/icons/src/vender/solid/general'
import { TONE_LIST } from '@/config'

type PresetsParameterProps = {
  onSelect: (toneId: number) => void
}
const PresetsParameter: FC<PresetsParameterProps> = ({
  onSelect,
}) => {
  const { t } = useTranslation()
  const renderTrigger = useCallback((open: boolean) => {
    return (
      <div
        className={`
          flex items-center px-2 h-7 rounded border border-gray-G5 shadow-xs
          text-xs font-semibold text-gray-G1 cursor-pointer
          ${open && 'bg-gray-100'}
        `}
      >
        <Slider className='mr-[5px] w-4 h-4 text-gray-G2' />
        {t('account.modelProvider.loadPresets')}
        <RiArrowDownSLine className='ml-0.5 w-4 h-4 text-gray-G2' />
      </div>
    )
  }, [])
  const getToneIcon = (toneId: number) => {
    const className = 'mr-2 w-[14px] h-[14px]'
    const res = ({
      1: <Brush01 className={`${className} text-[#6938EF]`} />,
      2: <Scales02 className={`${className} text-indigo-600`} />,
      3: <Target04 className={`${className} text-[#107569]`} />,
    })[toneId]
    return res
  }
  const options = TONE_LIST.slice(0, 3).map((tone) => {
    return {
      value: tone.id,
      text: (
        <div className='flex items-center h-full'>
          {getToneIcon(tone.id)}
          {t(`account.model.tone.${tone.name}`) as string}
        </div>
      ),
    }
  })

  return (
    <Dropdown
      renderTrigger={renderTrigger}
      items={options}
      onSelect={item => onSelect(item.value as number)}
      popupClassName='z-[1003]'
    />
  )
}

export default PresetsParameter
