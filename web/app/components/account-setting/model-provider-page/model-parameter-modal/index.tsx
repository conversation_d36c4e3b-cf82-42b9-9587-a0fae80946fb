import type {
  FC,
  ReactNode,
} from 'react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { Form, Switch } from 'antd'
import useSWR from 'swr'
import { useTranslation } from 'react-i18next'
import type {
  DefaultModel,
  FormValue,
  ModelParameterRule,
} from '../declarations'
import { ModelStatusEnum } from '../declarations'
import ModelSelector from '../model-selector'
import {
  useTextGenerationCurrentProviderAndModelAndModelList,
} from '../hooks'
import ParameterItem from './parameterItem'
import type { ParameterValue } from './parameterItem'
import Trigger from './trigger'
import type { TriggerProps } from './trigger'
import { AdvancedParamModeEnum, BasicParamModeEnum } from '@/types/model'
import type { ModelConfigStorage } from '@/types/model'
import cn from '@/utils/classnames'
import { fetchModelParameterRules } from '@/service/common'
import { useProviderContext } from '@/context/provider-context'
import Button from '@/app/components/base/button'
import Radio from '@/app/components/base/radio'
import PopoverModal from '@/app/components/base/modal/popover-modal'

export type ModelParameterModalProps = {
  popupClassName?: string
  portalToFollowElemContentClassName?: string
  triggerClassName?: string
  isAdvancedMode: boolean
  mode: string
  modelId: string
  provider: string
  setModel: (model: { modelId: string; provider: string; mode?: string; features?: string[] }) => void
  basicParamMode?: string
  completionParams: FormValue
  onCompletionParamsChange: (newParams: FormValue, useStore?: boolean) => void
  onModelParamsModeChange: (key: string, value: any) => void
  getModelConfigStoraged: (provider: string, modelId: string) => Promise<ModelConfigStorage>
  hideDebugWithMultipleModel?: boolean
  debugWithMultipleModel?: boolean
  onDebugWithMultipleModelChange?: () => void
  renderTrigger?: (v: TriggerProps) => ReactNode
  readonly?: boolean
  isInWorkflow?: boolean
}
const stopParameterRule: ModelParameterRule = {
  default: [],
  help: {
    en_US: 'Up to four sequences where the API will stop generating further tokens. The returned text will not contain the stop sequence.',
    zh_Hans: '最多四个序列，API 将停止生成更多的 token。返回的文本将不包含停止序列。',
  },
  label: {
    en_US: 'Stop sequences',
    zh_Hans: '停止序列',
  },
  name: 'stop',
  required: false,
  type: 'tag',
  tagPlaceholder: {
    en_US: 'Enter sequence and press Tab',
    zh_Hans: '输入序列并按 Tab 键',
  },
}

const ModelParameterModal: FC<ModelParameterModalProps> = ({
  popupClassName,
  triggerClassName,
  isAdvancedMode,
  modelId,
  provider,
  setModel,
  basicParamMode,
  completionParams,
  onCompletionParamsChange,
  onModelParamsModeChange,
  getModelConfigStoraged,
  debugWithMultipleModel,
  onDebugWithMultipleModelChange,
  renderTrigger,
  readonly,
  isInWorkflow,
}) => {
  const { t } = useTranslation()
  const { isAPIKeySet } = useProviderContext()
  const { data: parameterRulesData, isLoading } = useSWR((provider && modelId) ? `/workspaces/current/model-providers/${provider}/models/parameter-rules?model=${modelId}` : null, fetchModelParameterRules)
  const {
    currentProvider,
    currentModel,
    activeTextGenerationModelList,
    mutateTextGenerationModelList,
    isTextGenerationModelListLoading,
  } = useTextGenerationCurrentProviderAndModelAndModelList(
    { provider, model: modelId },
  )

  // 模型配置弹窗是否打开
  const [open, setOpen] = useState(false)
  const [advancedParamMode, setAdvancedParamMode] = useState<AdvancedParamModeEnum>(AdvancedParamModeEnum.normal)
  const [openAdvancedParamMode, setOpenAdvancedParamMode] = useState(false)

  // 是否已经废弃
  const hasDeprecated = !currentProvider || !currentModel
  // 模型是否禁用
  const modelDisabled = currentModel?.status !== ModelStatusEnum.active
  // 是否禁用
  const disabled = !isAPIKeySet || hasDeprecated || modelDisabled

  // 接口提供的模型参数规则，包括默认值、帮助信息等
  const parameterRules: ModelParameterRule[] = useMemo(() => {
    return parameterRulesData?.data || []
  }, [parameterRulesData])
  // 模型参数值变更
  const handleParamChange = (key: string, value: ParameterValue) => {
    onCompletionParamsChange({
      ...completionParams,
      [key]: value,
    }, true)
  }
  // 切换模型
  const handleChangeModel = ({ provider, model }: DefaultModel) => {
    const targetProvider = activeTextGenerationModelList.find(modelItem => modelItem.provider === provider)
    const targetModelItem = targetProvider?.models.find(modelItem => modelItem.model === model)
    setModel({
      modelId: model,
      provider,
      mode: targetModelItem?.model_properties.mode as string,
      features: targetModelItem?.features || [],
    })
  }
  // 切换生成多样性模式,切换到自定义模式时初始值取default
  const changeBasicParamMode = (targetMode: string) => {
    const key = `default_${targetMode}` as keyof ModelParameterRule
    const temperatureParam = parameterRules.find(parameter => parameter.name === 'temperature')
    if (temperatureParam && key in temperatureParam)
      handleParamChange('temperature', temperatureParam[key] as ParameterValue)

    else
      handleParamChange('temperature', temperatureParam?.default)

    if (onModelParamsModeChange)
      onModelParamsModeChange('basicParamMode', targetMode)
  }

  const getStoragedConfig = useCallback(async () => {
    const config = await getModelConfigStoraged(provider, modelId)
    // 优先取数据库缓存，不然取传参
    if (config) {
      setAdvancedParamMode(config?.advancedParamMode ? config.advancedParamMode : AdvancedParamModeEnum.normal)
      setOpenAdvancedParamMode(config?.openAdvancedParamMode || false)
    }
    else {
      const customAdvancedParamMode = Object.keys(completionParams).filter(item => item !== 'temperature' && item !== 'stop').length > 0
      setOpenAdvancedParamMode(customAdvancedParamMode)
      // 模型生成多样性手动赋值常规模式默认值
      const temperatureDefault = parameterRules.find(item => item.name === 'temperature')?.default_normal as ParameterValue
      if (temperatureDefault)
        handleParamChange('temperature', temperatureDefault)

      setAdvancedParamMode(customAdvancedParamMode ? AdvancedParamModeEnum.custom : AdvancedParamModeEnum.normal)
    }
  }, [provider, modelId, parameterRules])

  // 从数据库初始化模型参数
  useEffect(() => {
    getStoragedConfig()
  }, [provider, modelId, parameterRules])

  // 高级参数配置 默认参数/自定义 切换
  const changeAdvancedParamMode = async (v: AdvancedParamModeEnum) => {
    const config = await getModelConfigStoraged(provider, modelId)
    const defaultParams = {} as Record<string, ParameterValue>
    parameterRules.filter(item => item.name !== 'temperature').forEach((parameter) => {
      defaultParams[parameter.name] = parameter.default
    })
    if (v === AdvancedParamModeEnum.custom) {
      onCompletionParamsChange({
        // ...defaultParams,
        ...config?.params,
      }, true)
    }
    else {
      const params = {} as Record<string, ParameterValue>
      if ('temperature' in completionParams)
        params.temperature = completionParams.temperature
      onCompletionParamsChange(params, false)
    }
    setAdvancedParamMode(v)
    onModelParamsModeChange('advancedParamMode', v)
  }

  // 打开/关闭高级参数配置开关
  const handleOpenAdvancedParamMode = useCallback(async (v: boolean) => {
    const config = await getModelConfigStoraged(provider, modelId)
    // 关闭时，清空配置的参数(保留数据库缓存的参数)
    if (!v) {
      const params = {} as Record<string, ParameterValue>
      if ('temperature' in completionParams)
        params.temperature = completionParams.temperature
      onCompletionParamsChange(params, false)
    }
    else {
      changeAdvancedParamMode(config?.advancedParamMode ? config.advancedParamMode : AdvancedParamModeEnum.normal)
    }
    setOpenAdvancedParamMode(v)
    onModelParamsModeChange('openAdvancedParamMode', v)
  }, [openAdvancedParamMode, setOpenAdvancedParamMode, onModelParamsModeChange, onCompletionParamsChange])

  useEffect(() => {
    mutateTextGenerationModelList()
  }, [])

  return (
    <PopoverModal
      title={t('account.modelProvider.modelConfig')}
      open={open}
      arrow={false}
      trigger={'click'}
      loading={isLoading}
      disabled={readonly}
      onOpenChange={(v) => {
        setOpen(v)
      }}
      className={cn(popupClassName, 'max-h-[480px] w-[496px]')}
      placement={isInWorkflow ? 'bottom' : 'bottom'}
      content={(
        <>
          {/* 模型选择 */}
          <Form.Item layout='vertical' label={t('account.modelProvider.modelSelect').toLocaleUpperCase()}>
            <ModelSelector
              loading={isTextGenerationModelListLoading}
              defaultModel={(provider || modelId) ? { provider, model: modelId } : undefined}
              modelList={activeTextGenerationModelList}
              onSelect={handleChangeModel}
              triggerClassName='w-full'
              onFetch={mutateTextGenerationModelList}
            />
          </Form.Item>
          {/* 渲染多样性（温度） */}
          {
            !isLoading && !!parameterRules.length && (
              [
                ...parameterRules,
                // ...(isAdvancedMode ? [stopParameterRule] : []),
              ].filter((parameter) => {
                return parameter.name === 'temperature'
              }).map(parameter => (
                <ParameterItem
                  key={`${modelId}-${parameter.name}`}
                  openAdvancedParamMode={openAdvancedParamMode}
                  parameterRule={parameter}
                  value={completionParams?.[parameter.name]}
                  onChange={v => handleParamChange(parameter.name, v)}
                  // onSwitch={(checked, assignValue) => handleSwitch(parameter.name, checked, assignValue)}
                  isInWorkflow={isInWorkflow}
                  disabled={basicParamMode !== BasicParamModeEnum.custom}
                  basicParamMode={basicParamMode}
                  onBasicParamModeChange={(e) => { changeBasicParamMode(e.target.value) }}
                />
              ))
            )
          }
          {/* 过滤了渲染多样性 */}
          {
            !isLoading && !!parameterRules.filter(item => item.name !== 'temperature').length
              && (
                <Form.Item
                  layout='vertical'
                  label={
                    <div className='flex items-center'>
                      <span>{t('account.modelProvider.info.paramConfig')!}</span>
                      <Switch
                        className='ml-1'
                        value={openAdvancedParamMode}
                        onChange={handleOpenAdvancedParamMode}
                        size='small'
                      />
                    </div>
                  }
                >
                  {
                    openAdvancedParamMode && <div className='p-3 border'>
                      <Radio.Group
                        className='flex items-center justify-center mb-3'
                        value={advancedParamMode}
                        onChange={changeAdvancedParamMode}
                      >
                        <Radio value={AdvancedParamModeEnum.normal} className='w-[50%] justify-center'>{t('account.modelProvider.info.defaultParam')!}</Radio>
                        <Radio value={AdvancedParamModeEnum.custom} className='w-[50%] justify-center'>{t('account.modelProvider.info.customParam')!}</Radio>
                      </Radio.Group>
                      {
                        [
                          ...parameterRules,
                          ...(isAdvancedMode ? [stopParameterRule] : []),
                        ].filter((parameter) => {
                          return parameter.name !== 'temperature'
                        }).map(parameter =>
                          (
                            <ParameterItem
                              key={`${modelId}-${parameter.name}`}
                              openAdvancedParamMode={openAdvancedParamMode}
                              advancedParamMode={advancedParamMode}
                              parameterRule={parameter}
                              value={completionParams?.[parameter.name]}
                              onChange={v => handleParamChange(parameter.name, v)}
                              // onSwitch={(checked, assignValue) => handleSwitch(parameter.name, checked, assignValue)}
                              isInWorkflow={isInWorkflow}
                              disabled={advancedParamMode === AdvancedParamModeEnum.normal}
                            />
                          ))
                      }
                    </div>
                  }
                </Form.Item>
              )
          }
        </>
      )}
      footer={
        !debugWithMultipleModel && (
          <Button variant="primary" onClick={() => onDebugWithMultipleModelChange?.()}>
            {t('appDebug.debugAsMultipleModel')}
          </Button>
        )}
    >

      {
        renderTrigger
          ? renderTrigger({
            open,
            disabled,
            modelDisabled,
            hasDeprecated,
            currentProvider,
            currentModel,
            providerName: provider,
            modelId,
          })
          : (
            <Trigger
              disabled={disabled}
              isInWorkflow={isInWorkflow}
              modelDisabled={modelDisabled}
              hasDeprecated={hasDeprecated}
              currentProvider={currentProvider}
              currentModel={currentModel}
              providerName={provider}
              modelId={modelId}
            />
          )
      }
    </PopoverModal>
  )
}

export default ModelParameterModal
