import type { FC, PropsWithChildren } from 'react'
import {
  modelTypeFormat,
  sizeFormat,
} from '../utils'
import { useLanguage } from '../hooks'
import type { ModelItem } from '../declarations'
import FeatureIcon from './feature-icon'
// 公共组件
import Tag from '@/app/components/base/tag'
import classNames from '@/utils/classnames'

type ModelNameProps = PropsWithChildren<{
  modelItem: ModelItem
  className?: string
  showModelType?: boolean
  modelTypeClassName?: string
  showMode?: boolean
  modeClassName?: string
  showFeatures?: boolean
  featuresClassName?: string
  showContextSize?: boolean
  tagStyle?: string
}>
const ModelName: FC<ModelNameProps> = ({
  modelItem,
  className,
  showModelType,
  modelTypeClassName,
  showMode,
  modeClassName,
  showFeatures,
  featuresClassName,
  showContextSize,
  children,
  tagStyle = 'ml-[6px] px-1',
}) => {
  const language = useLanguage()

  if (!modelItem)
    return null
  return (
    <div
      className={`
        flex items-center truncate text-S1 leading-H1 
        ${className}
      `}
    >
      <div
        className='truncate'
        title={modelItem.label[language] || modelItem.label.en_US}
      >
        {modelItem.model_label || modelItem.label[language] || modelItem.label.en_US}
      </div>
      {
        showModelType && modelItem.model_type && (
          <Tag color='blue' bordered size='small' className={classNames(tagStyle, modelTypeClassName)}>{modelTypeFormat(modelItem.model_type)}</Tag>
        )
      }
      {
        modelItem.model_properties.mode && showMode && (
          <Tag color='blue' bordered size='small' className={classNames(tagStyle, modeClassName)}>{(modelItem.model_properties.mode as string).toLocaleUpperCase()}</Tag>
        )
      }
      {
        showFeatures && modelItem.features?.map(feature => (
          <FeatureIcon
            key={feature}
            feature={feature}
            className={featuresClassName}
          />
        ))
      }
      {
        showContextSize && modelItem.model_properties.context_size && (
          <Tag color='blue' bordered size='small' className={tagStyle}>{sizeFormat(modelItem.model_properties.context_size as number)}</Tag>
        )
      }
      {children}
    </div>
  )
}

export default ModelName
