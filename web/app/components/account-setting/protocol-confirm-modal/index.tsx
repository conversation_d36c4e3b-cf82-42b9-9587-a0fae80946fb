import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Checkbox } from 'antd'
import s from './styles/index.module.css'
import './styles/index.css'
// 公共组件
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import { APP_NAME } from '@/config'
import { confirmAgreement } from '@/service/common'

const ProtocolConfirmModal = ({
  onConfirm,
}: {
  onConfirm: () => void
}) => {
  const { t } = useTranslation()

  // 同意协议1
  const [agree1, setAgree1] = useState<boolean>(false)
  // 同意协议2
  const [agree2, setAgree2] = useState<boolean>(false)

  // 立即体验
  const handleExperienceNow = async () => {
    await confirmAgreement()
    onConfirm()
  }

  return (
    <Modal
      isShow
      className='!w-[786px] protocol-confirm-modal'
      closable={false}
    >
      <div className={s['modal-content']}>
        {/* logo部分 */}
        <div className={s.logo}/>
        <img alt='' className={'!h-[44px]'} src='/logo/logo-protocol.svg' width={349}></img>
        {/* 描述 */}
        <div className={s.desc}>{t('account.protocol.desc')}</div>
        {/* 立即体验 */}
        <Button onClick={handleExperienceNow} disabled={!agree1 || !agree2} className={s['experience-btn']} variant='primary'>{t('account.protocol.experienceNow')}</Button>
        {/* 协议列表 */}
        <div className={s['checkbox-item']}>
          <Checkbox className='cursor-pointer mr-2' checked={agree1} onChange={() => setAgree1(!agree1)}></Checkbox>
          <span>{t('account.protocol.agree')}</span>
          <a
            className='text-primary-P1 hover:text-primary-P1 !underline hover:underline'
            href='https://k36drdpyul.feishu.cn/docx/Ejyzd8Cu7oKtt5xFehocad7mnbM'
            target="_blank"
            rel="noopener noreferrer"
          >{`《${APP_NAME}${t('account.protocol.userProtocol')}》`}</a>
        </div>
        <div className={s['checkbox-item']}>
          <Checkbox className='cursor-pointer mr-2' checked={agree2} onChange={() => setAgree2(!agree2)}></Checkbox>
          <span>{t('account.protocol.agree')}</span>
          <a
            className='text-primary-P1 hover:text-primary-P1 !underline hover:underline'
            href='https://k36drdpyul.feishu.cn/docx/Ew2sdR7HbooW8AxdfBAcItGTnYb'
            target="_blank"
            rel="noopener noreferrer"
          >{`《${APP_NAME}${t('account.protocol.privacyPolicy')}》`}</a>
        </div>
      </div>

    </Modal>
  )
}

export default ProtocolConfirmModal
