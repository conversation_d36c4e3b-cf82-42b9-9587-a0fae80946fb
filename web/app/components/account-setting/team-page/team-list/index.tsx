'use client'
import type { FC } from 'react'
import React, { useCallback, useEffect, useState } from 'react'
import { useBoolean } from 'ahooks'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import type { TableColumnsType } from 'antd'
import { Progress, Table } from 'antd'

import { useContext } from 'use-context-selector'
import s from '../style.module.css'
import RenameModal from './rename-modal'
// import DetailModal from './detail-modal'
// import OperationAction from './operation'

import useTimestamp from '@/hooks/use-timestamp'
import { deleteDocument } from '@/service/datasets'
import { DataSourceType, type SimpleDocumentDetail } from '@/models/datasets'
import StatusItem from '@/app/components/datasets/common/status-indicator'
import { getDocStatus } from '@/app/components/datasets/utils'
import { deleteTeam, deleteTeamExit } from '@/service/team'

// 公共的工具和方法
import cn from '@/utils/classnames'
import { formatFileSize, formatNumber2 } from '@/utils/format'
import { Edit } from '@/app/components/base/icons/src/vender/line/general'
import FileIcon from '@/app/components/base/file-icon'
import Confirm from '@/app/components/base/confirm'
import { asyncRunSafe } from '@/utils'
import { ToastContext } from '@/app/components/base/toast'
import TextButton from '@/app/components/base/button/text-button'
import Tooltip from '@/app/components/base/tooltip'

type LocalDoc = SimpleDocumentDetail & { percent?: number }
type IDocumentListProps = {
  embeddingAvailable: boolean
  documents: LocalDoc[]
  datasetId?: string
  onUpdate: () => void
  onDelete: () => void
  tableType?: string
  onViewData?: () => void
  currPage: number
  pageSize: number
  rowKey?: (record: LocalDoc) => string
}

const TeamList: FC<IDocumentListProps> = ({ 
  embeddingAvailable, 
  documents = [], 
  datasetId, 
  onUpdate, 
  onDelete,
  tableType,
  onViewData,
  currPage,
  pageSize,
  rowKey = 'id',
}) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()
  const { notify } = useContext(ToastContext)
  const router = useRouter()
  // 列表
  const [localDocs, setLocalDocs] = useState<LocalDoc[]>(documents)
  // 当前选中团队
  const [currDocument, setCurrDocument] = useState<LocalDoc | null>(null)
  // 是否显示重命名弹窗
  const [isShowRenameModal, {
    setTrue: setShowRenameModalTrue,
    setFalse: setShowRenameModalFalse,
  }] = useBoolean(false)

  // 是否显示知识库详情弹窗
  const [showDetailModal, setShowDetailModal] = useState(false)

  // 是否显示退出团队弹窗
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  // 解散团队校验
  // const [disbandCheck, setDisbandCheck] = useState(false)
  // 解散团队校验:不符合
  const [noShowDisbandModal, setNoShowDisbandModal] = useState(false)
  // 解散团队校验:符合
  const [showDisbandModal, setShowDisbandModal] = useState(false)

  // 显示重命名弹窗
  const showRenameModal = useCallback((doc: LocalDoc) => {
    setCurrDocument(doc)
    setShowRenameModalTrue()
  }, [setShowRenameModalTrue])
  // 查看团队成员列表
  const openDetailModal = (data: LocalDoc) => {
    onViewData && onViewData(data)
    if (data?.role)
      router.push(`/account-setting/team-page/${data.id}/member-list?role=${data?.role}&team_name=${data?.name}`) 
    else
      router.push(`/account-setting/team-page/${data.id}/member-list?team_name=${data?.name}`)
  }
  // 打开退出团队弹窗
  const openDeleteModal = useCallback((doc: LocalDoc) => {
    setCurrDocument(doc)
    setShowDeleteModal(true)
  }, [])
  // 退出团队
  const deleteDataset = async () => {
    // TODO: 退出团队接口  deleteTeamExit
    // console.log(currDocument, '===退出团队===')
    try {
      const res = await deleteTeamExit({team_id: currDocument.id || ''})
      if (res?.code === 200) {
        setShowDeleteModal(false)
        onDelete()
        notify({
          type: 'success',
          message: res?.message,
        })
      }
      // notify({ type: 'success', message: res?.message })
    } catch (e) {
      // console.log(e)
    }
  }
  
  // 解散团队 deleteTeam
  const openDisbandModal = useCallback(async (doc: LocalDoc) => {
    setCurrDocument(doc)
    setShowDisbandModal(true)
    // console.log(doc, '===解散团队===')
  }, [])

  // 解散团队
  const disbandTeam = async () => {
    try {
      const res = await deleteTeam({team_id: currDocument.id || ''})
      if (res?.code === 200) {
        // 符合解散逻辑，解散
        setShowDisbandModal(false)
        onDelete()
        notify({
          type: 'success',
          message: res?.message,
        })
      }
      else {
        // 不符合解散逻辑
        setNoShowDisbandModal(true)
      }
      // notify({ type: 'success', message: res?.message })
    } catch (e) {
      // console.log(e)
    }
  }

  useEffect(() => {
    setLocalDocs(documents)
  }, [documents])

  const columns: TableColumnsType<LocalDoc> = [
    // {
    //   title: '',
    //   key: 'test',
    //   width: 20,
    // },
    {
      title: t('common.info.serial'),
      key: 'position',
      width: 80,
      dataIndex: 'position',
      render: (_: any, record, index) => {
        return (currPage * 1 - 1) * pageSize * 1 + (index * 1 + 1)
      }
    },
    // 团队名称以及重命名弹窗 
    {
      title: t('account.team.teamName'),
      key: 'name',
      render: (_: any, record) => {
        return (
          <Tooltip
            popupContent = {record.name}
          >
            <div className='group flex items-center'>
              <span className={`${s.tdValue} max-w-[160px]`}>
                {record.name}
              </span>
              { // 主账号可编辑
                tableType === 'mainAccount' && (
                  <Edit
                    className='w-4 h-4 text-gray-G3 hover:text-gray-G1 cursor-pointer shrink-0 ml-3'
                    onClick={(e) => {
                      e.stopPropagation()
                      showRenameModal(record)
                    }}
                  />
                )
              }
            </div>
          </Tooltip>
        )
      },
    },
    // 创建时间
    {
      title: t('account.team.createTime'),
      sorter: (a, b) => (dayjs(a.created_at).isBefore(dayjs(b.created_at)) ? -1 : 1),
      key: 'uploadTime',
      render: (_: any, record) => {
        return (
          <div className='min-w-[80px]'>
            {/* {formatTime(record.tenant_created_at, t('account.dateTimeFormat') as string)} */}
            {record.tenant_created_at
              ? dayjs.unix(record.tenant_created_at).format(t('account.dateTimeFormat') as string)
              : '--'
            }
          </div>
        )
      },
      // width: 250,
    },
    // 团队创建人
    ...(tableType === 'subAccount'
      ? [
        {
          title: t('account.team.timeCreatePeople'),
          key: 'created_by',
          width: 120,
          render: (_: any, record) => {
            return record.created_by || '--'
          },
        },
        // 加入时间
        {
          title: t('account.team.joinTime'),
          sorter: (a, b) => (dayjs(a.updated_at).isBefore(dayjs(b.updated_at)) ? -1 : 1),
          key: 'uploadTime',
          render: (_: any, record) => {
            return (
              <div className='min-w-[80px]'>
                {record.created_at
                  ? dayjs.unix(record.created_at).format(t('account.dateTimeFormat') as string)
                  : '--'
                }
              </div>
            )
          },
          // width: 250,
        },
        // 团队角色
        {
          title: t('account.team.timeRole'),
          key: 'role',
          width: 120,
          render: (_: any, record) => {
            const val = record?.role || '--'
            switch (val) {
              case 'normal':
                return t('account.role.normal')
              case 'admin':
                return t('account.role.admin')
              case 'owner':
                return t('account.role.owner')
              case 'dataset_operator':
                return t('account.role.datasetOperator')
              default:
                return val
            }
          },
        },
      ]
    :[]),
    // 操作
    ...(embeddingAvailable
      ? [{
        title: t('account.team.operate'),
        render: (_: any, record: LocalDoc) => (
          <div className='flex items-center gap-6' onClick={e => e.stopPropagation()}>
            {
              tableType === 'mainAccount' && (
                <>
                  <TextButton size='middle' type='primary' onClick={() => openDetailModal(record)}>
                    {/* 查看 */}
                    {t('account.team.view')}
                  </TextButton>
                  <TextButton size='middle' type='primary' onClick={() => openDisbandModal(record)}>
                    {/* 解散 */}
                    {t('account.team.disband')}
                  </TextButton>
                </>
              )
            }
            {
              tableType === 'subAccount' && (
                <>
                  <TextButton size='middle' type='primary' onClick={() => openDetailModal(record)}>
                    {/* 查看 */}
                    {t('account.team.view')}
                  </TextButton>
                  <TextButton size='middle' type='primary' onClick={() => openDeleteModal(record)}>
                    {/* 退出 */}
                    {t('account.team.exit')}
                  </TextButton>
                </>
              )
            }

          </div>
        ),
        key: 'operation',
        align: 'left' as any,
        width: 200,
      }]
      : []),
  ]
  
  return (
    <>
      <Table
        size='large'
        columns={columns}
        pagination={{ position: ['none', 'none'], pageSize: 15, showSizeChanger: false }}
        scroll={{ y: 'calc(100vh - 264px)', x: '100%' }}
        dataSource={localDocs}
        className='border-gray-G5 rounded border max-w-[888px]'
        rowClassName='cursor-pointer'
        rowKey={rowKey}
        // onRow={(record) => {
        //   return {
        //     onClick: () => openDetailModal(record),
        //   }
        // }}
      ></Table>
      {/* 编辑名称 */}
      {isShowRenameModal && currDocument && (
        <RenameModal
          documentId={currDocument.id}
          name={currDocument.name}
          onClose={setShowRenameModalFalse}
          onSaved={onUpdate}
          showEditor = {false}
          showDatasetOperator = {false}
        />
      )}
      {/* 解散团队校验:不符合 */}
      {
        noShowDisbandModal && (
          <Confirm
            isShow={noShowDisbandModal}
            title={t('account.team.disbandInfo.disbandPrompt')}
            content={t('account.team.disbandInfo.disbandPromptDesc')}
            type='info'
            showCancel={false}
            showConfirm={false}
            showCancelAssistant={true}
            cancelAssistantTxt={t('account.team.disbandInfo.noDisbandButton')}
            // onConfirm={() => deleteDataset()}
            onCancel={() => setNoShowDisbandModal(false)}
          />
        )
      }
      {/* 解散团队校验:符合 */}
      {
        showDisbandModal && (
          <Confirm
            isShow={showDisbandModal}
            title={t('account.team.disbandInfo.disbandWarn')}
            content={t('account.team.disbandInfo.disbandWarnDesc', { key: currDocument?.name })}
            onConfirm={() => disbandTeam()}
            onCancel={() => setShowDisbandModal(false)}
          />
        )
      }
      {/* 退出团队弹窗 */}
      {showDeleteModal
        && <Confirm
          isShow={showDeleteModal}
          title={t('account.team.disbandInfo.exitWarn')}
          content={t('account.team.disbandInfo.exitWarnDesc')}
          onConfirm={() => deleteDataset()}
          onCancel={() => setShowDeleteModal(false)}
        />
      }
      {/* {showDetailModal && currDocument
       && <DetailModal
         detail={
           {
             originFileName: currDocument!.data_source_detail_dict?.upload_file?.name || '-',
             originFileSize: formatFileSize(currDocument!.data_source_detail_dict?.upload_file?.size || 0).toString(),
             uploadTime: formatTime(currDocument!.created_at, t('datasetHitTesting.dateTimeFormat') as string),
             lastUpdateTime: formatTime(currDocument!.updated_at, t('datasetHitTesting.dateTimeFormat') as string),
           }
         }
         onClose={() => setShowDetailModal(false)}
       ></DetailModal> 
      } */}
    </>
  )
}

export default TeamList
