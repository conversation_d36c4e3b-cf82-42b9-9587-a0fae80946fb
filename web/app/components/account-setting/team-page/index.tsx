'use client'
import { useEffect, useState } from 'react'
import useS<PERSON> from 'swr'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import { useContext } from 'use-context-selector'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { Pagination } from 'antd'
// import InviteModal from './../members-page/invite-modal'
import TeamList from './team-list'
import type { TeamType } from './team-type'
import TeamContext from './team-context'
import CreateModal from '@/app/components/account-setting/team-page/create-modal'
import { fetchMembers } from '@/service/common'
import type { InvitationResult, QueryTeamCreate, TeamDataItemsType } from '@/models/common'
import { Plan } from '@/types/public/billing'
import { useProviderContext } from '@/context/provider-context'
import { useAppContext } from '@/context/app-context'
import I18n from '@/context/i18n'
// import { useDatasetDetailContext } from '@/context/dataset-detail'
import { getTeamList } from '@/service/team'
import { useSWRConfig } from 'swr'

// import type { QueryTeamList } from '@/service/common'

// 公共组件
import SearchInput from '@/app/components/base/input/search-input'
import Loading from '@/app/components/base/loading'
import Button from '@/app/components/base/button'
import { Add } from '@/app/components/base/icons/src/vender/line/action'
import Tooltip from '@/app/components/base/tooltip'

dayjs.extend(relativeTime)

const TeamPage = () => {
  const { t } = useTranslation()
  // const { dataset } = useDatasetDetailContext()
  const router = useRouter()
  const { mutate: globalMutate } = useSWRConfig()
  // 团队列表信息
  const [teamData, setTeamData] = useState<TeamDataItemsType[]>([])
  // 搜索值
  const [searchValue, setSearchValue] = useState<string>('')
  // 当前页码
  const [currPage, setCurrPage] = useState<number>(1)
  // 每页条数
  const [sizeValue, setSizeValue] = useState<number>(15)
  // 总条数
  const [totalValue, setTotalValue] = useState<number>(0)
  // 是否正在加载
  const [isLoading, setIsLoading] = useState(true)
  // 最大上限
  const [maxUpperLimit, setMaxUpperLimit] = useState(100)
  const getTeamData = async () => {
    try {
      const params = {
        team_name: searchValue, // 搜索的团队名称
        page: currPage, //  当前页码，默认为 1
        size: sizeValue, // 每页大小，默认为 15
        search: searchValue, // 关键字搜索（模糊匹配团队名称）
      }
      const res = await getTeamList(params)
      setTeamData(res?.data?.items || [])
      setCurrPage(res?.data?.page || 1)
      setSizeValue(res?.data?.size || 10)
      setTotalValue(res?.data?.total || 0)
      setMaxUpperLimit(res?.data?.max_upper_limit || 100)
      // console.log(res, 'res')
    }
    catch (error) {
      // console.error('获取团队列表失败:', error.message)
    }
    finally {
      setIsLoading(false)
      globalMutate({ url: '/workspaces' }) // 更新工作空间列表 
    }
  }
  useEffect(() => {
    getTeamData()
  }, [searchValue, currPage, sizeValue])

  // 当前创建团队弹窗是否可见
  const [createModalVisible, setCreateModalVisible] = useState(false)
  // 创建结果
  const [createResults, setCreateResults] = useState<QueryTeamCreate[]>([])

  // 创建团队
  const clickCreateTeam = () => {
    setCreateModalVisible(true)
  }

  const { locale } = useContext(I18n)

  const { userProfile, currentWorkspace, canAdmin } = useAppContext()
  const { plan, enableBilling } = useProviderContext()
  // console.log(userProfile, currentWorkspace, canAdmin, plan, enableBilling, '=')

  const { data, mutate } = useSWR({ url: '/workspaces/current/members', params: {} }, fetchMembers)
  // 当前邀请弹窗是否可见
  const [inviteModalVisible, setInviteModalVisible] = useState(false)
  // 邀请结果
  const [invitationResults, setInvitationResults] = useState<InvitationResult[]>([])
  // 当前被邀请完成弹窗是否可见
  const [invitedModalVisible, setInvitedModalVisible] = useState(false)
  // 账号信息
  const accounts = data?.accounts || []
  // 管理者
  const owner = accounts.filter(account => account.role === 'owner')?.[0]?.email === userProfile.email
  const isNotUnlimitedMemberPlan = enableBilling && plan.type !== Plan.team && plan.type !== Plan.enterprise
  const isMemberFull = enableBilling && isNotUnlimitedMemberPlan && accounts.length >= plan.total.teamMembers
  // 查看当前团队信息
  const [teamDataObj, setTeamDataObj] = useState<TeamType>()
  // 是否主或子账号:  mainAccount 主账号 ， subAccount 子账号
  const [tableType, setTableType] = useState<string>('mainAccount')

  // 查看：上下文传值测试
  const onViewData = (data) => {
    setTeamDataObj(data)
  }
  useEffect(() => {
    if (userProfile.is_parent) // is_vip 有无团队权限，is_parent 区分主子账号()
      setTableType('mainAccount')
    else
      setTableType('subAccount')
  }, [teamDataObj, tableType])

  const onUpdate = () => {
    getTeamData()
  }
  const onDelete = () => {
    getTeamData()
  }
  return (
    <TeamContext.Provider value={{ teamDataObj, setTeamDataObj: () => setTeamDataObj }}>
      <div className='flex flex-col'>
        <div className='flex items-center justify-between flex-wrap mb-3'>
          <SearchInput
            className='!w-[360px]'
            value={searchValue}
            onChange={e => setSearchValue(e)}
          />
          <div className='flex gap-2 justify-center items-center'>
            {/* 子账号无创建团队权限 */}
            {
              tableType === 'mainAccount' && (
                <Tooltip
                  popupContent={totalValue >= maxUpperLimit ? t('account.team.createTeamTooltip') : ''}
                >
                  <Button
                    variant='primary'
                    className='shrink-0'
                    // disabled={!canAdmin || isMemberFull}
                    // onClick={() => (canAdmin && !isMemberFull) && setInviteModalVisible(true)}
                    onClick={clickCreateTeam}
                    disabled={totalValue >= maxUpperLimit}
                  >
                    <Add className='h-4 w-4' />
                    <span className='pl-2'>
                      {t('account.team.createTeam')}
                    </span>
                  </Button>
                </Tooltip>
              )
            }
          </div>
        </div>
        {
          isLoading
            ? <Loading type='area' />
            : <TeamList
              embeddingAvailable={true}
              documents={teamData}
              datasetId={''}
              onUpdate={onUpdate}
              onDelete={onDelete}
              onViewData={onViewData}
              tableType={tableType}
              // scroll={{ y: 'calc(100vh - 264px)', x: '100%' }}
              currPage={currPage}
              pageSize={sizeValue}
              rowKey='id'
            />
        }
        <Pagination
          className='mt-3'
          align='end'
          current={currPage}
          hideOnSinglePage
          onChange={page => setCurrPage(page)}
          total={totalValue}
          pageSize={sizeValue}
          showQuickJumper={false}
          showSizeChanger={false}
        />
      </div>
      {/* {
        inviteModalVisible && (
          <InviteModal
            onCancel={() => setInviteModalVisible(false)}
            onSend={(invitationResults) => {
              // setInvitedModalVisible(true)
              setInvitationResults(invitationResults)
              mutate()
            }}
          />
        )
      }
      {
        invitedModalVisible && (
          <InvitedModal
            invitationResults={invitationResults}
            onCancel={() => setInvitedModalVisible(false)}
          />
        )
      } */}
      {/* 创建团队 */}
      {
        createModalVisible && (
          <CreateModal
            title={t('account.team.createTeam')}
            onCancel={() => setCreateModalVisible(false)}
            onSend={(invitationResults) => {
              // setInvitedModalVisible(true)
              setCreateResults(invitationResults)
              getTeamData()// 刷新数据
            }}
            showEditor = {false}
            showDatasetOperator = {false}
          />
        )
      }
    </TeamContext.Provider>
  )
}

export default TeamPage
