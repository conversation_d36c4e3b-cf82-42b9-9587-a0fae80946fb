'use client'
import type { FC } from 'react'
import React, { useCallback, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import type { TableColumnsType } from 'antd'
import { Table } from 'antd'

import { useContext } from 'use-context-selector'
// import RenameModal from '../team-list/rename-modal'
import RoleChangeModal from './role-change'

import useTimestamp from '@/hooks/use-timestamp'
import { type SimpleDocumentDetail } from '@/models/datasets'
import { deleteTeamMember } from '@/service/team'

// 公共的工具和方法
import Confirm from '@/app/components/base/confirm'
import { ToastContext } from '@/app/components/base/toast'
import TextButton from '@/app/components/base/button/text-button'

type LocalDoc = SimpleDocumentDetail & { percent?: number }
type IDocumentListProps = {
  embeddingAvailable: boolean
  documents: LocalDoc[]
  onUpdate: () => void
  onDelete: () => void
  currPage?: number
  pageSize?: number
  teamId?: string
  onUpdateRole?: () => void
  teamRole?: string
  isParent?: boolean
  rowKey?: (record: LocalDoc) => string
}

const DocumentList: FC<IDocumentListProps> = ({
  embeddingAvailable, documents = [], onUpdate, onDelete,
  currPage, pageSize,
  teamId,
  onUpdateRole,
  teamRole = '',
  isParent = true,
  rowKey = 'id'
}) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()
  const { notify } = useContext(ToastContext)
  const router = useRouter()
  // 列表
  const [localDocs, setLocalDocs] = useState<LocalDoc[]>(documents)
  // 当前选中数据
  const [currDocument, setCurrDocument] = useState<LocalDoc | null>(null)
  // 是否显示重命名弹窗
  // const [isShowRenameModal, {
  //   setTrue: setShowRenameModalTrue,
  //   setFalse: setShowRenameModalFalse,
  // }] = useBoolean(false)
  // 是否显示删除弹窗
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  // 是否显示详情弹窗
  const [showDetailModal, setShowDetailModal] = useState(false)
  // 角色变更弹窗
  const [showRoleChangeModal, setShowRoleChangeModal] = useState(false)

  // 显示重命名弹窗
  // const showRenameModal = useCallback((doc: LocalDoc) => {
  //   setCurrDocument(doc)
  //   setShowRenameModalTrue()
  // }, [setShowRenameModalTrue])
  // 打开移除弹窗
  const openDeleteModal = useCallback((doc: LocalDoc) => {
    setCurrDocument(doc)
    setShowDeleteModal(true)
  }, [])
  // 打开角色变更弹窗
  const openRoleChangeModal = useCallback((doc: LocalDoc) => {
    setCurrDocument(doc)
    setShowRoleChangeModal(true)
  }, [])
  // 查看详情
  const showDatasetDetail = (doc: LocalDoc) => {
    // router.push(`/datasets/${datasetId}/documents/${doc.id}`)
  }
  // 删除
  const deleteDataset = async () => {
    try {
      const body = {
        team_id: teamId,
        user_id: currDocument?.account_id || '',
      }
      const response = await deleteTeamMember(body )
      setShowDeleteModal(false)
      if (typeof onUpdateRole === 'function')
        onUpdateRole() // 刷新数据
      if (response?.code === 200)
        notify({ type: 'success', message: response?.message })
    }
    catch (e) {
      console.log(e)
    }
  }

  useEffect(() => {
    setLocalDocs(documents)
  }, [documents])

  const columns: TableColumnsType<LocalDoc> = [
    // {
    //   title: '',
    //   key: 'test',
    //   width: 20,
    // },
    {
      title: t('common.info.serial'),
      key: 'position',
      width: 80,
      dataIndex: 'position',
      render: (_: any, record, index) => {
        return (currPage * 1 - 1) * pageSize * 1 + (index * 1 + 1)
      },
    },
    // 用户ID
    {
      title: t('account.team.table.userID'),
      key: 'account_id',
      render: (_: any, record, index) => {
        return record?.account_id || ''
      },
    },
    // 用户名称
    {
      title: t('account.team.table.userName'),
      key: 'account_name',
      render: (_: any, record, index) => {
        return record?.account_name || ''
      },
      // width: 100,
    },
    // 加入时间
    {
      title: t('account.team.joinTime'),
      sorter: (a, b) => (dayjs(a.created_at).isBefore(dayjs(b.created_at)) ? -1 : 1),
      key: 'uploadTime',
      render: (_: any, record) =>{
        return (
          <div className='min-w-[80px]'>
            {record.created_at
              ? dayjs.unix(record.created_at).format(t('account.dateTimeFormat') as string)
              : '--'
            }
          </div>
        )
      },
      width: 250,
    },
    // 团队角色
    {
      title: t('account.team.timeRole'),
      key: 'role',
      render: (_: any, record) => {
        const val = record?.role || '--'
        switch (val) {
          case 'normal':
            return t('account.role.normal')
          case 'admin':
            return t('account.role.admin')
          case 'owner':
            return t('account.role.owner')
          case 'dataset_operator':
            return t('account.role.datasetOperator')
          default:
            return val
        }
      },
    },
    // 操作
    ...(embeddingAvailable
      ? [{
        title: t('account.team.operate'),
        render: (_: any, record: LocalDoc) => {
          let isDisabled = false
          let isDelete = false
          // 主
          if (isParent && record.role === 'owner') {
            isDisabled = true
            isDelete = true
          }
          else if (!isParent && teamRole === 'normal') {
            // 子：// 子账号成员不能操作 teamRole ==='normal'   isParent ===false 子账号
            isDisabled = true
            isDelete = true
          }
          else if (!isParent && teamRole !== 'normal' && record.role === 'owner') {
            isDisabled = true
            isDelete = true
          }

          return (
            <div className='flex items-center gap-6' onClick={e => e.stopPropagation()}>
              <TextButton
                size='middle'
                type='primary'
                disabled={isDisabled }
                onClick={() => openRoleChangeModal(record)}
              >{t('account.team.table.roleChange')}</TextButton>
              <TextButton 
                size='middle'
                type='primary'
                disabled={isDelete}
                onClick={() => openDeleteModal(record)}
              >{t('account.team.table.delete')}</TextButton>
            </div>
          )
        },
        key: 'operation',
        align: 'left' as any,
        width: 240,
      }]
      : []),
  ]
  return (
    <>
      <Table
        size='large'
        columns={columns}
        pagination={{ position: ['none', 'none'], pageSize: 15, showSizeChanger: false }}
        scroll={{ y: 'calc(100vh - 285px)' }}
        dataSource={localDocs}
        className='border-gray-G5 rounded border'
        rowClassName='cursor-pointer'
        rowKey = {rowKey}
        // onRow={(record) => {
        //   return {
        //     onClick: () => showDatasetDetail(record),
        //   }
        // }}
      ></Table>
      {/* {isShowRenameModal && currDocument && (
        <RenameModal
          datasetId={datasetId}
          documentId={currDocument.id}
          name={currDocument.name}
          onClose={setShowRenameModalFalse}
          onSaved={onUpdate}
        />
      )} */}
      {showDeleteModal
        && <Confirm
          isShow={showDeleteModal}
          title={t('account.team.disbandInfo.removeWarn')}
          content={t('account.team.disbandInfo.removeWarnDesc', { key: currDocument?.name })}
          onConfirm={() => deleteDataset()}
          onCancel={() => setShowDeleteModal(false)}
        />
      }
      {/* 角色变更 */}
      {
        showRoleChangeModal && (
          <RoleChangeModal
            title={t('account.team.table.roleChange')}
            modalData = {currDocument}
            showEditor = {false}
            userNameDisabled ={true}
            showDatasetOperator = {false}
            teamId={teamId}
            onCancel={() => setShowRoleChangeModal(false)}
            onSend={(invitationResults) => {
              setShowRoleChangeModal(false)
              onUpdateRole() // 刷新数据
              // setCreateResults(invitationResults)
              // mutate()// 刷新数据
            }}
          />
        )
      }
    </>
  )
}

export default DocumentList
