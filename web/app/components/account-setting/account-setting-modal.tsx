import Modal from '../base/modal'
import AccountSetting from '.'

type AccountSettingModalProps = {
  activeTab?: string
  onClose: () => void
}

const AccountSettingModal = ({ activeTab, onClose }: AccountSettingModalProps) => {
  return (
    <Modal
      isShow
      closable
      onClose={onClose}
      className="!w-[1000px]"
    >
      <AccountSetting className='!p-0 !h-[calc(80vh-88px)]' activeTab={activeTab}></AccountSetting>
    </Modal>
  )
}

export default AccountSettingModal
