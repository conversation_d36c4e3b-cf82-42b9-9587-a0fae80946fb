'use client'

import { useTranslation } from 'react-i18next'
import s from './styles/index.module.css'
import './styles/index.css'
// 公共组件
import Modal from '@/app/components/base/modal'
import { STOP_SERVICE_BEGIN, STOP_SERVICE_END, STOP_SERVICE_PUBLISH } from '@/config'

const StopServiceModal = () => {
  const { t } = useTranslation()

  return (
    <Modal
      isShow
      className='!w-[480px] stop-service-modal'
      closable={false}
    >
      <div className={s['modal-content']}>
        {/* logo部分 */}
        <img alt='' className={'!h-[44px]'} src='/assets/stop-service-title.svg' width={349}></img>
        {/* 描述 */}
        <div className={s.desc}>
          <div className='mb-2'>{t('account.stopService.prologue')}</div>
          <div className='indent-8 mb-4'>{t('account.stopService.reason', { begin: STOP_SERVICE_BEGIN, end: STOP_SERVICE_END })}</div>
          <div className='indent-8'>{t('account.stopService.thanks')}</div>
          <div className='text-end'>{t('account.stopService.team')}</div>
          <div className='text-end'>{STOP_SERVICE_PUBLISH}</div>
        </div>

      </div>

    </Modal>
  )
}

export default StopServiceModal
