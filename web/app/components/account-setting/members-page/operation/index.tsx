'use client'
import { useTranslation } from 'react-i18next'
import { useMemo } from 'react'
import { useContext } from 'use-context-selector'
import { useProviderContext } from '@/context/provider-context'
import type { Member } from '@/models/common'
import { deleteMemberOrCancelInvitation, updateMemberRole } from '@/service/common'
/* 公共组件 */
import { ToastContext } from '@/app/components/base/toast'
import CheckSelect from '@/app/components/base/select/check-select'

const itemTitleClassName = `
  leading-[20px] text-sm text-gray-700 whitespace-nowrap
`
const itemDescClassName = `
  leading-[18px] text-xs text-gray-500 whitespace-nowrap text-ellipsis overflow-hidden
`

type IOperationProps = {
  member: Member
  onOperate: () => void
}

const Operation = ({
  member,
  onOperate,
}: IOperationProps) => {
  const { t } = useTranslation()
  const { datasetOperatorEnabled } = useProviderContext()
  const { notify } = useContext(ToastContext)
  const toHump = (name: string) => name.replace(/_(\w)/g, (all, letter) => letter.toUpperCase())

  /* 角色列表 */
  const roleList = useMemo(() => {
    return [
      {
        title: t('account.role.admin'),
        label: t('account.role.admin'),
        value: 'admin',
      },
      {
        title: t('account.role.editor'),
        label: t('account.role.editor'),
        value: 'editor',
      },
      {
        title: t('account.role.normal'),
        label: t('account.role.normal'),
        value: 'normal',
      },
      ...(datasetOperatorEnabled
        ? [{
          title: t('account.role.datasetOperator'),
          label: t('account.role.datasetOperator'),
          value: 'dataset_operator',
        }]
        : []),
      {
        title: t('account.role.owner'),
        label: t('account.role.owner'),
        value: 'owner',
        disabled: true,
      },
    ]
  }, [datasetOperatorEnabled])

  /* 取消成员邀请 */
  const handleDeleteMemberOrCancelInvitation = async () => {
    try {
      await deleteMemberOrCancelInvitation({ url: `/workspaces/current/members/${member.id}` })
      onOperate()
      notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
    }
    catch (e) {

    }
  }
  /* 更新成员角色 */
  const handleUpdateMemberRole = async (role: string) => {
    try {
      await updateMemberRole({
        url: `/workspaces/current/members/${member.id}/update-role`,
        body: { role },
      })
      onOperate()
      notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
    }
    catch (e) {

    }
  }

  return (
    <CheckSelect
      className='w-full min-w-[220px]'
      value={member.role}
      options={roleList}
      onChange={handleUpdateMemberRole}
      optionRender={option => (
        <>
          <div className={itemTitleClassName}>{t(`account.role.${toHump(option.value as string)}`)}</div>
          <div className={itemDescClassName}>{t(`account.role.${toHump(option.value as string)}Tip`)}</div>
        </>
      )}
      dropdownRender={menu => (
        <>
          {menu}
          <div onClick={handleDeleteMemberOrCancelInvitation} className='cursor-pointer h-[50px] px-3 py-[6px]'>
            <div className={itemTitleClassName}>{t('account.action.removeFromTeam')}</div>
            <div className={itemDescClassName}>{t('account.notify.removeFromTeamTip')}</div>
          </div>
        </>
      )}
    ></CheckSelect>
  )
}

export default Operation
