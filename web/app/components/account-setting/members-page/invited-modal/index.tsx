import { CheckCircleIcon } from '@heroicons/react/24/solid'
import { RiQuestionLine } from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import { useMemo } from 'react'
import { IS_CE_EDITION } from '@/config'
/* 公共组件 */
import type { InvitationResult } from '@/models/common'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import CopyInput from '@/app/components/base/input/copy-input'
import Tooltip from '@/app/components/base/tooltip'

export type SuccessInvationResult = Extract<InvitationResult, { status: 'success' }>
export type FailedInvationResult = Extract<InvitationResult, { status: 'failed' }>

type IInvitedModalProps = {
  invitationResults: InvitationResult[]
  onCancel: () => void
}
const InvitedModal = ({
  invitationResults,
  onCancel,
}: IInvitedModalProps) => {
  const { t } = useTranslation()
  // 格式化连接
  const formatValue = (url: string) => {
    return `${!url.startsWith('http') ? window.location.origin : ''}${url}`
  }

  // 成功邀请结果
  const successInvationResults = useMemo<SuccessInvationResult[]>(() => invitationResults?.filter(item => item.status === 'success') as SuccessInvationResult[], [invitationResults])
  // 失败邀请结果
  const failedInvationResults = useMemo<FailedInvationResult[]>(() => invitationResults?.filter(item => item.status !== 'success') as FailedInvationResult[], [invitationResults])

  return (
    <Modal
      isShow
      closable
      title={
        <div className='flex items-center'>
          <CheckCircleIcon className='w-[22px] h-[22px] mr-2 text-[#039855]' />
          <span className='text-xl font-semibold text-gray-900'>{t('account.notify.invitationSent')}</span>
        </div>
      }
      description={t('account.notify.invitationSentTip')}
      onClose={onCancel}
      className={'w-[480px]'}
    >
      {IS_CE_EDITION && (
        <>
          <div className='flex flex-col gap-2 mb-9'>
            {
              !!successInvationResults.length
                && <>
                  <div className='py-2 text-sm font-semibold text-gray-900'>{t('account.info.invitationLink')}</div>
                  {successInvationResults.map(item =>
                    <CopyInput key={item.email} value={formatValue(item.url)} />)}
                </>
            }
            {
              !!failedInvationResults.length
                && <>
                  <div className='py-2 text-sm font-semibold text-gray-900'>{t('account.info.failedinvitationEmails')}</div>
                  <div className='flex flex-wrap justify-between gap-y-1'>
                    {
                      failedInvationResults.map(item =>
                        <div key={item.email} className='flex justify-center border border-red-300 rounded-md px-1 bg-orange-50'>
                          <Tooltip popupContent={item.message}>
                            <div className='flex justify-center items-center text-sm gap-1'>
                              {item.email}
                              <RiQuestionLine className='w-4 h-4 text-red-300' />
                            </div>
                          </Tooltip>
                        </div>,
                      )
                    }
                  </div>
                </>
            }
          </div>
        </>
      )}
    </Modal>
  )
}

export default InvitedModal
