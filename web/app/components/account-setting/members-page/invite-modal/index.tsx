'use client'
import { useCallback, useState } from 'react'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import { ReactMultiEmail } from 'react-multi-email'
import 'react-multi-email/dist/style.css'
import { Form } from 'antd'
import { inviteMember } from '@/service/common'
import { emailRegex } from '@/config'
import { ToastContext } from '@/app/components/base/toast'
import { useProviderContext } from '@/context/provider-context'
import type { InvitationResult } from '@/models/common'
import I18n from '@/context/i18n'
// 公共组件
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import CheckSelect from '@/app/components/base/select/check-select'

type IInviteModalProps = {
  onCancel: () => void
  onSend: (invitationResults: InvitationResult[]) => void
}

const InviteModal = ({
  onCancel,
  onSend,
}: IInviteModalProps) => {
  const { t } = useTranslation()
  const [emails, setEmails] = useState<string[]>([])
  const { notify } = useContext(ToastContext)
  const { datasetOperatorEnabled } = useProviderContext()
  const { locale } = useContext(I18n)
  const [role, setRole] = useState<string>('normal')

  // 发送角色邀请
  const handleSend = useCallback(async () => {
    if (emails.map((email: string) => emailRegex.test(email)).every(Boolean)) {
      try {
        const { result, invitation_results } = await inviteMember({
          url: '/workspaces/current/members/invite-email',
          body: { emails, role, language: locale },
        })

        if (result === 'success') {
          notify({
            type: invitation_results?.some(item => item.status === 'failed') ? 'error' : 'success',
            message: invitation_results?.some(item => item.status === 'failed') ? (invitation_results.find(item => item.status === 'failed') as { message: string }).message : t('account.notify.inviteSuccful'),
          })
          onCancel()
          onSend(invitation_results)
        }
      }
      catch (e) { }
    }
    else {
      notify({ type: 'error', message: t('common.validate.pattern', { label: t('account.info.email') }) })
    }
  }, [role, emails, notify, onCancel, onSend, t])

  // 角色下拉列表
  const roleSelectList = [
    {
      label: t('account.role.normal'),
      desc: t('account.role.normalTip'),
      value: 'normal',
    }, {
      label: t('account.role.editor'),
      desc: t('account.role.editorTip'),
      value: 'editor',
    },
    {
      label: t('account.role.admin'),
      desc: t('account.role.adminTip'),
      value: 'admin',
    },
    ...(datasetOperatorEnabled
      ? [
        {
          label: t('account.role.datasetOperator'),
          desc: t('account.role.datasetOperatorTip'),
          value: 'dataset_operator',
        },
      ]
      : []),
  ]

  return (
    <Modal
      closable
      isShow
      onClose={onCancel}
      className='!w-[400px] !max-w-[400px]'
      title={t('account.action.inviteTeamMember')}
      description={t('account.notify.inviteTeamMemberTip')}
      footer={
        <Button
          className='w-full'
          onClick={handleSend}
          disabled={!emails.length}
          variant='primary'
        >
          {t('account.action.sendInvite')}
        </Button>
      }
    >
      <Form layout='vertical'>
        <Form.Item label={t('account.info.email')}>
          <ReactMultiEmail
            autoFocus
            emails={emails}
            inputClassName='bg-transparent'
            onChange={setEmails}
            getLabel={(email, index, removeEmail) =>
              <div data-tag key={index} className='bg-white'>
                <div data-tag-item>{email}</div>
                <span data-tag-handle onClick={() => removeEmail(index)}>
                    ×
                </span>
              </div>
            }
            placeholder={t('common.placeholder.input', { label: t('account.info.email') }) || ''}
          />
        </Form.Item>
        <Form.Item label={t('account.info.role')}>
          <CheckSelect
            className='w-full'
            value={role}
            onChange={e => setRole(e)}
            options={roleSelectList}
            optionRender={option => (
              <>
                <div className='text-sm leading-5'>{option.label}</div>
                <div className='text-xs leading-[18px] truncate'>{option.data.desc}</div>
              </>
            )}
          ></CheckSelect>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default InviteModal
