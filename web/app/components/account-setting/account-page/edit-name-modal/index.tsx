import { Form, Input } from 'antd'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { debounce } from 'lodash-es'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import { updateUserProfile } from '@/service/common'
import { ToastContext } from '@/app/components/base/toast'
import { useAppContext } from '@/context/app-context'

type EditNameModalProps = {
  name: string
  visible: boolean
  onClose: () => void
}

const EditNameModal = ({ name, visible, onClose }: EditNameModalProps) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { mutateUserProfile } = useAppContext()
  const [form] = Form.useForm()
  const values = Form.useWatch([], form)
  // 是否可提交
  const [disabled, setDisabled] = useState(true)

  // 是否正在编辑
  const [editing, setEditing] = useState(false)

  // 保存名字
  const handleSaveName = async () => {
    const { name } = form.getFieldsValue()
    try {
      setEditing(true)
      await updateUserProfile({ url: 'account/name', body: { name } })
      notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
      mutateUserProfile()
      onClose()
      setEditing(false)
    }
    catch (e) {
      notify({ type: 'error', message: (e as Error).message })
      onClose()
      setEditing(false)
    }
  }

  useEffect(() => {
    if (name) {
      form.setFieldsValue({
        name,
      })
    }
  }, [form, name])
  useEffect(debounce(() => {
    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(false)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])

  return (
    <Modal
      title={t('account.action.editName')}
      isShow={visible}
      onClose={onClose}
      className='!max-w-[400px] !w-[400px]'
      closable
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={onClose}>{t('common.operation.cancel')}</Button>
          <Button
            disabled={editing || disabled}
            variant='primary'
            onClick={handleSaveName}
          >
            {t('common.operation.save')}
          </Button>
        </>
      }
    >
      <Form form={form} layout='vertical'>
        <Form.Item
          required
          name={'name'}
          label={t('account.info.name')}
          validateFirst={true}
          validateTrigger='onBlur'
          rules={[
            {
              required: true, whitespace: true, min: 3, max: 30,
            },
          ]}
        >
          <Input placeholder={t('common.placeholder.input', { label : t('account.info.name') })!}/>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default EditNameModal
