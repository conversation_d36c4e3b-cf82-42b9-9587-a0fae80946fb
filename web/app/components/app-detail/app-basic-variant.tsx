import React from 'react'
import s from './styles/basic.module.css'
import AppAvatar from '@/app/components/app/common/avatar'
import Tooltip from '@/app/components/base/tooltip'
import { InfoCircle } from '@/app/components/base/icons/src/vender/line/tip'

export type IAppBasicProps = {
  name: string
  type?: string | React.ReactNode
  url?: string
  tooltip?: string
  appIcon?: boolean
}

export default function AppBasic({
  name, type, url, tooltip,
  appIcon = false,
}: IAppBasicProps) {
  return (
    <div className={s['app-basic']}>
      {
        appIcon && <AppAvatar className={s['app-icon']} url={url} appMode={'datasets'} size={36}/>
      }
      <div className={s['app-info']} style={{ width: 'calc(100% - 80px)' }}>
        <div className={s['app-title']}>
          <span className="text-[14px] text-[#5C6273]"> {name} </span>
          { tooltip && <Tooltip
            popupContent={tooltip}
          >
            <InfoCircle className='w-4 h-4 text-gray-G3'></InfoCircle>
          </Tooltip> }
        </div>
        {
          type && <div className={s['app-description']}>{type}</div>
        }
      </div>
    </div>
  )
}
