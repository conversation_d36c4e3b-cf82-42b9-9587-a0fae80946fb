import React from 'react'
import s from './styles/basic.module.css'
import AppAvatar from '@/app/components/app/common/avatar'
import Tooltip from '@/app/components/base/tooltip'
import { InfoCircle } from '@/app/components/base/icons/src/vender/line/tip'

export type IAppBasicProps = {
  name: string
  type: string | React.ReactNode
  url?: string
  tooltip?: string
}

export default function AppBasic({ name, type, url, tooltip }: IAppBasicProps) {
  return (
    <div className={s['app-basic']}>
      <AppAvatar className={s['app-icon']} url={url} appMode={'datasets'} size={36}/>
      <div className={s['app-info']} style={{ width: 'calc(100% - 80px)' }}>
        <div className={s['app-title']}>
          {name}
          { tooltip && <Tooltip
            popupContent={tooltip}
          >
            <InfoCircle className='w-4 h-4 text-gray-G3'></InfoCircle>
          </Tooltip> }
        </div>
        <div className={s['app-description']}>{type}</div>
      </div>
    </div>
  )
}
