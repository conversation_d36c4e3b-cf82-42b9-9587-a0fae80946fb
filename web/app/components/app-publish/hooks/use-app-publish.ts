import { useCallback } from 'react'
import { useStore as useAppStore } from '@/app/components/app/store'
import { getAppPublishInfo } from '@/service/market'
import { getToolPublishInfo } from '@/service/tools'
export const useAppPublishInit = () => {
  const appDetail = useAppStore(state => state.appDetail)!
  const appPublishInfo = useAppStore(state => state.appPublishInfo)
  const toolPublishInfo = useAppStore(state => state.toolPublishInfo)
  const setAppPublishInfo = useAppStore(state => state.setAppPublishInfo)
  const setToolPublishInfo = useAppStore(state => state.setToolPublishInfo)// 工具审核
  const getAppPublishInfoData = useCallback(async () => {
    const res = await getAppPublishInfo(appDetail.id)
    setAppPublishInfo(res)
  }, [])
  const getToolPublishInfoData = useCallback(async (id: string) => {
    const res = await getToolPublishInfo(id)
    setToolPublishInfo(res)
  }, [])

  return {
    getAppPublishInfoData,
    appPublishInfo,
    toolPublishInfo,
    getToolPublishInfoData,
  }
}
