'use client'
import React, { useCallback, useEffect, useState } from 'react'
import useSWR from 'swr'
import { Input, Pagination, Table } from 'antd'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import type { TableColumnsType } from 'antd'
import { useRouter } from 'next/navigation'
import useTimestamp from '@/hooks/use-timestamp'
import {
  ModifyPublishToolStatus,
  getPublishToolList,
  unPublishToolspublic,
} from '@/service/tools'
import { ToastContext } from '@/app/components/base/toast'
import Indicator from '@/app/components/base/indicator'
import TextButton from '@/app/components/base/button/text-button'
import Confirm from '@/app/components/base/confirm'
import type { statusEnum } from '@/app/components/app/app-publish-config/type'
import { statusMap } from '@/app/components/app/app-publish-config/type'
import { APP_PAGE_LIMIT } from '@/config'
import Tooltip from '@/app/components/base/tooltip'
// const i18nPrefix = 'app.info'
const i18nPrefix = 'reviewManagement.tool'

const ToolPublishReviewManagement = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { notify } = useContext(ToastContext)
  const { formatTime } = useTimestamp()
  // 当前页
  const [currPage, setCurrPage] = useState<number>(1)

  const [showConfirmUnpublish, setShowConfirmUnpublish] = useState(false)
  const [currentApp, setCurrentApp] = useState<any>({})
  // 拒绝通过/下架原因
  const [failedMessage, setFailedMessage] = useState<string>('')

  useEffect(() => {
    setFailedMessage('')
  }, [showConfirmUnpublish])

  const defaultValue = '-'

  const query = {
    page: currPage,
    limit: APP_PAGE_LIMIT,
  }

  // 获取工具审核列表接口
  const {
    data: dataList,
    mutate: mutateDataList,
    isLoading,
  } = useSWR(
    () => ({
      url: '/tool-market/toolsrelease',
      params: query,
    }),
    getPublishToolList,
  )

  // 点击体检工具跳转。
  const EXepericeTool = async (toolID: string) => {
    window.open(`/app/${toolID}/reviewworkflow`, '_blank', 'noreferrer')
  }
  const total = dataList?.total

  // // 工具详情
  // const clickDetail = (app: any) => {
  //   let path = 'configuration'
  //   if(app.mode === 'workflow' || app.mode === 'advanced-chat')
  //     path = 'workflow'
  //   window.open(`/app/${app.app_id}/${path}`, '_blank')
  // }

  // 审核通过
  const clickPublish = async (app: any) => {
    // TODO
    try {
      await ModifyPublishToolStatus({
        id: app.id,
        market_status: 'online',
        failed_message: '',
      })
      notify({
        type: 'success',
        message: t(`${i18nPrefix}.PublishTool`),
      })
      mutateDataList()
    }
    catch {}
  }

  // 下线工具/拒绝通过
  const ClickUnpublish = useCallback((app: any) => {
    setShowConfirmUnpublish(true)
    setCurrentApp(app)
  }, [])

  // 申请上线：不通过; 已上线：下架工具
  const handleUnPublish = async () => {
    // 申请上线：不通过
    // TODO
    if (currentApp.market_status === 'review') {
      try {
        await ModifyPublishToolStatus({
          id: currentApp.id,
          market_status: 'failed',
          failed_message: failedMessage,
        })
        notify({
          type: 'success',
          message: t(`${i18nPrefix}.unPublishTool`),
        })
        mutateDataList()
      }
      catch {}
    }
    else {
      // 已上线：下架工具
      try {
        await unPublishToolspublic(currentApp.app_id, {
          failed_message: failedMessage,
        })
        notify({
          type: 'success',
          message: t(`${i18nPrefix}.unPublishTool`),
        })
        mutateDataList()
      }
      catch {}
    }
    setShowConfirmUnpublish(false)
  }

  // 判断按钮是否禁用
  const checkDisabled = useCallback((app: any, action: string) => {
    if (action === 'experience') {
      // 体验工具
      return ['offline', 'review', 'failed'].includes(app.market_status)
    }
    else if (action === 'publish') {
      // 上线工具
      return ['offline', 'online', 'failed'].includes(app.market_status)
    }
    else if (action === 'unPublish') {
      // 下线工具
      return ['offline'].includes(app.market_status)
    }
    else if (action === 'detail') {
      // 工具详情
      return false
    }
    return true
  }, [])
  // 表格列
  const columns: TableColumnsType<any> = [
    // 工具名称
    {
      title: t(`${i18nPrefix}.table.name`),
      key: 'name',
      render: (_: any, data) => {
        return data.create_data.name || defaultValue
      },
      width: 150,
      ellipsis: true,
    },
    // 工具描述
    {
      title: t(`${i18nPrefix}.table.description`),
      key: 'description',
      render: (_: any, data) => {
        return data.create_data.description || defaultValue
      },
      width: 150,
      ellipsis: true,
    },
    // 类型
    {
      title: t(`${i18nPrefix}.table.type`),
      key: 'type',
      render: (_: any, data) => {
        return data.create_data.description || defaultValue
      },
      ellipsis: true,
    },
    // 创建时间
    {
      title: t(`${i18nPrefix}.table.createTime`),
      key: 'time',
      render: (_: any, data) => {
        return formatTime(
          data.created_at,
          t('common.dateFormat.dateTime') as string,
        )
      },
      width: 190,
    },
    // 状态
    {
      title: t(`${i18nPrefix}.table.status`),
      key: 'status',
      render: (_: any, data) => {
        return (
          <div className="flex items-center">
            <Indicator
              color={
                statusMap[data.market_status as statusEnum]?.color || 'gray'
              }
              textClassName="text-S3 leading-H3"
            >
              {t(statusMap[data.market_status as statusEnum]?.text)
                || data.market_status}
              {/* 提示信息 */}
            </Indicator>
            {data.is_multiple_release && (
              <Tooltip
                popupContent={
                  <div className="max-w-[260px] break-all">
                    {t(`${i18nPrefix}.table.history_online_at`)}
                    {formatTime(
                      data.history_updated_at,
                      t('common.dateFormat.dateTime') as string,
                    )}
                  </div>
                }
                TipType={'exclamation'}
                triggerClassName="ml-1 w-4 h-4"
              />
            )}
          </div>
        )
      },
      width: 120,
    },
    // 审核人
    {
      title: t(`${i18nPrefix}.table.reviewer`),
      key: 'reviewer',
      render: (_: any, data) => {
        return (
          <div className="flex items-center">
            <span>{data.reviewer}</span>
            {data.is_multiple_release && (
              <Tooltip
                popupContent={
                  <>
                    <div className="max-w-[260px] break-all">
                      {t(`${i18nPrefix}.table.reviewer`)}：
                      {data.history_reviewer_id}
                    </div>
                    <div className="max-w-[260px] break-all">
                      {t(`${i18nPrefix}.table.history_updated_at`)}：
                      {formatTime(
                        data.first_updated_at,
                        t('common.dateFormat.dateTime') as string,
                      )}
                    </div>
                  </>
                }
                TipType={'exclamation'}
                triggerClassName="ml-1 w-4 h-4"
              />
            )}
          </div>
        )
      },
      width: 150,
    },
    // 操作
    {
      title: t(`${i18nPrefix}.table.operate`),
      render: (_: any, data) => (
        <div className="flex gap-6">
          {/* TODO-体验工具 */}
          <TextButton
            size="middle"
            onClick={() => EXepericeTool(data.app_id)}
            disabled={checkDisabled(data, 'experience')}
          >
            {t('app.action.experienceTool')}
          </TextButton>

          {/* 上线工具 */}
          <TextButton
            size="middle"
            onClick={() => clickPublish(data)}
            disabled={checkDisabled(data, 'publish')}
          >
            {t('app.action.publishTool')}
          </TextButton>
          {/* 下线工具/拒绝通过 */}
          <TextButton
            size="middle"
            onClick={() => ClickUnpublish(data)}
            disabled={checkDisabled(data, 'unPublish')}
          >
            {data.market_status === 'review'
              ? t('app.action.reject')
              : t('app.action.unpublishTool')}
          </TextButton>
          {/* <Popover
            placement="bottom"
            className="custom-popover"
            content={
              <div>
                <Button
                  size="middle"
                  onClick={() => ClickUnpublish(data)}
                  disabled={checkDisabled(data, "unPublish")}
                  type="text"
                >
                  {data.market_status === "review"
                    ? t("app.action.refuse")
                    : t("app.action.unpublishTool")}
                </Button>
              </div>
            }
          >
            <TextButton size="middle">
              {t(`${i18nPrefix}.table.more`)}
            </TextButton>
          </Popover> */}

          {/* */}
        </div>
      ),
      key: 'operation',
      align: 'left',
      width: 300,
    },
  ]
  return (
    <div className="p-8">
      <Table
        size="middle"
        loading={isLoading}
        columns={columns}
        pagination={false}
        scroll={{ x: '100%' }}
        rowKey="id"
        dataSource={dataList?.items || []}
      />
      {/* 分页组件 */}
      <Pagination
        className="mt-3"
        align="end"
        current={currPage}
        hideOnSinglePage
        onChange={setCurrPage}
        total={total}
        pageSize={APP_PAGE_LIMIT}
        showQuickJumper={false}
        showSizeChanger={false}
      />
      <Confirm
        title={
          currentApp.market_status === 'review'
            ? t('app.action.reject')
            : t('app.action.unPublish')
        }
        content={
          <div>
            {currentApp.market_status === 'online' && (
              <div className="text-gray-G2">
                {t('tools.publish.tip.unToolTip')}
              </div>
            )}
            <Input
              placeholder={
                t('app.placeholder.failMessagePlaceholder') as string
              }
              maxLength={100}
              onChange={e => setFailedMessage(e.target.value)}
              className="mt-2"
            ></Input>
          </div>
        }
        isDisabled={!failedMessage}
        isShow={showConfirmUnpublish}
        onCancel={() => setShowConfirmUnpublish(false)}
        onConfirm={handleUnPublish}
      ></Confirm>
    </div>
  )
}

export default ToolPublishReviewManagement
