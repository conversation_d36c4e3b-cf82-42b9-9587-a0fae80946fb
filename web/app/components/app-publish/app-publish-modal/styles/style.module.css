.wrap {
  @apply flex border border-gray-G5 rounded px-[12px] py-[6px] text-gray-G1 leading-H3;
}

.option {
  width: 200px;
  height: 121px;
  @apply box-border cursor-pointer bg-auto bg-no-repeat bg-center rounded border hover:border-primary-P1;
}
.active {
  @apply border-primary-P1;
}
.iframeIcon {
  background-image: url('/assets/icons/app/iframe-option.png');
  background-size: cover;
}
.scriptsIcon {
  background-image: url('/assets/icons/app/scripts-option.png');
  background-size: cover;
}

.configTip {
  @apply flex gap-1 text-S1 leading-H1 text-gray-G3;
}