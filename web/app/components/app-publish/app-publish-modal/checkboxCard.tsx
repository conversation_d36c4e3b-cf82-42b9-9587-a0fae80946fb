'use client'
import { useTranslation } from 'react-i18next'
import { Checkbox } from 'antd'
import type { CheckboxProps } from 'antd/es/checkbox'
import TextButton from '../../base/button/text-button'
import style from './styles/style.module.css'

type CheckboxCardProps = {
  onConfigure?: () => void
  disabledText?: string
  children?: React.ReactNode
}
const CheckboxCard: React.FC<CheckboxProps & CheckboxCardProps> = ({ onConfigure, disabledText, children, ...rest }) => {
  const { t } = useTranslation()
  return (
    <div className={style.wrap}>
      <Checkbox {...rest}>{children}</Checkbox>
      {onConfigure
        && <>
          {!rest.disabled && <TextButton
            disabled={!rest.checked}
            onClick={onConfigure}
            size='middle'
            className='ml-2'
          >{t('app.action.configure')}</TextButton>}
          {rest.disabled && (
            <div className='text-red-R2 ml-3'>{disabledText}</div>
          )}
        </>
      }
    </div>
  )
}

export default CheckboxCard
