import { AppMarketPower, AppMarketCopyConfig } from '@/types/app-market'
import { Option } from '@/app/components/app/embedded/type'

export type AppPublishConfig = {
  name: string, // 应用名称
  description: string, // 应用描述
  publishMethod: Array<string>, // 发布方式
  acl: AppMarketPower, // 权限
  category_id: string, // 分类id
  copyConfig: AppMarketCopyConfig // 工具、知识库同步配置
  embedding_method: Option, // 嵌入方式
  tool_dataset_acl: AppMarketCopyConfig
}