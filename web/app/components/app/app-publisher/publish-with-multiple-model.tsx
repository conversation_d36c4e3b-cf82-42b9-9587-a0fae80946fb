import type { FC } from 'react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { RiArrowDownSLine } from '@remixicon/react'
import { Dropdown } from 'antd'
import type { ModelAndParameter } from '../configuration/debug/types'
import ModelIcon from '../..//account-setting/model-provider-page/model-icon'
import Button from '@/app/components/base/button'
import { useProviderContext } from '@/context/provider-context'
import type { Model, ModelItem } from '@/app/components/account-setting/model-provider-page/declarations'
import { useLanguage } from '@/app/components/account-setting/model-provider-page/hooks'

type PublishWithMultipleModelProps = {
  multipleModelConfigs: ModelAndParameter[]
  // textGenerationModelList?: Model[]
  onSelect: (v: ModelAndParameter) => void
}
const PublishWithMultipleModel: FC<PublishWithMultipleModelProps> = ({
  multipleModelConfigs,
  // textGenerationModelList = [],
  onSelect,
}) => {
  const { t } = useTranslation()
  const language = useLanguage()
  const { textGenerationModelList } = useProviderContext()
  const [open, setOpen] = useState(false)

  const validModelConfigs: (ModelAndParameter & { modelItem: ModelItem; providerItem: Model })[] = []

  multipleModelConfigs.forEach((item) => {
    const provider = textGenerationModelList.find(model => model.provider === item.provider)

    if (provider) {
      const model = provider.models.find(model => model.model === item.model)

      if (model) {
        validModelConfigs.push({
          id: item.id,
          model: item.model,
          provider: item.provider,
          modelItem: model,
          providerItem: provider,
          parameters: item.parameters,
        })
      }
    }
  })

  const handleSelect = (key: string) => {
    const item = validModelConfigs.find(item => item.id === key) as ModelAndParameter
    onSelect(item)
    setOpen(false)
  }

  return (
    <Dropdown
      open={open}
      onOpenChange={setOpen}
      placement='bottom'
      menu={{
        items: validModelConfigs.map((item, index) => {
          return {
            key: item.id,
            label: (
              <div className='flex items-center gap-2'>
                <span className='shrink-0'>#{index + 1}</span>
                <ModelIcon modelName={item.model} provider={item.providerItem} model={item.modelItem} />
                <div
                  className='truncate'
                  title={item.modelItem.label[language]}
                >
                  {item.modelItem.label[language]}
                </div>
              </div>
            ),
          }
        }),
        onClick: ({ key }) => { handleSelect(key) },
      }}
    >
      <Button
        variant='primary'
        disabled={!validModelConfigs.length}
        className='mt-3 w-full'
      >
        {t('appDebug.publishAs')}
        <RiArrowDownSLine className='ml-0.5 w-4 h-4' />
      </Button>
    </Dropdown>
  )
}

export default PublishWithMultipleModel
