import type { HTMLProps, PropsWithChildren } from 'react'
import cn from '@/utils/classnames'
import { ArrowUpRight } from '@/app/components/base/icons/src/vender/line/arrows'
import Tooltip from '../../base/tooltip'
import style from './styles/style.module.css'

export type SuggestedActionProps = PropsWithChildren<HTMLProps<HTMLAnchorElement> & {
  icon?: React.ReactNode
  link?: string
  tooltip?: string
  disabled?: boolean
}>

const SuggestedAction = ({ icon, link, tooltip, disabled, children, className, ...props }: SuggestedActionProps) => {
  return (
    <Tooltip popupContent={tooltip}>
    <a
      href={disabled ? undefined : link}
      target='_blank'
      rel='noreferrer'
      className={cn(
        style.btnWrap,
        disabled ? 'text-gray-G4 hover:text-gray-G4 text-gray-G4 cursor-not-allowed' : 'hover:border-primary-P2 hover:bg-primary-P4 text-gray-G1 active:text-primary-P1 cursor-pointer',
        className,
      )}
      {...props}
    >
      {icon}
      <div className={cn(style.btnContent, disabled ? 'text-gray-G4': 'text-gray-G1')}>{children}</div>
      <ArrowUpRight />
    </a>
  </Tooltip>
  )
}

export default SuggestedAction
