import {
  memo,
  useCallback,
  useMemo,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import { RiArrowDownSLine } from '@remixicon/react'
import { Divider, Popover } from 'antd'
import { useContext } from 'use-context-selector'
import type { ModelAndParameter } from '../configuration/debug/types'
import { ToastContext } from '../../base/toast'
import SuggestedAction from './suggested-action'
import PublishWithMultipleModel from './publish-with-multiple-model'
import Button from '@/app/components/base/button'
import EmbeddedModal from '@/app/components/app/embedded'
import { useStore as useAppStore } from '@/app/components/app/store'
import { useGetLanguage } from '@/context/i18n'
import { Embedding, Run } from '@/app/components/base/icons/src/public/app'
import type { InputVar } from '@/app/components/workflow/types'

export type AppPublisherProps = {
  disabled?: boolean
  publishDisabled?: boolean
  notPublishedAfterCopied?: boolean
  publishedAt?: number
  /** only needed in workflow / chatflow mode */
  draftUpdatedAt?: number
  debugWithMultipleModel?: boolean
  multipleModelConfigs?: ModelAndParameter[]
  /** modelAndParameter is passed when debugWithMultipleModel is true */
  onPublish?: (showNotify: boolean, modelAndParameter?: ModelAndParameter) => Promise<any> | any
  onRestore?: () => Promise<any> | any
  onToggle?: (state: boolean) => void
  crossAxisOffset?: number
  toolPublished?: boolean
  inputs?: InputVar[]
  onRefreshData?: () => void
}

const AppPublisher = ({
  disabled = false,
  publishDisabled = false,
  notPublishedAfterCopied = false,
  publishedAt,
  draftUpdatedAt,
  debugWithMultipleModel = false,
  multipleModelConfigs = [],
  onPublish,
  onRestore,
  onToggle,
  crossAxisOffset = 0,
  toolPublished,
  inputs,
  onRefreshData,
}: AppPublisherProps) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const [published, setPublished] = useState(false)
  const [open, setOpen] = useState(false)
  const appDetail = useAppStore(state => state.appDetail)
  const { app_base_url: appBaseURL = '', access_token: accessToken = '' } = appDetail?.site ?? {}
  const appMode = (appDetail?.mode !== 'completion' && appDetail?.mode !== 'workflow') ? 'chat' : appDetail.mode
  const appURL = `${appBaseURL}/${appMode}/${accessToken}`

  const language = useGetLanguage()
  const formatTimeFromNow = useCallback((time: number) => {
    return dayjs(time).locale(language === 'zh_Hans' ? 'zh-cn' : language.replace('_', '-')).fromNow()
  }, [language])

  const handlePublish = async (modelAndParameter?: ModelAndParameter) => {
    try {
      await onPublish?.(true, modelAndParameter)
      notify({ type: 'success', message: t('common.actionMsg.saveSuccessfully') })
      setPublished(true)
    }
    catch (e) {
      setPublished(false)
    }
  }

  const handleRestore = useCallback(async () => {
    try {
      await onRestore?.()
      setOpen(false)
    }
    catch (e) { }
  }, [onRestore])

  const onOpenChange = (state: boolean) => {
    setOpen(state)
    if (!state)
      setPublished(false)
  }
  const handleTrigger = useCallback(() => {
    const state = !open

    if (disabled) {
      setOpen(false)
      return
    }

    onToggle?.(state)
    setOpen(state)

    if (state)
      setPublished(false)
  }, [disabled, onToggle, open])

  const runDisabledTip = useMemo(() => {
    if (!publishedAt)
      return t('workflow.common.runDisabled.notPublished')
    if (notPublishedAfterCopied)
      return t('workflow.common.runDisabled.notPublishedAfterCopied')
  }, [publishedAt, notPublishedAfterCopied])

  const [embeddingModalOpen, setEmbeddingModalOpen] = useState(false)

  return (
    <Popover
      onOpenChange={open => onOpenChange(open)}
      arrow={false}
      placement='bottom'
      content= {
        <div className='w-[300px] bg-white rounded shadow-xl'>
          <div className='px-4 py-3'>
            <div className='flex items-center text-[12px] leading-H1 text-gray-G3 uppercase'>
              {publishedAt ? t('workflow.common.latestPublished') : t('workflow.common.currentDraftUnpublished')}
            </div>
            {publishedAt
              ? (
                <div className='flex justify-between items-center mb-[6px]'>
                  <div className='flex items-center leading-H1 text-[12px] text-gray-G1'>
                    {t('workflow.common.publishedAt')} {formatTimeFromNow(publishedAt)}
                  </div>
                  { appDetail?.mode !== 'agent-chat'
                    && <Button
                      className='!rounded-[2px] !border-gray-G4 hover:!border-primary-P1 text-gray-G2 h-[24px] !w-[64px]'
                      size='small'
                      onClick={handleRestore}
                      disabled={published}
                      variant={'secondary-accent'}
                    >
                      {t('workflow.common.restore')}
                    </Button>
                  }
                </div>
              )
              : (
                <div className='flex items-center h-[18px] leading-[18px] text-[13px] font-semibold text-gray-700'>
                  {t('workflow.common.autoSaved')} · {Boolean(draftUpdatedAt) && formatTimeFromNow(draftUpdatedAt!)}
                </div>
              )}
            {debugWithMultipleModel
              ? (
                <PublishWithMultipleModel
                  multipleModelConfigs={multipleModelConfigs}
                  onSelect={item => handlePublish(item)}
                // textGenerationModelList={textGenerationModelList}
                />
              )
              : (
                <Button
                  variant='primary'
                  size='small'
                  className='w-full'
                  onClick={() => handlePublish()}
                  disabled={publishDisabled || published}
                >
                  {
                    published
                      ? t('workflow.common.published')
                      : publishedAt ? t('workflow.common.update') : (appDetail?.mode === 'workflow' ? t('common.operation.submit') : t('common.operation.publish'))
                  }
                </Button>
              )
            }
          </div>

          {appDetail?.mode !== 'workflow' && (
            <>
              <Divider className='mt-2'></Divider>
              <div className='px-4 pb-4 flex flex-col gap-2'>
                <div className='flex items-center text-[12px] leading-H1 text-gray-G3 uppercase'>{t('workflow.common.appUseType')}</div>
                <SuggestedAction
                  disabled={!publishedAt || notPublishedAfterCopied}
                  tooltip={runDisabledTip}
                  link={appURL}
                  icon={<Run />}
                >{t('workflow.common.runApp')}</SuggestedAction>
                <SuggestedAction
                  onClick={() => {
                    if (!publishedAt || notPublishedAfterCopied)
                      return
                    setEmbeddingModalOpen(true)
                    handleTrigger()
                  }}
                  disabled={!publishedAt || notPublishedAfterCopied}
                  tooltip={runDisabledTip}
                  icon={<Embedding className='w-6 h-6' />}
                >
                  {t('workflow.common.embedIntoSite')}
                </SuggestedAction>
              </div>
            </>
          )}
        </div>

      }
    >
      <Button
        variant='primary'
        size='large'
        disabled={disabled}
      >
        {appDetail?.mode === 'workflow' ? (publishedAt ? t('workflow.common.updateSubmit') : t('common.operation.submit')) : t('common.operation.publish')}
        <RiArrowDownSLine className='w-4 h-4 ml-0.5' />
      </Button>
      <EmbeddedModal
        siteInfo={appDetail?.site}
        isShow={embeddingModalOpen}
        onClose={() => setEmbeddingModalOpen(false)}
        appBaseUrl={appBaseURL}
        accessToken={accessToken}
      />
    </Popover >
  )
}

export default memo(AppPublisher)
