import React from 'react'
import Tooltip from '@/app/components/base/tooltip'

export type IAppBasicProps = {
  name: string
  type: string | React.ReactNode
  hoverTip?: string
  textStyle?: { main?: string; extra?: string }
  mode?: string
}

export default function AppBasic({ name, type, hoverTip, textStyle, mode = 'expand' }: IAppBasicProps) {
  return (
    <div className="flex items-start p-1">
      {mode === 'expand' && <div className="group">
        <div className={`flex flex-row items-center text-sm font-semibold text-gray-G1 group-hover:text-gray-G1 break-all ${textStyle?.main ?? ''}`}>
          {name}
          {hoverTip
            && <Tooltip popupContent={hoverTip} triggerClassName='ml-1' />
          }
        </div>
        <div className={`text-xs font-normal text-gray-500 group-hover:text-gray-700 break-all ${textStyle?.extra ?? ''}`}>{type}</div>
      </div>}
    </div>
  )
}
