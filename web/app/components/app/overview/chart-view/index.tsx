'use client'
import React, { useState } from 'react'
import { v4 as uuid4 } from 'uuid'
import dayjs from 'dayjs'
import { random } from 'lodash'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import { useTranslation } from 'react-i18next'
import type { PeriodParams } from '@/app/components/app/overview/chart-view/appChart'
import { AvgResponseTime, AvgSessionInteractions, AvgUserInteractions, ConversationsChart, CostChart, EndUsersChart, MessagesChart, TokenPerSecond, UserSatisfactionRate, WorkflowCostChart, WorkflowDailyTerminalsChart, WorkflowMessagesChart } from '@/app/components/app/overview/chart-view/appChart'
import { useStore as useAppStore } from '@/app/components/app/store'
import Select from '@/app/components/base/select'

dayjs.extend(quarterOfYear)
const today = dayjs()

const queryDateFormat = 'YYYY-MM-DD HH:mm'

export type IChartViewProps = {
  appId: string
}
const TIME_PERIOD_LIST = [
  { value: `0-${random()}`, name: 'today' },
  { value: `7-${random()}`, name: 'last7days' },
  { value: `28-${random()}`, name: 'last4weeks' },
  { value: `${today.diff(today.subtract(3, 'month'), 'day')}-${uuid4()}`, name: 'last3months' },
  { value: `${today.diff(today.subtract(12, 'month'), 'day')}-${uuid4()}`, name: 'last12months' },
  { value: `${today.diff(today.startOf('month'), 'day')}-${uuid4()}`, name: 'monthToDate' },
  { value: `${today.diff(today.startOf('quarter'), 'day')}-${uuid4()}`, name: 'quarterToDate' },
  { value: `${today.diff(today.startOf('year'), 'day')}-${uuid4()}`, name: 'yearToDate' },
  { value: 'all', name: 'allTime' },
]

export default function ChartView({ appId }: IChartViewProps) {
  const { t } = useTranslation()
  const appDetail = useAppStore(state => state.appDetail)
  const isChatApp = appDetail?.mode !== 'completion' && appDetail?.mode !== 'workflow'
  const isWorkflow = appDetail?.mode === 'workflow'
  const [period, setPeriod] = useState<PeriodParams>({ name: t('appLog.filter.period.last7days'), query: { start: today.subtract(7, 'day').startOf('day').format(queryDateFormat), end: today.endOf('day').format(queryDateFormat) } })

  const onSelect = (item: { value: string ; label: string }) => {
    const realValue = item.value.split('-')[0]
    if (realValue === 'all') {
      setPeriod({ name: item.label, query: undefined })
    }
    else if (realValue === '0') {
      const startOfToday = today.startOf('day').format(queryDateFormat)
      const endOfToday = today.endOf('day').format(queryDateFormat)
      setPeriod({ name: item.label, query: { start: startOfToday, end: endOfToday } })
    }
    else {
      setPeriod({ name: item.label, query: { start: today.subtract(Number(realValue) as number, 'day').startOf('day').format(queryDateFormat), end: today.endOf('day').format(queryDateFormat) } })
    }
  }

  if (!appDetail)
    return null

  return (
    <div>
      {/* 顶部标题 */}
      <div className='flex flex-row items-center mb-4 text-gray-G1 text-base'>
        <span className='mr-3'>{t('appOverview.analysis.title')}</span>
        <Select
          labelInValue
          options={TIME_PERIOD_LIST.map(item => ({ value: item.value, label: t(`appLog.filter.period.${item.name}`) }))}
          className='mt-0 !w-40'
          onChange={onSelect}
          defaultValue={TIME_PERIOD_LIST[1].value}
        />
      </div>
      {/* 不是工作流 */}
      {!isWorkflow && (
        <div className='grid gap-6 grid-cols-1 xl:grid-cols-2 w-full mb-6'>
          <ConversationsChart period={period} id={appId} />
          <EndUsersChart period={period} id={appId} />
        </div>
      )}
      {/* 不是工作流 */}
      {!isWorkflow && (
        <div className='grid gap-6 grid-cols-1 xl:grid-cols-2 w-full mb-6'>
          {isChatApp
            ? (
              <AvgSessionInteractions period={period} id={appId} />
            )
            : (
              <AvgResponseTime period={period} id={appId} />
            )}
          <TokenPerSecond period={period} id={appId} />
        </div>
      )}
      {!isWorkflow && (
        <div className='grid gap-6 grid-cols-1 xl:grid-cols-2 w-full mb-6'>
          <UserSatisfactionRate period={period} id={appId} />
          <CostChart period={period} id={appId} />
        </div>
      )}
      {!isWorkflow && isChatApp && (
        <div className='grid gap-6 grid-cols-1 xl:grid-cols-2 w-full mb-6'>
          <MessagesChart period={period} id={appId} />
        </div>
      )}
      {/* 是工作流 */}
      {isWorkflow && (
        <div className='grid gap-6 grid-cols-1 xl:grid-cols-2 w-full mb-6'>
          <WorkflowMessagesChart period={period} id={appId} />
          <WorkflowDailyTerminalsChart period={period} id={appId} />
        </div>
      )}
      {isWorkflow && (
        <div className='grid gap-6 grid-cols-1 xl:grid-cols-2 w-full mb-6'>
          <WorkflowCostChart period={period} id={appId} />
          <AvgUserInteractions period={period} id={appId} />
        </div>
      )}
    </div>
  )
}
