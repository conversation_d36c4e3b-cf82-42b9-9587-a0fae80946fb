'use client'
import type { <PERSON> } from 'react'
import React from 'react'
import cn from '@/utils/classnames'
import { TracingIcon as Icon } from '@/app/components/base/icons/src/public/tracing'

type Props = {
  className?: string
  size: 'lg' | 'md'
}

const sizeClassMap = {
  lg: 'w-9 h-9 p-2 rounded',
  md: 'w-6 h-6 p-1 rounded',
}

const TracingIcon: FC<Props> = ({
  className,
  size,
}) => {
  const sizeClass = sizeClassMap[size]
  return (
    <div className={cn(className, sizeClass, 'bg-primary-500 shadow-md')}>
      <Icon className='w-full h-full' />
    </div>
  )
}
export default React.memo(TracingIcon)
