.option {
  width: 200px;
  height: 121px;
  @apply box-border cursor-pointer bg-auto bg-no-repeat bg-center rounded border hover:border-primary-P1;
}
.active {
  @apply border-primary-P1;
}
.iframe-zh-<PERSON>-<PERSON><PERSON> {
  background-image: url('/assets/icons/app/iframe-option.png');
  background-size: cover;
}
.iframe-en-US-Icon {
  background-image: url('/assets/icons/app/iframe-en-US-option.png');
  background-size: cover;
}
.scripts-zh-<PERSON>-<PERSON> {
  background-image: url('/assets/icons/app/scripts-option.png');
  background-size: cover;
}
.scripts-en-US-Icon {
  background-image: url('/assets/icons/app/scripts-en-US-option.png');
  background-size: cover;
}

.codeCardWrap {
  @apply border border-gray-G5 rounded;
  padding: 8px 12px;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}
.codeCardHeader {
 @apply flex justify-between items-center mb-1;
}

.codeTitle {
  @apply text-gray-G2;
}
.codeContent {
  @apply bg-gray-G7 text-gray-G2 grow shrink basis-0 rounded break-all;
  padding: 10px 12px;
}