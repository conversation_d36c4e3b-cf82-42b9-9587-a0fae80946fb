import { IS_CE_EDITION } from '@/config'

export const OPTION_MAP = {
  iframe: {
    getContent: (url: string, token: string) =>
      `<iframe
 src="${url}/chatbot/${token}"
 style="width: 100%; height: 100%; min-height: 700px"
 frameborder="0"
 allow="microphone">
</iframe>`,
    label: '居中嵌入',
    value: 'iframe',
  },
  scripts: {
    getContent: (url: string, token: string, primaryColor: string) =>
      `<script>
 window.agentChatbotConfig = {
  token: '${token}'${IS_CE_EDITION
  ? `,
  baseUrl: '${url}'`
  : ''}
 }
</script>
<script
 src="${url}/embed.min.js"
 id="${token}"
 defer>
</script>
<style>
  #agent-builder-chatbot-bubble-button {
    background-color: ${primaryColor} !important;
  }
  #agent-builder-chatbot-bubble-window {
    width: 24rem !important;
    height: 40rem !important;
  }
</style>`,
    label: '右侧嵌入',
    value: 'scripts',
  },
}

export type Option = keyof typeof OPTION_MAP
