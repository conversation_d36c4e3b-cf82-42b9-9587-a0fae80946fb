import { Radio } from 'antd'
import style from './style.module.css'
import type { Option } from './type'
import { OPTION_MAP } from './type'
import cn from '@/utils/classnames'
import { useI18N } from '@/context/i18n'

type EmbeddedOptionProps = {
  value: Option
  onSelect: (option: Option) => void
  showRadio?: boolean
}

const EmbeddedOption = ({
  value,
  onSelect,
  showRadio = false,
}: EmbeddedOptionProps) => {
  const { locale } = useI18N()
  return (
    <>
      {Object.keys(OPTION_MAP).map((v, index) => {
        return (
          <div
            key={v}
            className='flex flex-col items-center gap-3 cursor-pointer'
            onClick={() => {
              onSelect(v as Option)
            }}
          >
            <div
              className={cn(
                style.option,
                value === v && style.active,
                style[`${v}-${locale}-Icon`],
              )}
            ></div>
            {showRadio
              && <Radio
                className='m-0'
                checked={value === v}
              >{OPTION_MAP[v as Option].label}</Radio>
            }
          </div>
        )
      })}
    </>
  )
}

export default EmbeddedOption
