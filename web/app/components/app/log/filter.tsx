'use client'
import type { FC } from 'react'
import React, { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import { DatePicker, Divider, Select } from 'antd'

import useSWR from 'swr'
import { datasetTypeOptions, datasetTypeOptions_ZZ } from '../data-reflux/type'
import Button from '../../base/button'
import type { QueryParam, likeParam } from './type'
import { ChatSourceEnum, LikeEnum } from './type'

import cn from '@/utils/classnames'
import { useStore as useAppStore } from '@/app/components/app/store'

// 公共组件
import { Add, Sort } from '@/app/components/base/icons/src/vender/line/action'
import Tooltip from '@/app/components/base/tooltip'
import PopoverSelect from '@/app/components/base/select/popover-select'
import TextButton from '@/app/components/base/button/text-button'
import SearchInput from '@/app/components/base/input/search-input'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'

import { fetchNodeList } from '@/service/log'
const { RangePicker } = DatePicker
dayjs.extend(quarterOfYear)

type IFilterProps = {
  showDatasetReflux?: boolean
  queryParams: QueryParam
  queryDays?: number
  setQueryParams: (v: QueryParam) => void
  onNewDataReflux?: () => void
  dataRefluxDisabled?: boolean
  dataRefluxDisabledTip?: string
}

const Filter: FC<IFilterProps> = ({ showDatasetReflux, queryParams, queryDays, setQueryParams, onNewDataReflux, dataRefluxDisabled, dataRefluxDisabledTip }: IFilterProps) => {
  const { t } = useTranslation()
  const appDetail = useAppStore(state => state.appDetail)
  const today = useMemo(() => new Date(), [])

  // 只有在工作流模式下才调用接口
  const shouldFetch = appDetail?.mode === 'advanced-chat' && appDetail?.id

  // 在 Filter 组件内部
  const { data: nodeData, mutate } = useSWR(shouldFetch ? { appID: appDetail.id } : null, fetchNodeList)

  // 将获取到的节点数据转换为 Select 的 options 格式
  const modelOptions = useMemo(() => {
    const allData = [{ value: '', label: t('appLog.filter.datasetType.all') }]
    if (!nodeData?.data?.length)
      return allData
    const options = nodeData?.data?.map((item: any, index) => ({
      value: item.node_id,
      label: `${item.name}-${item.node_id}`,
    }))

    return ([...allData, ...options])
  }, [nodeData])

  // 日志筛选最小天数判断
  const minDate = useMemo(() => {
    if (queryDays)
      return dayjs(today).subtract((queryDays - 1), 'day')
  }, [today, queryDays])

  // 排序选择
  const sortOption = [
    { key: 'created_at', label: t('appLog.table.header.time'), title: t('appLog.table.header.time') },
    { key: 'updated_at', label: t('appLog.table.header.updatedTime'), title: t('appLog.table.header.updatedTime') },
  ]
  // 排序组合
  const {
    order,
    sort,
  } = useMemo(() => {
    return {
      order: queryParams.sort_by?.startsWith('-') || false,
      sort: queryParams.sort_by?.replace('-', '') || 'created_at',
    }
  }, [queryParams.sort_by])
  // 选中的排序名称
  const sortName = useMemo(() => {
    return sortOption.find(item => item.key === sort)?.label
  }, [sort])

  // 变更排序
  const changeSort = (curSort: string, curOrder: boolean) => {
    setQueryParams({
      ...queryParams, sort_by: `${curOrder ? '-' : ''}${curSort}`,
    })
  }
  const onRangeChange = (dates: any, dateStrings: any) => {
    if (dates) {
      setQueryParams({
        ...queryParams,
        start: dayjs(dateStrings[0]).startOf('day').format('YYYY-MM-DD HH:mm'),
        end: dayjs(dateStrings[1]).endOf('day').format('YYYY-MM-DD HH:mm'),
      })
    }
    else {
      const { start, end, ...props } = queryParams
      setQueryParams(props)
    }
  }
  const changeLike = (value: string) => {
    setQueryParams({ ...queryParams, like: likeParamMap[value as LikeEnum] })
  }
  const changeSource = (value: string) => {
    setQueryParams({ ...queryParams, source: value })
  }
  const changeDatasetType = (value: string) => {
    setQueryParams({ ...queryParams, dataset_type: value })
  }
  const likeParamMap: Record<LikeEnum, likeParam> = {
    [LikeEnum.All]: {},
    [LikeEnum.Like]: { is_like: 1 },
    [LikeEnum.Dislike]: { is_dislike: 1 },
    [LikeEnum.LikeAndNoDislike]: { is_like: 1, is_dislike: 0 },
    [LikeEnum.NoLikeAndDislike]: { is_like: 0, is_dislike: 1 },
    [LikeEnum.NoLikeAndNoDislike]: { is_like: 0, is_dislike: 0 },
  }

  // 赞踩情况列选项
  const likeOptions = Object.keys(LikeEnum).map(key => ({
    value: LikeEnum[key as keyof typeof LikeEnum],
    name: LikeEnum[key as keyof typeof LikeEnum],
  }))

  // 数据来源, 工作流不包含本地数据源
  let chatSourceList = Object.keys(ChatSourceEnum).map(item => ({ label: t(`appLog.filter.source.${item}`), value: item === ChatSourceEnum.all ? '' : item }))
  if (appDetail?.mode === 'advanced-chat')
    chatSourceList = chatSourceList.filter(item => item.value !== ChatSourceEnum.local)

  return (
    <div className='flex justify-between items-center mb-4'>
      <div className='flex gap-4 items-center text-gray-G1'>
        {/* 日志时间 */}
        <RangePicker
          onChange={onRangeChange}
          maxDate={dayjs(today)}
          minDate={minDate}
        />
        {/* 关键词 */}
        <SearchInput
          className='w-[250px]'
          placeholder={t('common.operation.searchKeyword')!}
          value={queryParams.keyword}
          onChange={(value: string) => {
            setQueryParams({ ...queryParams, keyword: value })
          }}
        ></SearchInput>
        {/* 大模型节点 */}
        {appDetail?.mode === 'advanced-chat' && (
          <Select
            showSearch
            className='w-[250px]'
            options={modelOptions} // 下拉选项
            value={queryParams.node_id } // 绑定当前值
            placeholder={t('common.operation.searchModelKeyword')}
            filterOption={(input, option) =>
              (option?.label as string).toLowerCase().includes(input.toLowerCase())
            }
            onChange={(value) => {
              setQueryParams({ ...queryParams, node_id: value })
            }}
            onDropdownVisibleChange={(open) => {
              if (open)
                mutate() // 手动刷新数据
            }}
          />
        )}
        {/* 排序 */}
        <PopoverSelect
          showCheck={false}
          options={sortOption}
          defaultValue={sort}
          onChange={value => changeSort(value, order)}
          triggerNode={open => (
            <div className={cn(
              'shrink-0 flex gap-2 items-center px-[11px] h-[36px] rounded cursor-pointer border border-gray-G5 hover:border-primary-P1 bg-white',
              open && 'border-primary-P1',
            )}>
              <div className='text-gray-G1 text-S3'>{t('appLog.filter.sortBy')}{sortName}</div>
              <ArrowDown className={cn('w-4 h-4 text-gray-G2', open && 'rotate-180')} />
            </div>
          )}
        >
        </PopoverSelect>
        {/* 排序 */}
        <TextButton variant='hover' onClick={() => changeSort(sort, !order)}>
          {order && <Sort className='w-4 h-4' />}
          {!order && <Sort className='w-4 h-4 rotate-180' />}
        </TextButton>
        <Divider className='h-[24px] m-0' type='vertical'></Divider>
        {/* 赞踩情况 */}
        <Select
          className='w-[130px]'
          options={likeOptions.map(item => ({ value: item.value, label: t(`appLog.filter.likeOrDislike.${item.name}`) }))}
          placeholder={t('appLog.filter.likeOrDislike.title')}
          onSelect={changeLike}
        />
        {/* 数据来源 */}
        <Select
          className='w-[130px]'
          options={chatSourceList}
          placeholder={t('appLog.filter.source.title')}
          onSelect={changeSource}
        />
        {/* 数据状态 */}
        {showDatasetReflux
          && <Select
            className='w-[120px]'
            options={appDetail?.mode === 'advanced-chat' ? datasetTypeOptions : datasetTypeOptions_ZZ}
            placeholder={t('appLog.filter.datasetType.title')}
            onChange={value => changeDatasetType(value)}
          />
        }
      </div>
      <div>
        {
          onNewDataReflux && showDatasetReflux && (
            <Tooltip disabled={!dataRefluxDisabled} popupContent={dataRefluxDisabledTip}>
              <Button
                variant='primary'
                onClick={onNewDataReflux}
                disabled={dataRefluxDisabled}
              >
                <Add className='w-4 h-4' />
                {t('appLog.dataReflux.create')}
              </Button>
            </Tooltip>
          )
        }
      </div>
    </div>
  )
}

export default Filter
