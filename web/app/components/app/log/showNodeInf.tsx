'use client'
import { useState , useEffect ,useMemo  } from 'react'
import { useTranslation } from 'react-i18next'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import { ToastContext } from '@/app/components/base/toast'
import { useContext } from 'use-context-selector'
import { Table } from 'antd'
import { fetchNodeDetails } from '@/service/log'
import type {  DataRefluxListResponse } from './type'
import type { NodeDetailsResponse } from '@/service/log'
import { datasetTypeList } from '../data-reflux/type'

type DataDisplayModalProps = {
  isOpen: boolean
  onClose: () => void
  appID?: string
  rowData: {
    node_id?: string
    user_id?: string
    dataset_size?: number
    dataset_type?: string
  }
}


const DataDisplayModal = ({appID, isOpen, onClose, rowData }: DataDisplayModalProps) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const [copied, setCopied] = useState(false)



  // 状态管理
  const [loading, setLoading] = useState(false)
  const [nodeData, setNodeData] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

    // 数据加载副作用
    useEffect(() => {
      if (isOpen && rowData) {
        const loadData = async () => {
          try {
            setLoading(true)
            setError(null)
            // 调用API获取节点详情
            // console.log("调用接口1" ,appID,rowData)
            const data = await fetchNodeDetails({ 
                appID: appID,
                params:{
                  dataset_uuid:rowData
                }
            })
            const adaptedData = data.node_ids.map((nodeId: string, index: number) => ({
              node_id: nodeId,
              user_id: data.user_id || '--',
              dataset_size: data.dataset_size?.[index] ?? '--',
              dataset_type: data.dataset_type || '--'
            }));
            setNodeData(adaptedData);
            // console.log("调用接口2" , data)
            // setNodeData(data)
          } catch (err) {
            setError('Failed to load node details')
            notify({ 
              type: 'error', 
              message: t('appDataReflux.tip.fetchNodeFailed') 
            })
          } finally {
            setLoading(false)
          }
        }
        loadData()
      }
    }, [isOpen, rowData])

  const columns = [
    {
            title: t('appDataReflux.action.table.header.nodeName'),
            dataIndex: 'node_id',
            key: 'node_id',
            width: 100,
            onCell: () => ({ style: { fontWeight: 600, width: '150px' } }),
            render: (text: string) => text || '--'
          },
          {
            title: t('appDataReflux.action.table.header.user'),
            dataIndex: 'user_id',
            key: 'user',
            width: 80,
            onCell: () => ({ style: { fontWeight: 600, width: '150px' } }),
            render: (text: string) => text || '--'
          },
          {
            title: t('appDataReflux.action.table.header.conversationCount'),
            dataIndex: 'dataset_size',
            key: 'conversation_count',
            width: 60,
            onCell: () => ({ style: { fontWeight: 600, width: '150px' } }),
            render: (text: number) => text?.toString() || '--'
          },
          {
            title: t('appDataReflux.action.table.header.dataType'),
            dataIndex: 'dataset_type',
            key: 'dataset_type',
            width: 80,
            onCell: () => ({ style: { fontWeight: 600, width: '150px' } }),
            render: (value) => {
              const matchedType = datasetTypeList.find(item => item.value === value)
              return matchedType?.label || value // 匹配不到时返回原始值
            }
          }
  ]

  return (
    <Modal
      title={t('appDataReflux.action.gobackNode')}//回流溯源
      isShow={isOpen}
      onClose={onClose}
      // 添加 closable 属性显示右上角关闭按钮
        closable={true}
      width={600}
      footer={<Button onClick={onClose}>{t('appDataReflux.action.finish')}</Button>}
    >
      <div className="p-4">
        <Table
          dataSource={nodeData}
          columns={columns}
          pagination={false}
          bordered={false}
          size="small"
          className="bg-gray-50"
          rowClassName={() => 'hover:bg-gray-100 transition-colors'}
          scroll={{ y: 400 }}
        />
      </div>
    </Modal>
  )
}

export default DataDisplayModal