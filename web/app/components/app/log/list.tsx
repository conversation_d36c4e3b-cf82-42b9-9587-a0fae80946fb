'use client'
import type { FC } from 'react'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import useSWR from 'swr'
import {
  HandThumbDownIcon,
  HandThumbUpIcon,
} from '@heroicons/react/24/outline'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { createContext, useContext } from 'use-context-selector'
import { useShallow } from 'zustand/react/shallow'
import { useTranslation } from 'react-i18next'
import type { TableColumnsType } from 'antd'
import { Divider, Table } from 'antd'
import { debounce } from 'lodash-es'

import s from './style.module.css'
import VarPanel from './var-panel'
import { ChatSourceEnum, DatasetTypeEnum } from './type'
import cn from '@/utils/classnames'
import type { ChatConversationGeneralDetail, ChatConversationsResponse, ChatMessage, ChatMessagesRequest, ChildConversation } from '@/models/log'
import type { App } from '@/types/app'
import { fetchChatConversationDetail, fetchChatMessages, updateLogMessageFeedbacks } from '@/service/log'
import { addFileInfos, sortAgentSorts } from '@/app/components/tools/utils'
import { useStore as useAppStore } from '@/app/components/app/store'
import { useAppContext } from '@/context/app-context'
import useTimestamp from '@/hooks/use-timestamp'
import { fetchAppChatBgConfig } from '@/service/apps'
import { getCropperFontCss } from '@/app/components/app/common/bg-cropper/utils'
// 公共组件
import { ToastContext } from '@/app/components/base/toast'
import CopyBtn from '@/app/components/base/button/copy-button'
import Drawer from '@/app/components/base/drawer'
import Chat from '@/app/components/base/chat/pref-chat'
import type { ScrollbarRef } from '@/app/components/base/scrollbar'
import Scrollbar from '@/app/components/base/scrollbar'
import MessageLogModal from '@/app/components/base/log-modal/message'
import Indicator from '@/app/components/base/indicator'
import { getProcessedFilesFromResponse } from '@/app/components/base/file-uploader/utils'
import type { AppChatBgConfig } from '@/app/components/base/features/types'
import CropperWrapper from '@/app/components/base/cropper/wrapper'
import { Close } from '@/app/components/base/icons/src/vender/line/general'
import Tooltip from '@/app/components/base/tooltip'
import type { FeedbackType, IChatItem, OnFeedback } from '@/types/chat'

// 在 ConversationList 中引入
import SubChatTable from '@/app/components/app/log/subchattable'

dayjs.extend(utc)
dayjs.extend(timezone)

type IConversationList = {
  logs?: ChatConversationsResponse
  appDetail: App
  loading: boolean
  onRefresh: () => void
  selectedRowKeys: React.Key[]
  onSelect: (selectedRowKeys: React.Key[], selectedRows: ChatConversationGeneralDetail[]) => void
}

const defaultValue = '-'

type IDrawerContext = {
  onClose: () => void
  appDetail?: App
}

const DrawerContext = createContext<IDrawerContext>({} as IDrawerContext)

/**
 * Icon component with numbers
 */
const HandThumbIconWithCount: FC<{ count: number; iconType: 'up' | 'down' }> = ({
  count,
  iconType,
}) => {
  const classname = iconType === 'up' ? 'text-primary-600 bg-primary-50' : 'text-red-600 bg-red-50'
  const Icon = iconType === 'up' ? HandThumbUpIcon : HandThumbDownIcon
  return (
    <div
      className={`inline-flex items-center w-fit rounded-md p-1 text-xs ${classname} mr-1 last:mr-0`}
    >
      <Icon className={'h-3 w-3 mr-0.5 rounded-md'} />
      {count > 0 ? count : null}
    </div>
  )
}

// Format interface data for easy display
const getFormattedChatList = (
  messages: ChatMessage[],
  conversationId: string,
  timezone: string,
  format: string,
) => {
  const newChatList: IChatItem[] = []
  messages.forEach((item: ChatMessage) => {
    const answerFiles = item.message_files?.filter((file: any) => file.belongs_to === 'assistant') || []
    const questionFiles = item.message_files?.filter((file: any) => file.belongs_to === 'user') || []
    newChatList.push({
      id: item.id,
      content: item.answer, // 答
      agent_thoughts: addFileInfos(
        item.agent_thoughts ? sortAgentSorts(item.agent_thoughts) : item.agent_thoughts,
        item.message_files,
      ),
      feedback: item.feedbacks.find(item => item.from_source === 'user'), // user feedback
      adminFeedback: item.feedbacks.find(item => item.from_source === 'admin'), // admin feedback
      feedbackDisabled: false,
      isAnswer: true,
      message_files: getProcessedFilesFromResponse(answerFiles.map((item: any) => ({ ...item, related_id: item.id }))),
      log: [
        ...item.message,
        ...(item.message[item.message.length - 1]?.role !== 'assistant'
          ? [
            {
              role: 'assistant',
              text: item.answer,
              files: getProcessedFilesFromResponse(answerFiles.map((item: any) => ({ ...item, related_id: item.id }))),
            },
          ]
          : []),
      ],
      workflow_run_id: item.workflow_run_id,
      conversationId,
      input: {
        inputs: item.inputs,
        query: item.query,
      },
      more: {
        time: dayjs.unix(item.created_at).tz(timezone).format(format),
        tokens: item.answer_tokens + item.message_tokens,
        latency: item.provider_response_latency.toFixed(2),
      },
      citation: item.metadata?.retriever_resources,
      annotation: (() => {
        if (item.annotation_hit_history) {
          return {
            id: item.annotation_hit_history.annotation_id,
            authorName: item.annotation_hit_history.annotation_create_account?.name || '-',
            created_at: item.annotation_hit_history.created_at,
          }
        }

        if (item.annotation) {
          return {
            id: item.annotation.id,
            authorName: item.annotation.account.name,
            logAnnotation: item.annotation,
            created_at: 0,
          }
        }

        return undefined
      })(),
    })
    newChatList.push({
      id: `question-${item.id}`,
      content: item.inputs.query || item.inputs.default_input || item.query, // text generation: item.inputs.query; chat: item.query
      isAnswer: false,
      message_files: getProcessedFilesFromResponse(questionFiles.map((item: any) => ({ ...item, related_id: item.id }))),
    })
  })
  // 接口返回的聊天对话顺序反了，上面的提问与对话顺序也需要调整
  return newChatList.reverse()
}

// 子节点对话数据处理
const getChildNodeFormattedChatList = (
  messages: ChatMessage[],
  conversationId: string,
  timezone: string,
  format: string,
  roleInstruction?: string,
  isChildNode?: boolean,
) => {
  const newChatList: IChatItem[] = []
  messages.forEach((item: ChatMessage) => {
    // 正确提取当前项的文件
    const answerFiles = item.message_files?.filter((file: any) => file.belongs_to === 'assistant') || []
    const questionFiles = item.message_files?.filter((file: any) => file.belongs_to === 'user') || []

    // 分割线
    const dividerH = (
      <div className='px-0 mb-[20px]'>
        <Divider className='!my-0' type='horizontal'></Divider>
      </div>
    )
    const chatLength_A = (messages.length - 1) * 2
    // 工作流增加角色指令：根据id，调用 node-  接口
    const roleInstructionPrefix = (
      <div>
        {
          chatLength_A === newChatList.length ? '' : dividerH
        }
        <div className='text-[14px] font-normal'>
          <span className='text-[#181818]'>{roleInstruction}: &nbsp;</span>
          <span className='text-[#5c6273]'>{item?.role}</span>
        </div>
      </div>
    )
    newChatList.push({
      id: item.id,
      content: item.answer,
      feedbackDisabled: false,
      isAnswer: true,
      message_files: getProcessedFilesFromResponse(answerFiles.map((item: any) => ({ ...item, related_id: item.id }))),
      isLog: false,
      isFeedback: false,
      // 添加以下字段以支持工具和检索展示
      agent_thoughts: addFileInfos(
        item.agent_thoughts ? sortAgentSorts(item.agent_thoughts) : item.agent_thoughts,
        item.message_files,
      ),
      citation: item.metadata?.retriever_resources, // 检索信息
      // 添加其他必要字段
      workflow_run_id: item.workflow_run_id,
    })
    newChatList.push({
      id: `question-${item.id}`,
      content: item.inputs.query || item.inputs.default_input || item.query,
      isAnswer: false,
      message_files: getProcessedFilesFromResponse(questionFiles.map((item: any) => ({ ...item, related_id: item.id }))),
      prefix: isChildNode ? roleInstructionPrefix : '',
    })
  })
  return newChatList.reverse()
}

type IDetailPanel = {
  detail: any
  chatBgConfig?: AppChatBgConfig
  isNodeClicked: boolean // 添加此行
  isChildNode?: boolean
  fatherId?: string
  onFeedback: OnFeedback
}

function DetailPanel({ detail, onFeedback, chatBgConfig, isNodeClicked, isChildNode, fatherId }: IDetailPanel) {
  const { userProfile: { timezone } } = useAppContext()
  const { onClose, appDetail } = useContext(DrawerContext)
  const {
    currentLogItem,
    setCurrentLogItem,
    showMessageLogModal,
    setShowMessageLogModal,
    currentLogModalActiveTab,
  } = useAppStore(
    useShallow(state => ({
      currentLogItem: state.currentLogItem,
      setCurrentLogItem: state.setCurrentLogItem,
      showMessageLogModal: state.showMessageLogModal,
      setShowMessageLogModal: state.setShowMessageLogModal,
      currentLogModalActiveTab: state.currentLogModalActiveTab,
    })),
  )

  const { t } = useTranslation()
  const [items, setItems] = React.useState<IChatItem[]>([])
  // 是否还有更多聊天记录
  const [hasMore, setHasMore] = useState(true)
  const [varValues, setVarValues] = useState<Record<string, string>>({})
  const scrollRef = useRef<ScrollbarRef>(null)
  const isChatMode = appDetail?.mode !== 'completion'
  const message_files = (!isChatMode && detail?.message?.message_files && detail?.message?.message_files.length > 0)
    ? detail?.message?.message_files?.map((item: any) => item.url)
    : []

  const [width, setWidth] = useState(0)
  const ref = useRef<HTMLDivElement>(null)
  // 第一风格字体颜色
  const firstColorCss = useMemo(() => {
    return chatBgConfig?.enabled ? ((chatBgConfig.fontColor === 'black' || chatBgConfig.fontColor === undefined) ? 'text-gray-G3' : 'text-gray-G4') : 'text-gray-G3'
  }, [chatBgConfig])
  // 第二风格字体颜色
  const secondColorCss = useMemo(() => {
    return chatBgConfig?.enabled ? ((chatBgConfig.fontColor === 'black' || chatBgConfig.fontColor === undefined) ? 'text-gray-G2' : 'text-gray-G5') : 'text-gray-G2'
  }, [chatBgConfig])
  // 变量列表
  const varList = (detail?.model_config as any)?.user_input_form?.map((item: any) => {
    const itemContent = item[Object.keys(item)[0]]
    return {
      label: itemContent.variable,
      value: varValues[itemContent.variable] || detail.message?.inputs?.[itemContent.variable],
    }
  }) || []

  // 父节点对话面板（保持原有）
  const fatherData = async () => {
    try {
      if (!hasMore)
        return
      const params: ChatMessagesRequest = {
        conversation_id: detail.id,
        limit: 10,
      }
      if (items?.[0]?.id)
        params.first_id = items?.[0]?.id.replace('question-', '')

      const messageRes = await fetchChatMessages({
        url: `/apps/${appDetail?.id}/chat-messages`,
        params,
      })
      if (messageRes.data.length > 0) {
        const varValues = messageRes.data[0].inputs
        setVarValues(varValues)
      }
      const newItems = [...getFormattedChatList(messageRes.data, detail.id, timezone!, t('appLog.dateTimeFormat') as string), ...items]
      // 没有更多的时候，插入自我介绍？
      if (messageRes.has_more === false && detail?.model_config?.configs?.introduction) {
        newItems.unshift({
          id: 'introduction',
          isAnswer: true,
          isOpeningStatement: true,
          content: detail?.model_config?.configs?.introduction ?? 'hello',
          feedbackDisabled: true,
        })
      }
      setItems(newItems)
      setHasMore(messageRes.has_more)
    }
    catch (err) {
      console.error(err)
    }
  }
  // 新增子节点
  const childData = async () => {
    try {
      if (!hasMore)
        return
      const params: ChatMessagesRequest = {
        // conversation_id: detail.id,
        conversation_id: fatherId || '',
        node_id: detail.nodeId,
        model_name: detail.model || '',
        limit: 10,
        dataset_type: detail.dataset_type || '',
      }
      if (items?.[0]?.id)
        params.first_id = items?.[0]?.id.replace('question-', '')

      // TODO：子节点data接口待完善
      const messageRes = await fetchChatMessages({
        url: `/apps/${appDetail?.id}/chat-nodes`,
        params,
      })

      // 在获取到真实数据后，可以添加额外的 message_files 信息
      if (messageRes && messageRes.data) {
        messageRes.data.forEach((item: any) => {
          // 如果没有 message_files 字段，创建一个空数组
          if (!item.message_files) {
            item.message_files = [];
          }
        });
      }



      // 新增：子节点对话数据处理
      const lists: any[] = [];
      (messageRes?.data || []).map((item: any) => {
        const role_system = (item.process_data?.prompts || []).find((item: any) => item.role === 'system')
        const role_user = (item.process_data?.prompts || []).find((item: any) => item.role === 'user')

        // 正确提取当前项的 message_files
        const answerFiles = item.message_files?.filter((file: any) => file.belongs_to === 'assistant') || []
        const questionFiles = item.message_files?.filter((file: any) => file.belongs_to === 'user') || []

        // 确保文件对象包含所有必要字段
        const processedAnswerFiles = answerFiles.map((file: any) => ({
          ...file,
          related_id: file.related_id || file.id,
          url: file.url || ''
        }));

        const processedQuestionFiles = questionFiles.map((file: any) => ({
          ...file,
          related_id: file.related_id || file.id,
          url: file.url || ''
        }));

        // 提取 agent thoughts 和 citation 信息
        const agentThoughts = item.agent_thoughts ? sortAgentSorts(item.agent_thoughts) : [];
        const retrieverResources = item.metadata?.retriever_resources;


        return lists.push({
          id: item.id,
          answer: item.outputs?.text,
          query: role_user?.text,
          inputs: item.inputs || {},
          message_files: [...processedAnswerFiles, ...processedQuestionFiles], // 合并所有文件
          role: role_system?.text || '-',
          agent_thoughts: addFileInfos(agentThoughts, item.message_files), // 添加 agent thoughts
          metadata: item.metadata, // 添加元数据，包含检索信息
          workflow_run_id: item.workflow_run_id, // 添加工作流ID
        })
      })
      if (messageRes.data.length > 0) {
        const varValues = messageRes.data[0].inputs
        setVarValues(varValues)
      }
      const newItems = [...getChildNodeFormattedChatList(lists, detail.id, timezone!, t('appLog.dateTimeFormat') as string, t('appLog.roleInstruction') as string, isChildNode), ...items]

      // 没有更多的时候，插入自我介绍？
      if (messageRes.has_more === false && detail?.model_config?.configs?.introduction) {
        newItems.unshift({
          id: 'introduction',
          isAnswer: true,
          isOpeningStatement: true,
          content: detail?.model_config?.configs?.introduction ?? 'hello',
          feedbackDisabled: true,
        })
      }
      // console.log(newItems, '===对话数据处理结果', appDetail , '===标识')
      setItems(newItems)
      setHasMore(messageRes.has_more)
    }
    catch (err) {
      console.error(err)
    }
  }
  // 获取聊天详情
  const fetchData = async () => {
    if (isChildNode) {
      // 新增子节点
      childData()
    }
    else {
      fatherData()
    }
  }

  // 工具函数————start
  const onScrollY = debounce(async () => {
    const container = scrollRef.current?.getContainer()
    if (container) {
      const { scrollTop, clientHeight, scrollHeight } = container
      if (scrollHeight - 100 <= scrollTop + clientHeight)
        await fetchData()
    }
  }, 200)
  const adjustModalWidth = () => {
    if (ref.current)
      setWidth(document.body.clientWidth - (ref.current?.clientWidth + 16) - 8)
  }
  // 工具函数————end

  useEffect(() => {
    adjustModalWidth()
  }, [])
  useEffect(() => {
    if (appDetail?.id && detail.id && appDetail?.mode !== 'completion')
      fetchData()
  }, [appDetail?.id, detail.id, appDetail?.mode])

  const chatLeftCon = () => {
    return (
      <div className='flex items-center w-full text-S3 leading-H3'>
        <div className={firstColorCss}>{isNodeClicked
          ? t('appLog.detail.nodeId') // 使用 nodeID 标签
          : t('appLog.detail.conversationId')}：
        </div>
        <div title={detail.id} className={cn('truncate mr-1', secondColorCss)}>
          {isChildNode ? detail.nodeId : detail.id}
        </div>
        <CopyBtn className={secondColorCss} value={detail.id} />
      </div>
    )
  }
  const closeButton = () => {
    return <Close className={cn('w-4 h-4  cursor-pointer', secondColorCss)} onClick={onClose} />
  }
  const agentHeaderCon = () => {
    return (
      <div className='py-4 px-6 flex items-center justify-between'>
        {/* 左侧部分 */}
        {chatLeftCon()}
        {/* 关闭按钮 */}
        {closeButton()}
      </div>
    )
  }
  const advancedHeaderCon = () => {
    return (
      <div className='py-4 px-6'>
        <div className=' flex items-center justify-between'>
          {
            (appDetail?.mode === 'advanced-chat' && isChildNode) && (
              <div className='text-[#272e47] font-medium mb-[6px]'>{detail?.name ?? ' '}</div>
            )
          }
          {/* 关闭按钮 */}
          {closeButton()}
        </div>
        <div>
          {chatLeftCon()}
        </div>
      </div>
    )
  }
  return (
    <>
      <CropperWrapper
        ref={ref}
        config={chatBgConfig?.enabled ? chatBgConfig.narrow : undefined}
        className={cn(s.appDebugWrap)}
        header={
          <>
            {/* Panel Header */}
            {/* 左侧部分 */}
            {/* {
              appDetail?.mode === 'advanced-chat' && isChildNode ? advancedHeaderCon() : agentHeaderCon()
            } */}
            {agentHeaderCon()}
            <div className='px-6'>
              <Divider className='!my-0' type='horizontal'></Divider>
            </div>
            {/* Panel Body */}
            {(varList.length > 0 || (!isChatMode && message_files.length > 0)) && (
              <div className='px-6 pt-4 pb-2'>
                <VarPanel
                  varList={varList}
                  message_files={message_files}
                />
              </div>
            )}
          </>
        }
      >
        {/* 聊天信息 */}
        <Scrollbar
          onScrollY={onScrollY}
          ref={scrollRef}
        >
          <Chat
            className='!h-auto'
            answerIcon={appDetail?.icon_url || ''}
            config={{
              appId: appDetail?.id,
              text_to_speech: {
                enabled: true,
              },
              supportAnnotation: true,
              annotation_reply: {
                enabled: true,
              },
              supportFeedback: true,
            } as any}
            chatList={items}
            onFeedback={onFeedback}
            noChatInput
            showPromptLog
            hideProcessDetail
            chatContainerClassName='!h-auto mx-auto w-full max-w-[800px]'
            chatContainerInnerClassName={cn('px-6', getCropperFontCss(chatBgConfig))}
          />
        </Scrollbar>
      </CropperWrapper>
      {showMessageLogModal && (
        <MessageLogModal
          width={width}
          currentLogItem={currentLogItem}
          onCancel={() => {
            setCurrentLogItem()
            setShowMessageLogModal(false)
          }}
          defaultTab={currentLogModalActiveTab}
        />
      )}
    </>
  )
}

// 对话详情包裹
const ChatConversationDetailComp: FC<{ appId?: string; conversationId?: string; isNodeClicked?: boolean; isChildNode?: boolean; fatherId?: string; chatData?: any }> = ({ appId, conversationId, isNodeClicked, isChildNode = false, fatherId = '', chatData = {} }) => {
  const { notify } = useContext(ToastContext)
  const { t } = useTranslation()

  // 通过应用id和对话id获取详情
  const detailParams = { url: `/apps/${appId}/chat-conversations/${conversationId}` }
  const { data: conversationDetail } = useSWR(() => (appId && conversationId) ? detailParams : null, fetchChatConversationDetail)

  // 获取子节点详情=》临时接口；勿删
  // const detailChildParams = { url: `/apps/${appId}/chat-nodes?conversation_id=${fatherId}&node_id=${conversationId}` }
  // const { data: conversationDetailChild } = useSWR(() => (appId && conversationId) ? detailChildParams : null, fetchChatNodesMessages)

  // 获取背景图配置
  const { data: chatBgConfig } = useSWR(() => ({ appId: appId! }), fetchAppChatBgConfig, { revalidateOnFocus: false })

  // 更新feedback(赞踩反馈)
  // const handleFeedback = async (mid: string, { rating }: FeedbackType): Promise<boolean> => {
  const handleFeedback = async (mid: string, bodyValue: FeedbackType): Promise<boolean> => {
    const body = {
      ...bodyValue,
      message_id: mid,
    }
    try {
      await updateLogMessageFeedbacks({
        url: `/apps/${appId}/feedbacks`,
        body,
      })
      notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
      return true
    }
    catch (err) {
      notify({ type: 'error', message: t('common.actionMsg.modifiedUnsuccessfully') })
      return false
    }
  }

  if (!conversationDetail)
    return null

  return <DetailPanel
    detail={isChildNode ? chatData : conversationDetail}
    onFeedback={handleFeedback}
    chatBgConfig={chatBgConfig?.data as AppChatBgConfig}
    isNodeClicked={isNodeClicked ?? false} // 提供默认值
    isChildNode={isChildNode ?? false} // 标识是否子节点
    fatherId={fatherId ?? ''} // 父节点
  />
}
// 对话列表
const ConversationList: FC<IConversationList> = ({
  logs,
  appDetail,
  loading,
  onRefresh,
  selectedRowKeys,
  onSelect,
}) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([])
  const [subRowKeys, setSubRowKeys] = useState<React.Key[]>([])

  const [expandedSubRows, setExpandedSubRows] = useState<Record<string, any>>({}) // 存储子表格选中状态
  const [isNodeClicked, setIsNodeClicked] = useState<boolean>(false)
  // 是否显示日志详情抽屉
  const [showDrawer, setShowDrawer] = useState<boolean>(false) // Whether to display the chat details drawer
  // 当前日志
  const [currentConversation, setCurrentConversation] = useState<ChatConversationGeneralDetail>() // Currently selected conversation

  const handleSubRowSelect = useCallback((parentId: string, keys: React.Key[], records: any[]) => {
    // 单独存储子表 keys
    setSubRowKeys(keys)

    setExpandedSubRows(prev => ({
      ...prev,
      [parentId]: { keys, records },
    }))
  }, [])

  // 在组件内部添加状态
  const [selectedRows, setSelectedRows] = useState<ChatConversationGeneralDetail[]>([])

  // 合并父子表格选中项
  const mergedSelectedRows = useMemo(() => {
    return [
      ...selectedRows, // 父级选中项
      ...Object.values(expandedSubRows).flatMap(item => item.records), // 子级选中项
    ]
  }, [selectedRows, expandedSubRows])

  // 使用 useMemo 提升性能
  const effectiveRowKeys = useMemo(() => {
    if (appDetail?.mode === 'advanced-chat')
      return subRowKeys // ✅ 高级模式下使用子表 keys

    return selectedRowKeys // 默认使用主表 keys
  }, [appDetail?.mode, selectedRowKeys, subRowKeys])

  // 当合并选中项变化时通知父级
  useEffect(() => {
    const effectiveRows = appDetail?.mode === 'advanced-chat'
      ? Object.values(expandedSubRows).flatMap(item => item.records) // 子表 rows
      : mergedSelectedRows // 主表 + 子表合并 rows

    onSelect(effectiveRowKeys, effectiveRows)
  }, [effectiveRowKeys, expandedSubRows, appDetail?.mode])

  const expandableConfig = useMemo(() => {
    if (appDetail?.mode !== 'advanced-chat')
      return undefined

    return {
      expandedRowKeys,
      indentSize: 0, // 👈 添加此行，去除默认缩进
      onExpand: (expanded: boolean, record: ChatConversationGeneralDetail) => {
        const keys = expanded
          ? [...expandedRowKeys, record.id]
          : expandedRowKeys.filter(key => key !== record.id)
        setExpandedRowKeys(keys)
      },
      expandedRowRender: (record: any) => (
        <SubChatTable
          conversationId={record.id}
          appId={appDetail.id}
          logData={logs?.data?.filter(item => item.id === record.id) || []}
          appDetail={appDetail}
          defaultSelectedRowKeys={
            expandedSubRows[record.id]?.keys || []
          } // 👈 添加此行
          onSubRowSelect={(keys, records) => handleSubRowSelect(record.id, keys, records)} // 传递回调
          onRowClick={(data) => {
            setShowDrawer(true) // 显示对话框
            setCurrentConversation({
              // ...record,
              ...data,
              isChildNode: true,
              conversation_id: record.id, // 父节点id
            })
            setIsNodeClicked(true) // 设置点击状态为 true
            // console.log(record, '===record=子===')
          }}
        />
      ),
      onExpandedRowsChange: (keys: any) => {
        if (appDetail?.mode !== 'advanced-chat')
          setExpandedRowKeys([])
      },
    }
  }, [appDetail?.mode, expandedRowKeys, logs])

  // 报变量定义之前就已使用，移至上方
  // // 是否显示日志详情抽屉
  // const [showDrawer, setShowDrawer] = useState<boolean>(false) // Whether to display the chat details drawer
  // // 当前日志
  // const [currentConversation, setCurrentConversation] = useState<ChatConversationGeneralDetail>() // Currently selected conversation

  const { setCurrentLogItem, setShowAgentLogModal } = useAppStore(useShallow(state => ({
    setCurrentLogItem: state.setCurrentLogItem,
    setShowAgentLogModal: state.setShowAgentLogModal,
  })))

  // 关闭抽屉
  const onCloseDrawer = () => {
    onRefresh()
    setShowDrawer(false)
    setCurrentConversation(undefined)
    setIsNodeClicked(false) // 重置点击状态为 false
  }
  // 数据来源, 工作流不包含本地数据源
  const chatSourceList = Object.keys(ChatSourceEnum).map(item => ({ label: t(`appLog.filter.source.${item}`), value: item }))
  // 数据类型
  const dataTypeList = Object.keys(DatasetTypeEnum).map(item => ({ label: t(`appLog.filter.dataType.${item}`), value: item }))
  // 表格列
  const columns: TableColumnsType<any> = [
    // 新增展开列（可选）
    ...(appDetail?.mode === 'advanced-chat'
      ? [{
        title: '',
        key: 'expand',
        width: 30,
        onCell: () => ({ style: { padding: '0 8px' } }),
      }]
      : []),
    // 标题
    {
      title: t('appLog.table.header.summary'),
      key: 'summary',
      render: (_: any, log) => {
        return <>
          {!log.read_at
            ? <Indicator color='blue' textClassName='text-S3 leading-H3 text-gray-G1 truncate' title={log.name}>{log.name}</Indicator>
            : log.name
          }
        </>
      },
      ellipsis: true,
    },
    // 用户或账户
    {
      title: t('appLog.table.header.endUser'),
      key: 'endUser',
      render: (_: any, log) => {
        return log.from_end_user_session_id || log.from_account_name || defaultValue
      },
      ellipsis: true,
    },
    // 数据来源
    {
      title: t('appLog.table.header.source'),
      key: 'source',
      render: (_: any, log) => {
        return chatSourceList.find(item => item.value === log.source)?.label || '-'
      },
      ellipsis: true,
    },
    // 数据类型
    {
      title: t('appDataReflux.table.header.datasetType'),
      key: 'datasetType',
      render: (_: any, log) => {
        return dataTypeList.find(item => item.value === log.dataset_type)?.label || '-'
      },
      ellipsis: true,
    },
    // 消息数
    {
      title: t('appLog.table.header.messageCount'),
      key: 'messageCount',
      dataIndex: 'message_count',
      ellipsis: true,
    },
    // 用户反馈
    {
      title: t('appLog.table.header.userRate'),
      key: 'userRate',
      render: (_: any, log) => {
        return (!log.user_feedback_stats.like && !log.user_feedback_stats.dislike)
          ? defaultValue
          : <>
            {!!log.user_feedback_stats.like && <HandThumbIconWithCount iconType='up' count={log.user_feedback_stats.like} />}
            {!!log.user_feedback_stats.dislike && <HandThumbIconWithCount iconType='down' count={log.user_feedback_stats.dislike} />}
          </>
      },
      width: 150,
      ellipsis: true,
    },
    // 反馈内容
    {
      title: t('appLog.table.header.reteContent'),
      key: 'userRate',
      render: (_: any, log) => {
        const tooltipContent = () => {
          return (
            <div className="flex flex-col">
              {log?.user_feedback_stats?.content || '-'}
            </div>
          )
        }
        return (
          <Tooltip popupContent={tooltipContent()}>
            <div className='truncate'>
              {log?.user_feedback_stats?.content || '-'}
            </div>
          </Tooltip>
        )
      },
      ellipsis: true,
    },
    // 管理员反馈
    {
      title: t('appLog.table.header.adminRate'),
      key: 'adminRate',
      render: (_: any, log) => {
        return (!log.admin_feedback_stats.like && !log.admin_feedback_stats.dislike)
          ? defaultValue
          : <>
            {!!log.admin_feedback_stats.like && <HandThumbIconWithCount iconType='up' count={log.admin_feedback_stats.like} />}
            {!!log.admin_feedback_stats.dislike && <HandThumbIconWithCount iconType='down' count={log.admin_feedback_stats.dislike} />}
          </>
      },
      ellipsis: true,
    },
    // 更新时间
    {
      title: t('appLog.table.header.updatedTime'),
      key: 'updatedTime',
      render: (_: any, log) => {
        return formatTime(log.updated_at, t('appLog.dateTimeFormat') as string)
      },
      ellipsis: true,
    },
    // 创建时间
    {
      title: t('appLog.table.header.time'),
      key: 'time',
      render: (_: any, log) => {
        return formatTime(log.created_at, t('appLog.dateTimeFormat') as string)
      },
      ellipsis: true,
    },
  ]

  // 工作流应用不显示数据类型
  if (appDetail?.mode === 'advanced-chat') {
    const index = columns.findIndex(item => item.key === 'datasetType')
    columns.splice(index, 1)
  }

  const rowSelection = useMemo(() => {
    // 高级模式下不渲染复选框列
    if (appDetail?.mode === 'advanced-chat')
      return undefined

    // 其他模式下保持原有结构
    return {
      preserveSelectedRowKeys: true,
      selectedRowKeys,
      onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
        onSelect?.(selectedRowKeys, selectedRows)
        setSelectedRows(selectedRows)
      },
      // 复选框禁用
      getCheckboxProps: (record: ChatConversationGeneralDetail) => {
        const label = dataTypeList.find(item => item.value === record.dataset_type)?.label || '-'
        return { disabled: label === '-' }
      },
    }
  }, [appDetail?.mode, selectedRowKeys])

  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>('checkbox')

  return (
    <>
      <Table
        size='middle'
        loading={loading}
        columns={columns}
        expandable={expandableConfig}
        rowSelection={rowSelection}
        pagination={false}
        scroll={{ y: 'calc(100vh - 285px)' }}
        rowKey='id'
        dataSource={logs?.data || []}
        className='border rounded border-gray-G5'
        rowClassName='cursor-pointer'
        onRow={(record) => {
          return {
            onClick: () => {
              setShowDrawer(true)
              setCurrentConversation(record)
              // console.log(record, '===record=父===')
            },
          }
        }}
      ></Table>
      <Drawer
        isOpen={showDrawer}
        onClose={onCloseDrawer}
        footer={null}
        mask={false}
        panelClassname='!m-0 !mt-[114px] !rounded-none w-[30%] !max-w-none !p-0 !min-w-[520px]'
      >
        <DrawerContext.Provider value={{
          onClose: onCloseDrawer,
          appDetail,
        }}>
          <ChatConversationDetailComp appId={appDetail.id} conversationId={currentConversation?.id} isNodeClicked={isNodeClicked} isChildNode={currentConversation?.isChildNode || false} fatherId={currentConversation?.conversation_id || ''} chatData={currentConversation || {}} />
        </DrawerContext.Provider>
      </Drawer>
    </>
  )
}

export default ConversationList
