export type QueryParam = {
  start?: string
  end?: string
  annotation_status?: string
  keyword?: string
  node_id?: string;   // 新增的模型节点关键词
  sort_by?: string
  like?: likeParam
  dataset_type?: string
  source?: string
}
export enum LikeEnum {
  All = 'all',
  Like = 'like',
  Dislike = 'dislike',
  LikeAndNoDislike = 'likeAndNoDislike',
  NoLikeAndDislike = 'noLikeAndDislike',
  NoLikeAndNoDislike = 'noLikeAndNoDislike',
}
export type likeParam = {
  is_like?: number
  is_dislike?: number
}

// 数据来源分类
export enum ChatSourceEnum {
  all = 'all',
  web = 'web',
  api = 'api',
  embed = 'embed',
  market = 'market',
  local = 'local',
}

// 数据类型分类
export enum DatasetTypeEnum {
  'LLM-SFT' = 'LLM-SFT',
  'LLM-CoT' = 'LLM-CoT',
  'LLM-FC' = 'LLM-FC',
  'VLM-CoT' = 'VLM-CoT',
  'VLM-FC' = 'VLM-FC',
  'VLM-SFT' = 'VLM-SFT',
}
