'use client'
import type { FC  } from 'react'
import React, { useState, useCallback, useMemo , useRef } from 'react'
import Link from 'next/link'
import { useBoolean } from 'ahooks'
import useSWR from 'swr'
import { useTranslation } from 'react-i18next'
import { Pagination } from 'antd'
import List from './list'
import Filter from './filter'
import { useAppContext } from '@/context/app-context'
import CreateDataRefluxModal from '../create-data-reflux-modal'
import Confirm from '../../base/confirm'
import { fetchChatConversations } from '@/service/log'
import { APP_PAGE_LIMIT } from '@/config'
import type { App } from '@/types/app'
import type { ChatConversationGeneralDetail } from '@/models/log'
import { QueryParam } from './type'
import { TabType } from '@/app/(commonLayout)/app/(appDetailLayout)/[appId]/logs/page'

export type ILogsProps = {
  appDetail: App,
  setActiveTab?: (tab: TabType) => void
}

const Logs: FC<ILogsProps> = ({ appDetail, setActiveTab }) => {
  const { t } = useTranslation()
  const { isMicroApp } = useAppContext()
  const showDatasetReflux = useMemo(() => {
    return isMicroApp && (appDetail.mode === 'agent-chat' || appDetail.mode === 'advanced-chat')
  }, [appDetail.mode, isMicroApp])
  // 当前查询参数
  const [queryParams, setQueryParams] = useState<QueryParam>({
    annotation_status: 'all',
    sort_by: '-created_at',
  })
  // 当前页
  const [currPage, setCurrPage] = React.useState<number>(1)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [selectedRowNodeKeys, setSelectedRowNodeKeys] = useState<React.Key[]>([])
  const [selectedRows, setSelectedRows] = useState<ChatConversationGeneralDetail[]>([])
  const [dataRefluxDisabledTip, setDataRefluxDisabledTip] = useState<string>('')
  // 缓存结构：保存每一页的数据

  

  const query = {
    page: currPage,
    limit: APP_PAGE_LIMIT,
    keyword: queryParams.keyword || '',
    // model_keyword: queryParams.node_type || '',  // 转换为后端字段名（如需）
    ...queryParams.like,
    source: queryParams.source || '',
    dataset_type: queryParams.dataset_type || '',
    node_id: queryParams.node_id || '',
    ...{ sort_by: queryParams.sort_by },
  } as QueryParam
  if(queryParams.start && queryParams.end) {
    query.start = queryParams.start
    query.end = queryParams.end
  }
  
  let url;
  if(appDetail.mode === 'advanced-chat'){
    url = `/apps/${appDetail.id}/chat-conversation-nodes`///apps/${appDetail.id}/chat-conversation-nodes
  }else{
    url = `/apps/${appDetail.id}/chat-conversations`
  }
  let { data: chatConversations, mutate: mutateChatList, isLoading } = useSWR(() => ({
    url: url,
    params: query,
  }),fetchChatConversations)

 

  const total = chatConversations?.total
  const [showDataRefluxModal, setShowDataRefluxModal] = useState(false)
  const [showConfirmFailed, setShowConfirmFailed] = useState(false)
  const [showConfirmSuccess, setShowConfirmSuccess] = useState(false)

  const hideDateRefluxModal = useCallback((status?: string) => {
    setShowDataRefluxModal(false)
    if(status === 'success') {
      setSelectedRowKeys([])
      setSelectedRowNodeKeys([])
      setSelectedRows([])
      setShowConfirmSuccess(true)
    } else if (status === 'failed') {
      setShowConfirmFailed(true)
    }
  }, [])
  // 表格选中
  const handleSelect = useCallback((selectedRowKeys: React.Key[], selectedRows: ChatConversationGeneralDetail[]) => {
    
    if(appDetail.mode === 'advanced-chat'){
      const sonIds = selectedRows.map(row => row.id ?? '')
      const nodeIds = selectedRows.map(row => row.nodeId ?? '')
      setSelectedRowKeys(sonIds)
      setSelectedRowNodeKeys(nodeIds)
    }else{
      setSelectedRowKeys(selectedRowKeys)
      setSelectedRowNodeKeys([])
    }
    setSelectedRows(selectedRows)
  }, [])


  // 修改缓存类型定义，支持数组
  const pageCache = useRef<Record<number, ChatConversationGeneralDetail[]>>({})

  // 更新保存当前页数据到缓存的方法
  const savePageToCache = useCallback((page: number, data: ChatConversationGeneralDetail[]) => {
    if (appDetail.mode !== 'advanced-chat') return
    pageCache.current[page] = [...data]
  }, [appDetail.mode])

  // 更新从缓存中获取某页数据的方法
  const getCachedPage = useCallback((page: number): ChatConversationGeneralDetail[] | null => {
    if (appDetail.mode !== 'advanced-chat') return null
    return pageCache.current[page] || null
  }, [appDetail.mode])

  // 分页改变时触发的函数
  const handlePageChange = useCallback((page: number) => {
    // 保存当前页数据到缓存
    if (chatConversations && chatConversations.data && currPage in pageCache.current) {
      savePageToCache(currPage, chatConversations.data)
    }

    // 保存当前页选中状态
    const currentSelection = [...selectedRowKeys];


    // 设置新页码
    setCurrPage(page)

    // 如果缓存中存在该页数据，则不重新请求
    const cachedData = getCachedPage(page)
    if (cachedData) {
      mutateChatList({
        data: [...cachedData],
        has_more: !!chatConversations?.has_more,
        total: chatConversations?.total || cachedData.length,
        limit: chatConversations?.limit || APP_PAGE_LIMIT,
        page: chatConversations?.page || currPage,
      }, false)
    }

    // 恢复之前该页的选中状态
    const restoredSelection = currentSelection.filter(key => 
      cachedData !== null && cachedData.some(item => item.id === key)
    )
    setSelectedRowKeys(restoredSelection)
  }, [currPage, chatConversations, savePageToCache, getCachedPage, mutateChatList]);

  // 判断创建数据回流按钮禁用
  const dataRefluxDisabled = useMemo(() => {
    if(selectedRows.length === 0) {
      setDataRefluxDisabledTip(t('appLog.dataReflux.tip.requireSelectTip') || '')
      return true
    }
    // 不支持多数据类型创建回流数据集
    const baseType = selectedRows[0].dataset_type
    for(let i = 0; i < selectedRows.length; i++) {
      if(selectedRows[i].dataset_type !== baseType) {
        setDataRefluxDisabledTip(t('appLog.dataReflux.tip.diffTypeTip') || '')
        return true
      }
    }
    // 暂不支持文本-cot以外的类型
    if(appDetail.mode === 'advanced-chat'){
      if(baseType == 'LLM-CoT' || baseType == 'VLM-CoT' || baseType == 'LLM-FC' || baseType == 'VLM-FC' ) {
        console.log(baseType,'baseType')
        setDataRefluxDisabledTip(t('appLog.dataReflux.tip.noSupportTip') || '')
        return true
      }
    }else{

    }
    return false
  }, [selectedRows])

  return (
    <>
      {/* <div>{ JSON.stringify(selectedRows) }</div> */}

      {/* 描述 */}
      <div className='text-gray-G3 text-S3 leading-H3 mb-4'>{t('appLog.description')}</div>
      <div className='flex flex-col flex-1'>
        {/* 查询条件 */}
        <Filter 
          showDatasetReflux={showDatasetReflux}
          queryParams={queryParams}
          queryDays={chatConversations?.query_days}
          setQueryParams={(params) => {
            setQueryParams(params)
            setCurrPage(1)
          }} 
          onNewDataReflux={()=>setShowDataRefluxModal(true)}
          dataRefluxDisabled={dataRefluxDisabled} 
          dataRefluxDisabledTip={dataRefluxDisabledTip}
        />
        {/* 列表页 */}
        <List 
          loading={isLoading} 
          logs={chatConversations} 
          appDetail={appDetail} 
          onRefresh={mutateChatList} 
          selectedRowKeys={selectedRowKeys}
          onSelect={handleSelect}
        />
        {/* 分页组件 */}
        <Pagination
          className='mt-3'
          align='end'
          current={currPage}
          hideOnSinglePage
          onChange={handlePageChange}
          total={total}
          pageSize={APP_PAGE_LIMIT}
          showQuickJumper={false}
          showSizeChanger={false}
        />
      </div>
      {showDataRefluxModal && (
        <CreateDataRefluxModal
          useTool={!!appDetail?.model_config?.agent_mode?.tools?.length}
          appId={appDetail.id}
          appDetail={appDetail}
          selectedKeys={selectedRowKeys}
          selectedNodeKeys={selectedRowNodeKeys}
          selectedRows={selectedRows}
          onClose={hideDateRefluxModal}
        />
      )}
      {/* 创建失败 */}
      {
        showConfirmFailed && (
          <Confirm
            title={t('appLog.dataReflux.confirm.failed.title')}
            content={(
              <div className=''>
                {t('appLog.dataReflux.confirm.failed.desc')}
              </div>
            )}
            showCancel={false}
            isShow={showConfirmFailed}
            onConfirm={()=>setShowConfirmFailed(false)}
            onCancel={()=>setShowConfirmFailed(false)}
          />
        )
      }
      {/* 创建成功 */}
      {
        showConfirmSuccess && (
          <Confirm
            type='success'
            title={t('appLog.dataReflux.confirm.success.title')}
            content={(
              <div className=''>
                {t('appLog.dataReflux.confirm.success.desc1')}
                <span onClick={()=>setActiveTab?.(TabType.dataReflux)} className='font-normal text-primary-P1 cursor-pointer'>{t('appLog.dataReflux.confirm.success.link')}</span>
                {t('appLog.dataReflux.confirm.success.desc2')}
              </div>
            )}
            isShow={showConfirmSuccess}
            showCancel={false}
            onConfirm={()=>setShowConfirmSuccess(false)}
            onCancel={()=>setShowConfirmSuccess(false)}
          />
        )
      }
    </>
  )
}

export default Logs
