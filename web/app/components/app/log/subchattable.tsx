// SubChatTable.tsx
import React, { useState } from 'react';
import type { TableColumnsType } from 'antd';
import { Table } from 'antd';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import cn from '@/utils/classnames';
import { useTranslation } from 'react-i18next'
import useTimestamp from '@/hooks/use-timestamp'
import type { App } from '@/types/app'
import type { SubChatNode } from '@/models/log'
import { datasetTypeList } from '../data-reflux/type'

// 启用dayjs插件
dayjs.extend(utc);
dayjs.extend(timezone);




interface SubChatTableProps {
  conversationId: string;
  appId: string;
  appDetail: App; // 新增必传参数
  logData?: any; // 直接传递原始 logs 数据
  defaultSelectedRowKeys?: React.Key[]; // 👈 新增 prop
  onSubRowSelect?: (keys: React.Key[], records: SubChatNode[]) => void; // 新增回调
  onRowClick?: (record: SubChatNode) => void; // 👈 新增行点击回调
}

const SubChatTable: React.FC<SubChatTableProps> = ({ 
  conversationId,
  appId,
  logData,
  appDetail,
  defaultSelectedRowKeys,
  onSubRowSelect, // 解构props
  onRowClick, // ✅ 新增：正确解构onRowClick prop
}) => {
  const { t } = useTranslation();
  const { formatTime } = useTimestamp();

  const [selectedRowKeys, setSelectedRowKeys] = React.useState<React.Key[]>(() => {
    return defaultSelectedRowKeys || []
  })

  // 新增useEffect处理数据（添加到组件体内合适位置）
  React.useEffect(() => {
  // ✅ 正确访问 logData.list 并添加类型守卫
  if (logData?.[0]?.nodes && Array.isArray(logData[0].nodes)) {
    try {
      const result = processData(logData[0].nodes)
      // console.log('processData result:', result)
      setDataSource(result)
    } catch (error) {
      console.error('数据处理失败:', error)
    }
  }
}, [logData])



  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (newKeys: React.Key[], newRecords: SubChatNode[]) => {
      setSelectedRowKeys(newKeys);
      onSubRowSelect?.(newKeys, newRecords.map(r => ({
        ...r,
        dataType: r.dataset_type // 显式映射字段
      })));
    },
  };


  const processData = (logList: SubChatNode[]) => {
  
  // 类型守卫验证
  if (!Array.isArray(logList) || logList.some(item => 
    !item || typeof item.node_id !== 'string'
  )) {
    // console.warn('数据格式异常', { isArray: Array.isArray(logList), item: logList[0] })
    return []
  }
  // 分组逻辑验证
  const grouped = logList.reduce((acc, item, index) => {
    // ✅ 加入 node_type 到 key 中
    const key = `${item.node_id}-${item.node_type}-${item.process_data?.model_name}-${item.dataset_type}`;
    
    if (!acc[key]) acc[key] = []
    acc[key].push(item)
    return acc
  }, {} as Record<string, SubChatNode[]>)
  // console.log('grouped', grouped)


  

  // ✅ 修正属性访问方式
  return Object.entries(grouped).map(([nodeId, items]) => ({
    id: conversationId ,
    sonId: items[0].id ,
    nodeId: items[0].node_id ,
    name: `${items[0].title}`,
    key:`${conversationId}-${items[0].node_id}-${items.map(i => i.created_at)}`,
    nodeType: items[0].node_type ,
    dataset_type: items[0].dataset_type ,//logData[0].dataset_type,items[0].dataset_type
    model: items[0]?.process_data?.model_name ?? '',
    message_count: items.length || [],
    updated_at: Math.max(...items.map(i => i.finished_at)) ,
    created_at: Math.min(...items.map(i => i.created_at)) ,
  }))
}



// 使用示例
const [dataSource, setDataSource] = useState<ReturnType<typeof processData>>(() => {
  if (logData?.[0]?.nodes) {
    return processData(logData[0].nodes)
  }
  return []
})

  // 定义表格列
  const columns: TableColumnsType<SubChatNode> = [
    {
      title: t('appLog.table.subTable.header.nodeName'),
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (name, record) => `${name} - ${record?.nodeId}`
    },
    {
      title: t('appLog.table.subTable.header.nodeType'),
      dataIndex: 'nodeType',
      key: 'nodeType',
      ellipsis: true,
      render: (value) => {
        const labelMap = {
          llm: t('appLog.filter.nodeType.llm'),
          agent: t('appLog.filter.nodeType.agent'),
        };
        return labelMap[value as keyof typeof labelMap] || value;
      },
    },
    {
      title: t('appLog.table.subTable.header.dataType'),
      dataIndex: 'dataset_type',
      key: 'dataset_type',
      ellipsis: true,
      render: (value) => {
        const matchedType = datasetTypeList.find(item => item.value === value)
        return matchedType?.label || value // 匹配不到时返回原始值
      }
    },
    {
      title: t('appLog.table.subTable.header.model'),
      dataIndex: 'model',
      key: 'model',
      ellipsis: true,
    },
    {
      title: t('appLog.table.subTable.header.messageCount'),
      dataIndex: 'message_count',
      key: 'message_count',
      align: 'center',
      width: 100,
    },
    {
      title: t('appLog.table.subTable.header.updatedTime'),
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (time) => formatTime(time, t('appLog.dateTimeFormat')),
      width: 160,
    },
    {
      title: t('appLog.table.subTable.header.createdTime'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => formatTime(time, t('appLog.dateTimeFormat')),
      width: 160,
    }
  ];





  return (
    <div className=" bg-gray-50">
      <Table<SubChatNode>
        size="middle"
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        scroll={{ y: 350 }}
        rowKey="key"
        rowSelection={rowSelection}
        className={cn('mb-0')}
        onRow={(record) => ({
          onClick: () => {
            // console.log(record)
            onRowClick?.(record); // 👈 触发回调，传递当前行数据
          },
        })}
        // tableLayout="fixed"
      />
    </div>
  );
};

export default SubChatTable;
