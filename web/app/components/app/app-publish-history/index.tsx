'use client'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import useSWR from 'swr'
import type { App } from '@/types/app'
import { useMount } from 'ahooks'
import { Table, Pagination } from 'antd'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import type { TableColumnsType, TableProps } from 'antd'
import useTimestamp from '@/hooks/use-timestamp'

import { getAppPublishHistoryList } from '@/service/market'

import { ToastContext } from '@/app/components/base/toast'
import TextButton from '@/app/components/base/button/text-button'
import Confirm from '@/app/components/base/confirm'
import Tooltip from '@/app/components/base/tooltip'
import { statusEnum, statusMap } from '@/app/components/app/app-publish-config/type'
import { APP_PAGE_LIMIT } from '@/config'
import { clickExperience } from '@/app/components/app-square/utils'
import StatusTooltip from '@/app/components/app-publish/statusTooltip'
import PublishDetailModal from './publishDetailModal'

type AppPublishHistoryProps = {
  appId: string
  appDetail: App
}

const AppPublishHistory = ({
  appId,
  appDetail,
}: AppPublishHistoryProps) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { formatTime } = useTimestamp()
  // 当前页
  const [currPage, setCurrPage] = useState<number>(1)
  const [showDetailModal, setShowDetailModal] = useState<boolean>(false)
  const [detailData, setDetailData] = useState<any>({})
  const defaultValue = '-'

  const query = {
    page: currPage,
    limit: APP_PAGE_LIMIT,
  }

  // 获取应用市场应用列表
  const { data: dataList, mutate: mutateDataList, isLoading } = useSWR(() => ({
    url: `/market/${appId}/appsrelease/history`,
    params: query,
  }), getAppPublishHistoryList)

  const total = dataList?.total

  const getPublishStatus = (status: string) => {
    if(status && status !== 'offline') {
      return t(`app.publish.status.on`)
    }
    return t(`app.publish.status.off`)
  }

  // 表格列
  const columns: TableColumnsType<any> = [
    // 应用名称
    {
      title: t(`app.info.name`),
      key: 'name',
      render: (_: any, data) => {
        return data.name
      },
      width: 200,
      ellipsis: true,
    },
    // 发布时间
    {
      title: t(`app.info.publishAt`),
      key: 'createAt',
      render: (_: any, data) => {
        return formatTime(data.created_at, t('common.dateFormat.dateTime') as string)
      },
      width: 190,
      ellipsis: true,
    },
    // 发布渠道-应用广场
    {
      title: `${t('app.publish.method')}-${t('app.publish.methodOption.market')}`,
      key: 'market',
      render: (_: any, data) => {
        return (
          <div className='flex items-center'>
            {data.market_status ? t(`app.publish.status.on`) : t(`app.publish.status.off`)}
            <StatusTooltip appPublishInfo={{status: data.market_status, message: data.market_message}}/>
          </div>
        )
      },
      width: 170,
      ellipsis: true,
    },
    // 发布渠道-前端
    {
      title: `${t('app.publish.method')}-${t('app.publish.methodOption.web')}`,
      key: 'web',
      render: (_: any, data) => {
        return getPublishStatus(data.web_status)
      },
      width: 170,
      ellipsis: true,
    },
    // 发布渠道-后端
    {
      title: `${t('app.publish.method')}-${t('app.publish.methodOption.api')}`,
      key: 'api',
      render: (_: any, data) => {
        return getPublishStatus(data.api_status)
      },
      width: 170,
      ellipsis: true,
    },
    // 发布渠道-嵌入网站
    {
      title: `${t('app.publish.method')}-${t('app.publish.methodOption.embed')}`,
      key: 'embed',
      render: (_: any, data) => {
        return getPublishStatus(data.embed_status)
      },
      width: 170,
      ellipsis: true,
    },
    // 操作
    {
      title: t(`app.info.action`),
      render: (_: any, data) => (
        <div className='flex gap-6'>
          <TextButton size='middle' onClick={() => handleDetail(data)}>
            {t(`app.action.detail`)}
          </TextButton>
        </div>
      ),
      key: 'operation',
      align: 'left',
      width: 100,
    },
  ]
  // 查看详情
  const handleDetail = (data: any) => {
    setDetailData(data)
    setShowDetailModal(true)
  }

  return (
    <div className='p-8'>
      <Table
        size='middle'
        loading={isLoading}
        columns={columns}
        pagination={false}
        rowKey='id'
        dataSource={dataList?.data || []}
      />
      {/* 分页组件 */}
      <Pagination
        className='mt-3'
        align='end'
        current={currPage}
        hideOnSinglePage
        onChange={setCurrPage}
        total={total}
        pageSize={APP_PAGE_LIMIT}
        showQuickJumper={false}
        showSizeChanger={false}
      />
      {showDetailModal && 
        <PublishDetailModal data={detailData} onClose={() => setShowDetailModal(false)}/>
      }
    </div>
  )
}

export default AppPublishHistory