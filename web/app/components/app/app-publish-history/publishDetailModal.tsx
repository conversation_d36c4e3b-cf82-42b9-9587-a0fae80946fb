import { useTranslation } from 'react-i18next'
import { Form, Input } from 'antd'
import Modal from '../../base/modal'
import { useEffect } from 'react'
import { MAX_APP_NAME_LENGTH, MAX_APP_DESC_LENGTH } from '@/config'

type PublishDetailModalProps = {
  data: any
  onClose: () => void
}
const PublishDetailModal = ({ data, onClose }: PublishDetailModalProps) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()

  useEffect(() => {
    form.setFieldsValue({
      name: data.name,
      description: data.description,
    })
  }, [])
  return (
    <Modal
      isShow
      title={t('app.modalTitle.publishDetail')}
      closable
      onClose={onClose}
    >
      <Form layout='vertical' form={form}>
        {/* 应用名称 */}
        <Form.Item
          name={'name'}
          label={t('app.info.appName')}
          validateTrigger='onBlur'
        >
          <Input readOnly placeholder={t('app.placeholder.appName') || ''} maxLength={MAX_APP_NAME_LENGTH}/>
        </Form.Item>
        {/* 应用描述 */}
        <Form.Item 
          name={'description'} 
          label={t('app.info.appDescription')}
        >
          <Input.TextArea
            readOnly
            placeholder={t('app.placeholder.appDescription') || ''}
            maxLength={MAX_APP_DESC_LENGTH}
          ></Input.TextArea>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default PublishDetailModal