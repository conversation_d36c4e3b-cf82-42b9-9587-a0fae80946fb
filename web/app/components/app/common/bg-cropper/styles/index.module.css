.app-bg-upload {
  @apply w-full;
}
.app-bg-cropper {
  @apply flex justify-between;
}

/* 浮层样式 */
.floating-app-avatar, .floating-chat-item-avatar {
  border: 0.875px solid rgba(255, 255, 255, 0.60) !important;
  background: rgba(255, 255, 255, 0.80) !important;
  backdrop-filter: blur(2.625px) !important;
}

.floating-app-title {
  @apply text-S2 leading-H2 mt-1;
}
.floating-chat-item {
  @apply mt-4 gap-3 w-full flex;
}
.floating-chat-item-avatar {
  @apply mt-1;
}
.floating-chat-item-avatar-second {
  @apply mt-1 ;
  background: linear-gradient(89deg, rgba(213, 214, 255, 0.80) 1.63%, rgba(206, 226, 255, 0.80) 100%);
}
.floating-chat-item-content {
  flex-grow: 1;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.90);
  backdrop-filter: blur(3px);
}
.floating-chat-item-content-second {
    flex-grow: 1;
    border-radius: 6px;
    background-color: rgba(213, 214, 255, 0.80);
    backdrop-filter: blur(2px);  
}
