'use client'

import { ConfigProvider, Divider, Radio, Upload } from 'antd'
import Compressor from 'compressorjs'
import { useRef, useState } from 'react'
import type { RcFile } from 'antd/es/upload'
import { useTranslation } from 'react-i18next'
import { useAsyncEffect } from 'ahooks'
import s from './styles/index.module.css'
import './styles/index.css'
import type { ImageFile } from '@/types/public/file'
import { getCropperFontCss } from '@/app/components/app/common/bg-cropper/utils'

import cn from '@/utils/classnames'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import type { CropperConfig } from '@/app/components/base/cropper/type'
import Cropper from '@/app/components/base/cropper'
import Avatar from '@/app/components/base/avatar'
import Toast from '@/app/components/base/toast'
import { useLocalFileUploader } from '@/app/components/base/image-uploader/hooks'
import type { AppChatBgConfig } from '@/app/components/base/features/types'

const { Dragger } = Upload

type AppBgCropperProps = {
  // 裁剪器高度
  height: number
  // 图像配置
  config?: AppChatBgConfig
  // 关闭事件
  onClose?: () => void
  // 提交动作
  onSubmit?: (value: AppChatBgConfig) => void
}

const AppBgCropper = ({
  onClose,
  onSubmit,
  height,
  config,
}: AppBgCropperProps) => {
  const { t } = useTranslation()
  const { handleLocalFileUpload } = useLocalFileUploader({
    limit: 10,
    disabled: false,
    onUpload: (imageFile: ImageFile) => {},
    url: '/images/upload',
  })

  // 图片预处理loading
  const [loading, setLoading] = useState(false)
  // 图像缩放比例
  const [zoom, setZoom] = useState(1)
  // 背景图url
  const [url, setUrl] = useState('')
  // 当前背景图文件
  const [file, setFile] = useState<File>()
  // 字体颜色
  const [fontColor, setFontColor] = useState<'white' | 'black'>('black')

  // 宽屏cropper
  const wideCropperRef = useRef<{
    getCropData: () => Promise<CropperConfig | undefined>
  }>()
  // 窄屏cropper
  const narrowCropperRef = useRef<{
    getCropData: () => Promise<CropperConfig | undefined>
  }>()

  // 字体颜色选项
  const fontColorOptions = [{
    label: t('app.cropper.fontColorBlack'),
    value: 'black',
  }, {
    label: t('app.cropper.fontColorWhite'),
    value: 'white',
  }]

  // 检测图像边框范围
  const detectBorders = (data: Uint8ClampedArray, width: number, height: number) => {
    const isBorderPixel = (r: number, g: number, b: number, a: number) => {
      const isWhite = r === 255 && g === 255 && b === 255
      const isBlack = r < 11 && g < 11 && b < 11
      return isWhite || isBlack
    }

    let top = 0
    let bottom = height
    let left = 0
    let right = width

    // 检测顶部边框
    for (let y = 0; y < height; y++) {
      let isBorder = true
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4
        if (!isBorderPixel(data[idx], data[idx + 1], data[idx + 2], data[idx + 3])) {
          isBorder = false
          break
        }
      }
      if (!isBorder) {
        top = y
        break
      }
    }
    // 检测底部边框
    for (let y = height - 1; y >= 0; y--) {
      let isBorder = true
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4
        if (!isBorderPixel(data[idx], data[idx + 1], data[idx + 2], data[idx + 3])) {
          isBorder = false
          break
        }
      }
      if (!isBorder) {
        bottom = y
        break
      }
    }
    // 检测左侧边框
    for (let x = 0; x < width; x++) {
      let isBorder = true
      for (let y = 0; y < height; y++) {
        const idx = (y * width + x) * 4
        if (!isBorderPixel(data[idx], data[idx + 1], data[idx + 2], data[idx + 3])) {
          isBorder = false
          break
        }
      }
      if (!isBorder) {
        left = x
        break
      }
    }
    // 检测右侧边框
    for (let x = width - 1; x >= 0; x--) {
      let isBorder = true
      for (let y = 0; y < height; y++) {
        const idx = (y * width + x) * 4
        if (!isBorderPixel(data[idx], data[idx + 1], data[idx + 2], data[idx + 3])) {
          isBorder = false
          break
        }
      }
      if (!isBorder) {
        right = x
        break
      }
    }
    return { top, bottom, left, right }
  }
  // 图像资源预处理边框
  const pretreatmentImageBorder = (img: HTMLImageElement) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (ctx) {
      // 设置画布大小为图片大小
      canvas.width = img.width
      canvas.height = img.height
      // 绘制图片到画布
      ctx.drawImage(img, 0, 0)
      // 获取图片像素数据
      const imageData = ctx.getImageData(0, 0, img.width, img.height)
      const data = imageData.data
      // 检测边框范围
      const { top, bottom, left, right } = detectBorders(data, img.width, img.height)
      // 裁剪图片
      const cropWidth = right - left
      const cropHeight = bottom - top
      const croppedCanvas = document.createElement('canvas')
      const croppedCtx = croppedCanvas.getContext('2d')
      croppedCanvas.width = cropWidth
      croppedCanvas.height = cropHeight
      if (croppedCtx) {
        croppedCtx.drawImage(img, left, top, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight)
        setUrl(croppedCanvas.toDataURL('image/jpg'))
      }
    }
  }
  // 图像资源预处理
  const pretreatmentFile = async (file: RcFile) => {
    setLoading(true)
    const acceptFileType = ['image/png', 'image/jpg', 'image/jpeg']
    const reader = new FileReader()
    const img = new Image()
    const compressReader = new FileReader()
    const compressImg = new Image()

    await new Promise((resolve, reject) => {
      // 文件读取器
      reader.onload = (e) => {
        img.src = e.target?.result as string
      }
      compressReader.onload = (e) => {
        compressImg.src = e.target?.result as string
      }
      // 图像读取器
      img.onload = () => {
        if (img.height < 640) {
          Toast.notify({
            type: 'error',
            message: t('app.cropper.heightErrorMessage'),
          })
          reject(new Error(t('app.cropper.heightErrorMessage')!))
        }
        else {
          if (img.height > 1000 && img.width > 1000) {
            const size = img.height > img.width ? (img.width / 1000) : (img.height / 1000)
            const compress = new Compressor(file, {
              quality: 1, // 设置压缩质量，范围从 0 到 1
              maxWidth: img.width / size, // 设置最大宽度
              maxHeight: img.height / size, // 设置最大高度
              success(result) {
                const renamedFile = new File([result], file.name, {
                  type: result.type,
                  lastModified: Date.now(),
                })
                setFile(renamedFile)
                compressReader.readAsDataURL(result)
              },
              error(err) {
                console.error(err.message)
              },
            })
          }
          else {
            setZoom(height / img.height)
            pretreatmentImageBorder(img)
            setFile(file)
            resolve('success')
          }
        }
      }
      // 压缩后的图像读取器
      compressImg.onload = () => {
        setZoom(height / compressImg.height)
        pretreatmentImageBorder(compressImg)
        resolve('success')
      }

      // 文件类型处理
      if (!acceptFileType.includes(file.type)) {
        Toast.notify({
          type: 'error',
          message: t('app.cropper.typeErrorMessage'),
        })
        reject(new Error(t('app.cropper.typeErrorMessage')!))
        return
      }
      // 图片大小不得超过10MB
      if (file.size > 10 * 1024 * 1024) {
        Toast.notify({
          type: 'error',
          message: t('app.cropper.sizeErrorMessage'),
        })
        reject(new Error(t('app.cropper.typeErrorMessage')!))
        return
      }
      reader.readAsDataURL(file)
    })
    setLoading(false)
  }
  // 提交裁剪配置
  const submitCropConfig = async () => {
    const [wideConfig, narrowConfig] = await Promise.all([wideCropperRef.current?.getCropData(), narrowCropperRef.current?.getCropData()])
    const currentConfig = {
      wide: {
        ...wideConfig!,
      },
      narrow: {
        ...narrowConfig!,
      },
      fontColor,
      url,
    }
    if (file) {
      await new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => {
          // 获取 Base64 地址
          const base64Url = reader.result as string
          // 这里可以根据需求使用 base64Url
          currentConfig.url = base64Url
          handleLocalFileUpload(file, (imageFile) => {
            // @ts-expect-error 类型正确
            currentConfig.id = imageFile.fileId
            setLoading(false)
            resolve(true)
          }, (err) => {
            setLoading(false)
            reject(err)
          })
        }
        reader.onerror = () => {
          setLoading(false)
          reject(new Error('Failed to read file as data URL'))
        }
        reader.readAsDataURL(file)
      })
    }
    if (currentConfig.url) {
      onSubmit && onSubmit({
        ...currentConfig,
        enabled: true,
      })
    }
  }

  useAsyncEffect(async () => {
    if (config && config.url) {
      setFontColor(config.fontColor!)
      const img = new Image()
      img.onload = () => {
        pretreatmentImageBorder(img)
      }
      img.src = config.url
    }
  }, [config])

  // 裁剪器浮层
  const CropperFloating = () => (
    <>
      <div className={cn('text-S1 leading-H1 font-semibold', getCropperFontCss({ fontColor, enabled: true }))}>{t('app.action.previewAndDebugger')}</div>
      <Divider className='bg-gray-G6 !mt-2 !mb-6' type='horizontal'></Divider>
      <div className='flex flex-col items-center'>
        {/* 应用信息 */}
        <Avatar size={36} className={s['floating-app-avatar']}></Avatar>
        <div className={cn(s['floating-app-title'], getCropperFontCss({ fontColor, enabled: true }))}>{t('app.info.appName')}</div>
        {/* 聊天列表 */}
        <div className={s['floating-chat-item']}>
          <Avatar size={28} className={s['floating-chat-item-avatar']}></Avatar>
          <div className={cn(s['floating-chat-item-content'], 'h-[62px]')}></div>
        </div>
        <div className={s['floating-chat-item']}>
          <Avatar size={28} className={s['floating-chat-item-avatar-second']}></Avatar>
          <div className={cn(s['floating-chat-item-content-second'], 'h-[36px]')}></div>
        </div>
        <div className={s['floating-chat-item']}>
          <Avatar size={28} className={s['floating-chat-item-avatar']}></Avatar>
          <div className={cn(s['floating-chat-item-content'], '!grow-0 !w-[50%] h-[36px]')}></div>
        </div>
      </div>
    </>
  )

  return (
    <Modal
      isShow
      title={t('app.cropper.title')}
      onClose={onClose}
      closable
      className="min-w-[800px]"
      footer={
        url
        && <div className='flex flex-col w-full'>
          <div className='mt-[-8px] mb-6 flex items-center gap-4'>
            <div className='text-S1 leading-H1 text-gray-G1'>{t('app.cropper.fontColor')}：</div>
            <ConfigProvider
              theme={{
                components: {
                  Radio: {
                    fontSize: 12,
                    lineHeight: 1.66667,
                    wrapperMarginInlineEnd: 32,
                  },
                },
              }}
            >
              <Radio.Group value={fontColor} options={fontColorOptions} onChange={val => setFontColor(val.target.value)}></Radio.Group>
            </ConfigProvider>
          </div>
          <div className="w-full flex items-center gap-10 justify-between">
            <div className="flex items-center w-0 grow gap-2">
              <Upload showUploadList={false} beforeUpload={pretreatmentFile}>
                <Button variant={'gray'}>{t('common.operation.reUpload')}</Button>
              </Upload>
              <div title={t('app.cropper.reUploadTip')!} className='truncate text-S1 leading-H1 text-gray-G3'>{t('app.cropper.reUploadTip')}</div>
            </div>
            <div className="flex items-center gap-4">
              <Button onClick={onClose} variant={'secondary-accent'}>{t('common.operation.cancel')}</Button>
              <Button onClick={submitCropConfig} variant={'primary'}>{t('common.operation.confirm')}</Button>
            </div>
          </div>
        </div>
      }
    >
      {/* 背景图上传 */}
      {
        !url && <div className='flex flex-col items-center'>
          {/* 拖拽上传 */}
          <Dragger
            accept={'.png,.jpg,.jpeg'}
            className={cn(s['app-bg-upload'], 'app-bg-upload')}
            showUploadList={false}
            beforeUpload={pretreatmentFile}
          >
            <div className='flex flex-col items-center'>
              <img className='mt-[58px]' src='/assets/icons/app/bgFileUpload.svg' alt=''></img>
              <div className='text-S3 leading-H3 text-gray-G1 mt-3 font-semibold'>{t('app.cropper.uploadTip')}</div>
              <div className='text-S1 leading-H1 text-gray-G2 mt-1'>{t('app.cropper.typeTip')}</div>
              <Button loading={loading} className='mt-9 mb-[126px]' variant={'primary'}>{t('app.cropper.upload')}</Button>
            </div>
          </Dragger>
        </div>
      }
      {/* 背景图裁剪 */}
      {
        url && <>
          <div className={s['app-bg-cropper']}>
            {/* 宽屏展示 */}
            <div className='flex flex-col items-center gap-2'>
              {/* 标题 */}
              <div className='text-S1 leading-H1 text-gray-G1'>{t('app.cropper.widePreview')}</div>
              {/* 背景图裁剪器 */}
              <Cropper
                ref={wideCropperRef}
                width={460}
                height={height}
                config={{
                  zoom,
                  ...config?.wide,
                  url,
                }}
                className='rounded-md overflow-hidden'
                floatingClassName='py-5 px-[62px]'
              >
                <CropperFloating></CropperFloating>
              </Cropper>
            </div>
            {/* 窄屏展示 */}
            <div className='flex flex-col items-center gap-2'>
              {/* 标题 */}
              <div className='text-S1 leading-H1 text-gray-G1'>{t('app.cropper.narrowPreview')}</div>
              {/* 背景图裁剪期 */}
              <Cropper
                width={240}
                height={height}
                ref={narrowCropperRef}
                config={{
                  zoom,
                  ...config?.narrow,
                  url,
                }}
                className='rounded-md overflow-hidden'
                floatingClassName='py-5 px-[31px]'
              >
                <CropperFloating></CropperFloating>
              </Cropper>
            </div>
          </div>
        </>
      }

    </Modal>
  )
}

export default AppBgCropper
