import React from 'react'
import { useTranslation } from 'react-i18next'
import type { AppMode } from '@/types/app'
import { AgentTagIcon, ChatTagIcon, CompletionTagIcon, WorkflowTagIcon } from '@/app/components/base/icons/src/vender/line/tag'
import Badge from '@/app/components/base/badge'

export type AppTagProps = {
  appMode: AppMode
  className?: string
}

const AppTag = ({ appMode }: AppTagProps) => {
  const { t } = useTranslation()

  return (
    <Badge className='rounded-[2px] bg-gray-G10 text-gray-G2'>
      {['advanced-chat', 'chat'].includes(appMode) && (
        <>
          <ChatTagIcon className='w-3 h-3 mr-1 text-primary-P1'></ChatTagIcon>
          <div className='truncate'>{t('app.newApp.advanced').toUpperCase()}</div>
        </>
      )}
      {appMode === 'agent-chat' && (
        <>
          <AgentTagIcon className='w-3 h-3 mr-1 text-primary-P1'></AgentTagIcon>
          <div className='truncate'>{t('app.newApp.basic').toUpperCase()}</div>
        </>
      )}
      {appMode === 'workflow' && (
        <>
          <WorkflowTagIcon className='w-3 h-3 mr-1 text-primary-P1'></WorkflowTagIcon>
          <div className='truncate'>{t('app.types.workflow').toUpperCase()}</div>
        </>
      )}
      {appMode === 'completion' && (
        <>
          <CompletionTagIcon className='w-3 h-3 mr-1 text-primary-P1'></CompletionTagIcon>
          <div className='truncate'>{t('app.types.completion').toUpperCase()}</div>
        </>
      )}
    </Badge>
  )
}

export default AppTag
