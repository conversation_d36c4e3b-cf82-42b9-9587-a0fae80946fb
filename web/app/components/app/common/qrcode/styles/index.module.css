.qrcode-popover,.qrcode-donw-popover {
  @apply py-6 px-10 flex flex-col items-center;
  border-radius: 4px;
  border: 1px solid #FFF;
  background: linear-gradient(254deg, #FFF -7.64%, #9FD3FF 131.21%);
  margin: 0 !important;
  margin-top: 4px !important;
  margin-left: -200px !important;
  width: fit-content;
  position: fixed;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.qrcode-donw-popover {
  z-index: 997 !important;
  right: 100% !important;
}
.qrcode-donw-popover .title {
  line-height: 32px !important;
  max-height: 64px !important
}
.qrcode-donw-popover .tip {
  line-height: 28px !important;
  max-height: 56px !important
}
.qrcode-donw-popover .desc {
  line-height: 24px !important;
  max-height: 48px !important;
}
.qrcode-donw-popover .app-logo {
  @apply !translate-x-0 !-translate-y-0 !top-[-32px];
  left: calc(50% - 32px) !important;
}
.fixed-content {
  @apply flex flex-col w-[340px] items-center;
  margin: 0 !important;
  padding: 40px;
  border-radius: 8px;
  background: #FFF;
  border: 0.5px solid #eaecf0;
  margin-top: 4px !important;
  margin-left: -200px !important;
  position: fixed !important;
  justify-content: center;
  z-index: 999;
  box-shadow: 0 12px 16px -4px rgba(16, 24, 40, 0.08),
  0 4px 6px -2px rgba(16, 24, 40, 0.03);
}

.logo {
  @apply mb-[52px];
}
.app-logo {
  @apply absolute top-0 left-1/2 transform -translate-x-[50%] -translate-y-[50%];
}
.title {
  @apply text-S4 leading-H4 text-gray-G1 max-h-[52px]  mb-1 font-semibold text-center;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.desc {
  @apply text-S1 leading-H1 text-gray-G2 max-h-[40px] text-center;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.content {
  @apply flex flex-col w-[340px] items-center;
  padding: 40px;
  border-radius: 8px;
  background: #FFF;
  position: relative;
}
.tip {
  @apply text-S3 leading-H3 text-primary-P1 font-semibold max-h-[48px] text-center;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}