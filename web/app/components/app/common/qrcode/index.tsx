'use client'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import html2canvas from 'html2canvas'
import QRCode from 'qrcode.react'
import { Tooltip } from 'antd'
import s from './styles/index.module.css'
import type { App } from '@/types/app'
// 公共组件
import { APP_NAME } from '@/config'
import Logo from '@/app/components/base/logo'
import Avatar from '@/app/components/base/avatar'
import TextButton from '@/app/components/base/button/text-button'
import { Qrcode } from '@/app/components/base/icons/src/vender/line/general'
import Button from '@/app/components/base/button'
import cn from '@/utils/classnames'

type Props = {
  // 二维码内容
  content: string
  // 应用信息
  app: App
  // 外层类名
  className?: string
}

const prefixEmbedded = 'appOverview.overview.appInfo.qrcode.title'

const ShareQRCode = ({ content, className, app }: Props) => {
  const { t } = useTranslation()
  // 是否展示
  const [isShow, setIsShow] = useState<boolean>(false)
  // 二维码节点
  const qrCodeRef = useRef<HTMLDivElement>(null)
  const donwloadRef = useRef<HTMLDivElement>(null)

  // 展示二维码
  const toggleQRCode = (event: React.MouseEvent) => {
    event.stopPropagation()
    setIsShow(prev => !prev)
  }
  // 下载二维码
  const downloadQR = async () => {
    /* const targetNode = qrCodeRef.current?.cloneNode(true) as HTMLDivElement | undefined
    const downloadNode = targetNode!.getElementsByTagName('button')[0]
    targetNode?.removeChild(downloadNode as HTMLElement)
    document.body.appendChild(targetNode!) */
    const canvas = await html2canvas(donwloadRef.current!)
    const link = document.createElement('a')
    link.download = 'qrcode.png'
    link.href = canvas.toDataURL()
    link.click()
    /* document.body.removeChild(targetNode!) */
  }
  // 点击面板区域
  const handlePanelClick = (event: React.MouseEvent) => {
    event.stopPropagation()
  }
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (qrCodeRef.current && !qrCodeRef.current.contains(event.target as Node))
        setIsShow(false)
    }

    if (isShow)
      document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [isShow])

  return (
    <Tooltip title={t(`${prefixEmbedded}`) || ''}>
      <div className={'w-4 h-5'}>
        <TextButton variant='text' onClick={toggleQRCode}>
          <Qrcode className='w-4 h-4'></Qrcode>
        </TextButton>
      </div>
      {isShow && (
        <>
          {/* 内容部分 */}
          <div className={s['fixed-content']}>
            {/* 二维码部分 */}
            <QRCode size={200} className='mb-5' value={content}/>
            {/* tip部分 */}
            <div className={s.tip}>{t('app.notify.shareTip', { name: APP_NAME })}</div>
            {/* 下载按钮部分 */}
            <Button id='download-qrcode' onClick={downloadQR} className='!h-[40px] !w-[208px] mt-6' variant={'primary'}>
              { t('app.action.downQRcode') }
            </Button>
          </div>

          <div
            ref={donwloadRef}
            className={cn(s['qrcode-donw-popover'])}
            onClick={handlePanelClick}
          >
            {/* logo部分 */}
            <Logo className={s.logo}></Logo>
            {/* 内容部分 */}
            <div className={s.content}>
              {/* 应用logo */}
              <Avatar className={s['app-logo']} avatar={app.icon_url!} size={64}></Avatar>
              {/* 应用名称 */}
              <div className={s.title} title={app.name}>{app.name}</div>
              {/* 应用描述 */}
              <div className={s.desc} title={app.description}>{app.description}</div>
              {/* 二维码部分 */}
              <QRCode size={200} className='my-5' value={content}/>
              {/* tip部分 */}
              <div className={s.tip}>{t('app.notify.shareTip', { name: APP_NAME })}</div>
            </div>
          </div>
        </>
      )
      }

    </Tooltip>
  )
}

export default ShareQRCode
