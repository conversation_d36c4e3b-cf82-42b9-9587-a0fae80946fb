import React from 'react'
import Image from 'next/image'
import type { AppMode } from '@/types/app'
import Avatar from '@/app/components/base/avatar'
import { MediaType } from '@/types/videos'

export type AppAvatarProps = {
  appMode?: AppMode | 'datasets' | MediaType
  url?: string
  size?: number
  className?: string
}

const AppAvatar = ({ appMode, url, className, size = 48 }: AppAvatarProps) => {
  if (url) {
    return <Avatar
      size={size}
      avatar={url}
      className={className}
    ></Avatar>
  }
  if (appMode === 'advanced-chat' || appMode === 'chat') {
    return (
      <Image className={className} src='/assets/icons/card/chat-card.svg' alt="聊天助手" width={size} height={size}></Image>
    )
  }
  else if (appMode === 'agent-chat') {
    return (
      <Image className={className} src='/assets/icons/card/agent-card.svg' alt="Agent" width={size} height={size}></Image>
    )
  }
  else if (appMode === 'workflow') {
    return (
      <Image className={className} src='/assets/icons/card/workflow-card.svg' alt="工作流" width={size} height={size}></Image>
    )
  }
  else if (appMode === 'completion') {
    return (
      <Image className={className} src='/assets/icons/card/completion-card.svg' alt="completion" width={size} height={size}></Image>
    )
  }
  else if (appMode === 'datasets') {
    return (
      <Image className={className} src='/assets/avatar/dataset.svg' alt="datasets" width={size} height={size}></Image>
    )
  }
  else if (appMode === MediaType.VideoStream) {
    return (
      <Image className={className} src='/assets/avatar/video-buket.svg' alt="视频流" width={size} height={size}></Image>
    )
  }
  else if (appMode === MediaType.Video) {
    return (
      <Image className={className} src='/assets/avatar/video.svg' alt="视频" width={size} height={size}></Image>
    )
  }
  else if (appMode === MediaType.Img) {
    return (
      <Image className={className} src='/assets/avatar/img.svg' alt="图像" width={size} height={size}></Image>
    )
  }

  return (
    <div className={`w-[${size}px] h-[${size}px]`}></div>
  )
}

AppAvatar.displayName = 'AppAvatar'

export default React.memo(AppAvatar)
