'use client'
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react'
import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { useContext } from 'use-context-selector'
import { Form, Input } from 'antd'
import { CarryOutOutlined } from '@ant-design/icons'
import { getRandomAppHead } from '../utils'
import s from './styles/index.module.css'
import { MAX_APP_DESC_LENGTH, MAX_APP_NAME_LENGTH } from '@/config'
import { useProviderContext } from '@/context/provider-context'
import type { App, AppIconType, AppMode } from '@/types/app'
import { createApp, importApp } from '@/service/apps'
import { useAppContext } from '@/context/app-context'
import { getRedirection } from '@/utils/app-redirection'
import { secureRandom } from '@/utils'
import Tooltip from '@/app/components/base/tooltip'
import { AgentTagIcon, WorkflowTagIcon } from '@/app/components/base/icons/src/vender/line/tag'
import { ToastContext } from '@/app/components/base/toast'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import Avatar from '@/app/components/base/avatar'
import { useFormDisabled } from '@/hooks/use-form'
import Radio from '@/app/components/base/radio/index'

export type onEditType = (info: {
  name: string
  icon_type: AppIconType
  icon: string
  icon_background?: string
  description: string
  use_icon_as_answer_icon?: boolean
}) => Promise<void>
export type CreateAppDialogProps = {
  app?: App
  canChangeMode?: boolean
  defaultConfig?: {
    mode?: AppMode
    name?: string
    description?: string
    icon?: string
    specify_model_name?: string
    specify_model_service_id?: string
    specify_model_provider?: string
  }
  onSuccess?: () => void
  onEdit?: (info: {
    name: string
    icon_type: AppIconType
    icon: string
    icon_background?: string
    description: string
    use_icon_as_answer_icon?: boolean
  }) => Promise<void>
  onClose: () => void
}

const CreateAppModal = ({
  defaultConfig,
  app,
  canChangeMode = true,
  onSuccess,
  onClose,
  onEdit,
}: CreateAppDialogProps) => {
  const { t } = useTranslation()
  const { setLongDocState } = useAppContext()
  const { push } = useRouter()
  const { notify } = useContext(ToastContext)
  const { enableCreateLongtextApp } = useProviderContext()
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)

  // 随机头像
  const randomHead = useRef(getRandomAppHead())
  // 应用模式
  const [appMode, setAppMode] = useState<AppMode>('agent-chat')
  // 应用图标
  const [appIcon, setAppIcon] = useState({
    type: 'image',
    icon: '',
    url: '',
    fileId: secureRandom(),
  })

  // 是否为编辑模式
  const isEdit = useMemo(() => !!app, [app])
  // 弹窗title
  const modalTitle = useMemo(() => {
    return !isEdit
      ? t('app.action.startByType', { type: t('app.types.emptyApp') })
      : t('app.modalTitle.editAppTitle')
  }, [isEdit, t])

  // 创建应用
  const onCreate: MouseEventHandler = useCallback(async () => {
    try {
      const { name, description } = form.getFieldsValue()
      let app: App
      if (appMode === 'LongTextWorkflow') {
        app = await importApp({
          data: 'is_longtext',
          name,
          is_longtext: true,
          description,
          icon_type: 'image',
          icon: appIcon.icon,
          icon_background: undefined,
        })
        if (app.id) {
          setLongDocState(true)
          notify({ type: 'success', message: t('app.notify.appCreated') })
          onSuccess && onSuccess()
          getRedirection(app, push)
        }
      }
      else {
        app = await createApp({
          ...defaultConfig,
          name,
          description,
          icon_type: 'image',
          icon: appIcon.icon,
          icon_background: undefined,
          mode: appMode,
        })
        setLongDocState(false)
      }
      if (app.id) {
        notify({ type: 'success', message: t('app.notify.appCreated') })
        onSuccess && onSuccess()
        getRedirection(app, push)
      }
    }
    catch (e) {
      notify({ type: 'error', message: t('app.notify.appCreateFailed') })
    }
  }, [form, appMode, appIcon.icon, setLongDocState, notify, t, onSuccess, push, defaultConfig])
  // 编辑应用
  const onEditApp = async () => {
    const { name, description } = form.getFieldsValue()
    onEdit && await onEdit({
      name,
      icon_type: 'image',
      icon: appIcon.icon,
      icon_background: undefined,
      description,
      use_icon_as_answer_icon: false,
    }).then(() => {
      onClose()
    })
  }
  // 变更应用头像
  const changeAppHead = (value: string) => {
    setAppIcon({
      type: 'image',
      icon: value,
      url: value,
      fileId: secureRandom(),
    })
  }

  useEffect(() => {
    if (app) {
      setAppIcon({
        type: 'image',
        icon: app?.icon || app?.icon_url || '',
        url: app?.icon_url || app?.icon,
        fileId: secureRandom(),
      })
    }
    else if (defaultConfig) {
      const { mode, icon } = defaultConfig
      mode && setAppMode(mode)
      icon && setAppIcon({
        type: 'image',
        icon,
        fileId: secureRandom(),
        url: icon,
      })
    }
    else {
      setAppIcon({
        type: 'image',
        icon: randomHead.current,
        url: randomHead.current,
        fileId: secureRandom(),
      })
    }
  }, [app, defaultConfig])

  return (
    <Modal
      className='!max-w-[720px] !w-[720px]'
      isShow
      title={modalTitle}
      closable
      onClose={onClose}
      footer={
        <>
          <Button className='w-24 mr-4' variant='secondary-accent' onClick={onClose}>
            {t('common.operation.cancel')}
          </Button>
          {isEdit
            ? (
              <Button className='w-24' disabled={disabled} variant='primary' onClick={onEditApp}>
                {t('common.operation.save')}
              </Button>
            )
            : (
              <Button className='w-24' disabled={disabled} variant='primary' onClick={onCreate}>
                {t('common.operation.create')}
              </Button>
            )}
        </>
      }
    >
      <Form layout='vertical' form={form} initialValues={{
        name: app?.name || defaultConfig?.name || '',
        description: app?.description || defaultConfig?.description || '',
      }}>
        {/* 应用类型 */}
        {!isEdit && <Form.Item required label={t('app.info.appCreateType')}>
          <Radio.Group
            type='default'
            onChange={(value) => {
              canChangeMode && setAppMode(value as AppMode)
            }}
            value={appMode}
            className='gap-3'
          >
            <Radio.Button value={'agent-chat'} className={s['app-mode-item']}>
              <AgentTagIcon className={s['mode-icon']} />
              <div className={s['mode-title']}>{t('app.newApp.basic')}</div>
              <Tooltip popupContent={t('app.placeholder.agentDescription')}></Tooltip>
            </Radio.Button>
            <Radio.Button value={'advanced-chat'} className={s['app-mode-item']}>
              <WorkflowTagIcon className={s['mode-icon']} />
              <div className={s['mode-title']}>{t('app.newApp.advanced')}</div>
              <Tooltip popupContent={t('app.placeholder.chatbotDescription')}></Tooltip>
            </Radio.Button>
            { enableCreateLongtextApp
              ? <Radio.Button value={'LongTextWorkflow'} className={s['app-mode-item']}>
                <CarryOutOutlined className={s['mode-icon']} />
                <div className={s['mode-title']}>{t('app.newApp.longDoc')}</div>
                <Tooltip popupContent={t('app.placeholder.templateDescription')}></Tooltip>
              </Radio.Button>
              : <></> }
          </Radio.Group>
        </Form.Item>}
        {/* 应用名称 */}
        <Form.Item
          name={'name'}
          validateFirst={true}
          rules={[
            {
              required: true,
              whitespace: true,
              max: MAX_APP_NAME_LENGTH,
            },
          ]}
          label={t('app.info.appName')}
          validateTrigger='onBlur'
        >
          <Input placeholder={t('app.placeholder.appName') || ''} maxLength={MAX_APP_NAME_LENGTH} />
        </Form.Item>
        {/* 应用描述 */}
        <Form.Item
          name={'description'}
          label={t('app.info.appDescription')}
          rules={[{ required: true, whitespace: true, max: MAX_APP_DESC_LENGTH }]}
        >
          <Input.TextArea
            placeholder={t('app.placeholder.appDescription') || ''}
            maxLength={MAX_APP_DESC_LENGTH}
          ></Input.TextArea>
        </Form.Item>
        {/* 应用头像 */}
        <Form.Item required label={t('app.info.appHead')}>
          <Avatar
            avatar={appIcon.url}
            size={80}
            className='!rounded'
            showUpload={true}
            onChange={changeAppHead}
            uploadTrigger={
              <span className='text-S3 leading-H3 text-white'>{t('app.action.reUpload')}</span>
            }
          ></Avatar>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default CreateAppModal
