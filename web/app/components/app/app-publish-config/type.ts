import type { IndicatorProps } from '../../base/indicator'

export enum statusEnum {
  pending = 'pending', // 未发布
  review = 'review', // 审核中
  online = 'online', // 已上架
  offline = 'offline', // 已下架
  failed = 'failed', // 未通过
  true = 'true', // 已上架
  false = 'false', // 已下架
}

export const statusMap: { [key in statusEnum]: {color: IndicatorProps['color'], text: string } } = {
  pending : { color: 'gray', text: 'app.publish.status.pending' },
  review: { color: 'blue', text: 'app.publish.status.review' },
  online: { color: 'green', text: 'app.publish.status.online' },
  offline: { color: 'gray', text: 'app.publish.status.offline' },
  failed: { color: 'yellow', text: 'app.publish.status.failed' },
  true: { color: 'green', text: 'app.publish.status.online' },
  false: { color: 'gray', text: 'app.publish.status.offline' },
}