'use client'
import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useContext, useContextSelector } from 'use-context-selector'
import Action from './action'
import type { IAppActionProps } from './action'
import {
  updateAppSiteAccessToken,
  updateAppSiteConfig,
  updateAppSiteStatus,
} from '@/service/apps'
import type { App } from '@/types/app'
import type { UpdateAppSiteCodeResponse } from '@/models/app'
import { asyncRunSafe } from '@/utils'

import { ToastContext } from '@/app/components/base/toast'
import SystemContext from '@/context/system-context'

export type ICardViewProps = {
  appDetail: App
  actionType: 'webapp' | 'api' | 'embed'
  hasPublish?: boolean
  onUpdateAppDetail?: () => void
}

const CardView: FC<ICardViewProps> = ({ appDetail, actionType, hasPublish, onUpdateAppDetail }) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)

  // 卡片类型
  const isApp = actionType === 'webapp'
  const isApi = actionType === 'api'
  const isEmbed = actionType === 'embed'
  const siteInfo = isApp ? appDetail.web_site : isApi ? appDetail.api_site : appDetail.embed_site
  const systemFeatures = useContextSelector(SystemContext, state => state.systemFeatures)

  // 处理变更回调事件
  const handleCallbackResult = (err: Error | null, message?: string) => {
    const type = err ? 'error' : 'success'

    message ||= (type === 'success' ? 'modifiedSuccessfully' : 'modifiedUnsuccessfully')

    if (type === 'success' && onUpdateAppDetail)
      onUpdateAppDetail()

    notify({
      type,
      message: t(`common.actionMsg.${message}`),
    })
  }
  const onChangeStatus = async (value: boolean) => {
    if (actionType === 'webapp')
      onChangeSiteStatus(value)
    else if (actionType === 'api')
      onChangeApiStatus(value)
    else if (actionType === 'embed')
      onChangeEmbedStatus(value)
  }
  // 变更站点状态
  const onChangeSiteStatus = async (value: boolean) => {
    const [err] = await asyncRunSafe<App>(
      updateAppSiteStatus({
        url: `/apps/${siteInfo.app_id}/site-enable`,
        body: { enable_site: value },
      }) as Promise<App>,
    )

    handleCallbackResult(err)
  }
  // 变更api状态
  const onChangeApiStatus = async (value: boolean) => {
    const [err] = await asyncRunSafe<App>(
      updateAppSiteStatus({
        url: `/apps/${siteInfo.app_id}/api-enable`,
        body: { enable_api: value },
      }) as Promise<App>,
    )

    handleCallbackResult(err)
  }
  const onChangeEmbedStatus = async (value: boolean) => {
    const [err] = await asyncRunSafe<App>(
      updateAppSiteStatus({
        url: `/apps/${siteInfo.app_id}/site-enable`,
        body: { enable_site: value },
      }) as Promise<App>,
    )

    handleCallbackResult(err)
  }
  // 保存站点配置
  const onSaveSiteConfig: IAppActionProps['onSaveSiteConfig'] = async (params) => {
    const [err] = await asyncRunSafe<App>(
      updateAppSiteConfig({
        url: `/apps/${siteInfo.app_id}/site`,
        body: params,
      }) as Promise<App>,
    )

    handleCallbackResult(err)
  }
  // 生成代码回调
  const onGenerateCode = async () => {
    const [err] = await asyncRunSafe<UpdateAppSiteCodeResponse>(
      updateAppSiteAccessToken({
        url: `/apps/${siteInfo.app_id}/site/access-token-reset`,
      }) as Promise<UpdateAppSiteCodeResponse>,
    )

    handleCallbackResult(err, err ? 'generatedUnsuccessfully' : 'generatedSuccessfully')
  }

  return (
    <Action
      appInfo={appDetail!}
      siteInfo={siteInfo}
      actionType={actionType}
      hasPublish={hasPublish}
      onChangeStatus={onChangeStatus}
      onGenerateCode={onGenerateCode}
      onSaveSiteConfig={onSaveSiteConfig}
    />
  )
}

export default CardView
