'use client'
import { useCallback, useEffect, useState } from 'react'
import { debounce } from 'lodash-es'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { Cascader, Form, Input } from 'antd'
import Button from '../../base/button'
import Modal from '@/app/components/base/modal'
import { createDatasetReflux } from '@/service/data-reflux'
import { ToastContext } from '@/app/components/base/toast'
import type { App } from '@/types/app'
import type { ChatConversationGeneralDetail } from '@/models/log'

type props = {
  appId: string
  selectedKeys: React.Key[]
  useTool?: boolean
  onClose: (status?: string) => void
  selectedNodeKeys?: React.Key[]
  appDetail: App
  selectedRows?: ChatConversationGeneralDetail[]
}
type DatasetTypeKey = 'LLM-CoT' | 'LLM-FC' | 'VLM-CoT' | 'VLM-FC' | 'LLM-SFT' | 'VLM-SFT'

enum Visibility {
  PUBLIC = 1,
  PRIVATE = 0,
}
const CreateDataRefluxModal = ({
  appId,
  selectedKeys,
  selectedNodeKeys,
  useTool,
  appDetail,
  onClose,
  selectedRows,
}: props) => {
  const I18N_PREFIX = 'appLog.dataReflux.modal'
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const [form] = Form.useForm()

  
  // 数据集类型
  const datasetTypeOptions = [
    {
      value: '2_GLM',
      label: t(`${I18N_PREFIX}.typeOptions.text`), // '文本',
      children: [
        {
          value: '2_GLM_SFT_FUN_CALL',
          label: 'SFT（Agent-CoT）',
        },
        {
          value: '2_GLM_AGENT_FC',
          label: 'SFT（Agent-FC）',
        },
        {
          value: '2_GLM_SFT',
          label: 'SFT',
        },
      ],
    },
    {
      value: '2_Multimodal',
      label: t(`${I18N_PREFIX}.typeOptions.multimodal`), // '多模态',
      children: [
        {
          value: '2_Multimodal_AGENT_COT',
          label: 'SFT（Agent-CoT）',
        },
        {
          value: '2_Multimodal_AGENT_FC',
          label: 'SFT（Agent-FC）',
        },
        {
          value: '2_Multimodal_SFT',
          label: 'SFT',
        },
      ],
    },
  ]
  // 是否标注
  const isAnnotationOptions = [
    { label: t(`${I18N_PREFIX}.isAnnotationYes`), value: 1, key: 1 },
    { label: t(`${I18N_PREFIX}.isAnnotationNo`), value: 0, key: 0, disabled: true },
  ]
  // 是否公开
  const radioOptions = [
    { label: t(`${I18N_PREFIX}.public`), desc: t(`${I18N_PREFIX}.publicDesc`), value: Visibility.PUBLIC },
    { label: t(`${I18N_PREFIX}.private`), desc: t(`${I18N_PREFIX}.privateDesc`), value: Visibility.PRIVATE },
  ]

  const values = Form.useWatch([], form)
  const [isLoading, setIsLoading] = useState(false)
  const [disabled, setDisabled] = useState(false)
  const [visibility, setVisibility] = useState(Visibility.PRIVATE)
  const [datasetUuid, setDatasetUuid] = useState('')
  
  // 保存
  const onSave = useCallback(() => {
    form.validateFields({ validateOnly: true }).then(async () => {
      // 根据 mode 动态决定 conversation_ids 的值
      let conversationIds = [];
      let nodeIds = [];
      if (appDetail.mode === 'advanced-chat') {
        conversationIds = selectedRows?.map(row => row.id).filter(id => id !== undefined) || [];
        nodeIds = selectedRows?.map(row => row.nodeId).filter(id => id !== undefined) || [];
        // console.log('selectedRows--',selectedRows,nodeIds)
      } else {
        conversationIds = selectedKeys;
        nodeIds = selectedNodeKeys || [];
      }
      // 提取所有模型名称到数组
      const modelNames = selectedRows?.map(row => row.model).filter(model => model !== undefined) || [];
      const query = {
        ...values,
        conversation_ids: conversationIds,
        node_ids:	nodeIds,
        datasetType: values.datasetType[1], // 默认值处理,
        model_names: modelNames,
        datasetAlias: values.datasetAlias ? values.datasetAlias : values.datasetName,
        // datasetAlias: values.datasetAlias || '',
        // datasetName: values.datasetName || '',
        // visibility,
      }
      setIsLoading(true)
      // console.log('query',query)
      // console.log('createModel',selectedRows)
      createDatasetReflux({ appId, body: query }).then((res: any) => {
        if (res.code === 1) {
          setDatasetUuid(res.datasetUuid)
          onClose('success')
        }
        else {
          if (res?.msg)
            notify({ type: 'error', message: res.msg })
          onClose('failed')
        }
      }).catch(() => {
        onClose('failed')
      }).finally(() => {
        setIsLoading(false)
      })
    },
    )
  }, [selectedKeys, visibility, values, createDatasetReflux, setDatasetUuid])

  useEffect(debounce(() => {
    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(false)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])

  const getType = (item: DatasetTypeKey): string[] => {
    const typeMap = {
      'LLM-SFT': ['2_GLM', '2_GLM_SFT'], 
      'LLM-CoT': ['2_GLM', '2_GLM_SFT_FUN_CALL'],
      'LLM-FC': ['2_GLM', '2_GLM_AGENT_FC'],
      'VLM-SFT': ['2_Multimodal', '2_Multimodal_SFT'],
      'VLM-CoT': ['2_Multimodal', '2_Multimodal_AGENT_COT'],
      'VLM-FC': ['2_Multimodal', '2_Multimodal_AGENT_FC'],
    }
    return typeMap[item]
  }
  // 类型直接写死 没有使用工具：2_GLM_SFT 使用工具：2_GLM_SFT_FUN_CALL
  useEffect(() => {
    // form.setFieldValue('datasetType', ['2_GLM', '2_GLM_SFT_FUN_CALL'])
    const dataset_type = selectedRows?.[0].dataset_type || ''
    const type = getType(dataset_type as DatasetTypeKey)
    form.setFieldValue('datasetType', type)
  }, [useTool])

  return (
    <Modal
      title={t(`${I18N_PREFIX}.title`)}
      isShow
      closable
      onClose={onClose}
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={() => {
            onClose()
          }}>{t('common.operation.abandon')}</Button>
          <Button
            disabled={disabled}
            variant='primary'
            onClick={onSave}
            loading={isLoading}
          >
            {t('common.operation.submit')}
          </Button>
        </>
      }
    >
      <Form form={form} layout='vertical' initialValues={{
        annotationState: 1,
        datasetDesc: '',
      }}>
        {/* 数据集类型 */}
        <Form.Item
          name={'datasetType'}
          label={t(`${I18N_PREFIX}.datasetType`)}
          required
        >
          <Cascader
            disabled
            options={datasetTypeOptions}
            showCheckedStrategy={Cascader.SHOW_CHILD}
            placeholder={t(`${I18N_PREFIX}.placeholder.datasetType`) || ''}
          />
        </Form.Item>
        {/* 是否标注 */}
        {/* <Form.Item
            name={'annotationState'}
            label={t(`${I18N_PREFIX}.annotationState`)}
            required
          >
            <Radio.Group
              options={isAnnotationOptions}
            >
            </Radio.Group>
        </Form.Item> */}
        {/* 数据集名称 */}
        <Form.Item
          name={'datasetName'}
          label={t(`${I18N_PREFIX}.datasetName`)}
          tooltip={(
            <ul className={'px-3'} style={{ listStyleType: 'disc' }}>
              <span className='-mx-3'>{t(`${I18N_PREFIX}.datasetNameTip.title`)}</span>
              <li>{t(`${I18N_PREFIX}.notify.datasetNameLength`)}</li>
              <li>{t(`${I18N_PREFIX}.notify.datasetNameStart`)}</li>
              <li>{t(`${I18N_PREFIX}.notify.datasetNameEnd`)}</li>
              <li>{t(`${I18N_PREFIX}.notify.datasetNameSpecial`)}</li>
              <li>{t(`${I18N_PREFIX}.notify.datasetNameSpecialConsecutive`)}</li>
            </ul>
          )}
          required
          rules={[
            { required: true, whitespace: true },
            { min: 2, max: 64, message: t(`${I18N_PREFIX}.notify.datasetNameLength`) || '' },
            { pattern: /^[a-zA-Z]/, message: t(`${I18N_PREFIX}.notify.datasetNameStart`) || '' },
            { pattern: /[a-zA-Z0-9]$/, message: t(`${I18N_PREFIX}.notify.datasetNameEnd`) || '' },
            { pattern: /^[a-zA-Z0-9-._]+$/, message: t(`${I18N_PREFIX}.notify.datasetNameSpecial`) || '' },
            { pattern: /^(?!.*[_\.-]{2,}).*/, message: t(`${I18N_PREFIX}.notify.datasetNameSpecialConsecutive`) || '' },
          ]}
        >
          <Input
            placeholder={t(`${I18N_PREFIX}.placeholder.datasetName`) || ''}
            maxLength={64}
            max={66}
            showCount
          ></Input>
        </Form.Item>
        {/* 数据集别名 */}
        <Form.Item
          name={'datasetAlias'}
          label={t(`${I18N_PREFIX}.datasetAlias`)}
          tooltip={t(`${I18N_PREFIX}.notify.datasetAliasCallTip`)}
          rules={[
            { pattern: /^[\u4E00-\u9FA5a-zA-Z0-9_\.-]{1,64}$/, message: t(`${I18N_PREFIX}.notify.datasetAliasCallTip`) || '' },
          ]}
        >
          <Input
            placeholder={t(`${I18N_PREFIX}.placeholder.datasetAlias`) || ''}
            maxLength={64}
            showCount
          ></Input>
        </Form.Item>
        {/* 数据集描述 */}
        {/* <Form.Item
            name={'datasetDesc'}
            label={t(`${I18N_PREFIX}.datasetDesc`)}
          >
          <Input.TextArea
            placeholder={t(`${I18N_PREFIX}.placeholder.datasetName`) || ''}
            maxLength={150}
            showCount
            className='h-[66px]'
          ></Input.TextArea>
        </Form.Item> */}
        {/* 可见性 公开/私有 */}
        {/* {radioOptions.map(item => (
          <RadioCard
            key={item.value}
            className={cn(style.radioItem, visibility === item.value && style.active)}
            title={t(`${I18N_PREFIX}.${item.value ? 'public' : 'private'}`)}
            description={t(`${I18N_PREFIX}.${item.value ? 'public' : 'private'}Desc`)}
            isChosen={visibility === item.value}
            onChosen={() => setVisibility(item.value)}
            radioPosition='left'
            size='small'
            radioClassName='h-6'
          >
          </RadioCard>
        ))} */}
      </Form>
    </Modal>
  )
}

export default CreateDataRefluxModal
