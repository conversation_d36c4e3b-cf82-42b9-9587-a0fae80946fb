.radioItem:hover {
  border-color: var(--color-primary-P1);
}

.radioItem:hover .radio {
  border-color: var(--color-primary-P1);
}

.radioItem {
  @apply py-4 bg-gray-G7 mb-3 last-of-type:mb-0;
  border-radius: 4px;
  border-color: transparent;
}

.radioItem.active {
  border: 1px solid var(--color-primary-P1);
  border-color: var(--color-primary-P1);
  background: linear-gradient(0deg, #FFF 57.78%, #ECF3FF 100%);
  box-shadow: 0px 3px 8px 0px rgba(159, 176, 201, 0.50);
}
