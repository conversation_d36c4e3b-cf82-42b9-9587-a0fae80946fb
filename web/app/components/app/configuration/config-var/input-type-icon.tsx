import React from 'react'
import type { FC } from 'react'
import InputVarTypeIcon from '@/app/components/workflow/nodes/_base/components/variable/input-var-type-icon'
import { InputVarType } from '@/app/components/workflow/types'

// 公告能力
import { ApiConnection } from '@/app/components/base/icons/src/vender/solid/others'

export type IInputTypeIconProps = {
  type: 'string' | 'select'
  className: string
}

const IconMap = (type: IInputTypeIconProps['type'], className: string) => {
  const classNames = `w-4 h-4 ${className}`
  const icons = {
    string: (
      <InputVarTypeIcon type={InputVarType.textInput} className={classNames} />
    ),
    paragraph: (
      <InputVarTypeIcon type={InputVarType.paragraph} className={classNames} />
    ),
    select: (
      <InputVarTypeIcon type={InputVarType.select} className={classNames} />
    ),
    number: (
      <InputVarTypeIcon type={InputVarType.number} className={classNames} />
    ),
    api: (
      <ApiConnection className={classNames} />
    ),
  }

  return icons[type]
}

const InputTypeIcon: FC<IInputTypeIconProps> = ({
  type,
  className,
}) => {
  const Icon = IconMap(type, className)
  return Icon
}

export default React.memo(InputTypeIcon)
