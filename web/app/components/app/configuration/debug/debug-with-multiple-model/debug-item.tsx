import type { CSSProperties, FC } from 'react'
import { useTranslation } from 'react-i18next'
import { usePathname } from 'next/navigation'
import { memo, useEffect, useState } from 'react'
import { Switch } from 'antd'
import type { ModelAndParameter } from '../types'
import ModelParameterTrigger from './model-parameter-trigger'
import ChatItem from './chat-item'
import { useDebugWithMultipleModelContext } from './context'
import s from './styles/style.module.css'
import { BasicParamModeEnum } from '@/types/model'
import { useDebugConfigurationContext } from '@/context/debug-configuration'
import Tooltip from '@/app/components/base/tooltip'
import { Close } from '@/app/components/base/icons/src/vender/line/general'
import { useProviderContext } from '@/context/provider-context'
import useStoragedModelConfig from '@/app/components/app/configuration/hooks/use-storaged-model-config'
import { ModelStatusEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import cn from '@/utils/classnames'
import TextButton from '@/app/components/base/button/text-button'

type DebugItemProps = {
  modelAndParameter: ModelAndParameter
  className?: string
  style?: CSSProperties
}
const DebugItem: FC<DebugItemProps> = ({
  modelAndParameter,
  className,
  style,
}) => {
  const { t } = useTranslation()
  const { mode } = useDebugConfigurationContext()
  const {
    bestModel,
    multipleModelConfigs,
    onMultipleModelConfigsChange,
    onDebugWithMultipleModelChange,
  } = useDebugWithMultipleModelContext()
  const { textGenerationModelList } = useProviderContext()

  const pathname = usePathname()
  const matched = pathname.match(/\/app\/([^/]+)/)
  const appId = (matched?.length && matched[1]) ? matched[1] : ''

  const index = multipleModelConfigs.findIndex(v => v.id === modelAndParameter.id)
  const currentProvider = textGenerationModelList.find(item => item.provider === modelAndParameter.provider)
  const currentModel = currentProvider?.models.find(item => item.model === modelAndParameter.model)
  const [basicParamMode, setBasicParamMode] = useState<BasicParamModeEnum>(BasicParamModeEnum.normal)

  // 生物多样性模式初始化
  useEffect(() => {
    setBasicParamMode(modelAndParameter.basicParamMode || BasicParamModeEnum.normal)
  }, [])

  const handleBasicParamsModeChange = (value: BasicParamModeEnum) => {
    setBasicParamMode(value)
  }

  const handleRemove = () => {
    onMultipleModelConfigsChange(
      true,
      multipleModelConfigs.filter(item => item.id !== modelAndParameter.id),
      bestModel,
    )
  }

  const {
    updateStoragedModelConfigByKey,
    getModelConfigStoraged,
    deleteModelConfigStoraged,
  } = useStoragedModelConfig({
    appId,
  })

  // 处理多模型情况下 模型参数模式变更
  const handleModelParamsModeChange = (key: string, value: any) => {
    updateStoragedModelConfigByKey(modelAndParameter.provider, modelAndParameter.model, key, value)
    if (key === 'basicParamMode')
      setBasicParamMode(value as BasicParamModeEnum)
  }

  // const handleSelect = (item: Item) => {
  //   if (item.value === 'duplicate') {
  //     if (multipleModelConfigs.length >= 4)
  //       return

  //     onMultipleModelConfigsChange(
  //       true,
  //       [
  //         ...multipleModelConfigs.slice(0, index + 1),
  //         {
  //           ...modelAndParameter,
  //           id: `${Date.now()}`,
  //         },
  //         ...multipleModelConfigs.slice(index + 1),
  //       ],
  //       bestModel
  //     )
  //   }
  //   if (item.value === 'debug-as-single-model')
  //     onDebugWithMultipleModelChange(modelAndParameter)
  //   if (item.value === 'remove') {
  //     onMultipleModelConfigsChange(
  //       true,
  //       multipleModelConfigs.filter(item => item.id !== modelAndParameter.id),
  //       bestModel
  //     )
  //   }
  // }

  return (
    <div
      className={cn(`${s.debugItemWrap} ${className}`)}
      style={style}
    >
      <div className={s.debugItemHeader}>
        {/* 切换最优模型 */}
        <div className='grow space-x-1 shrink-1 truncate'>
          <Tooltip popupContent={t('appDebug.errorMessage.notSelectModel')} disabled={!!modelAndParameter.model}>
            <Switch
              size='small'
              disabled={!modelAndParameter.model}
              value={bestModel === modelAndParameter.id}
              onChange={(checked) => {
                onMultipleModelConfigsChange(true, multipleModelConfigs, checked ? modelAndParameter.id : '')
              }}
            />
          </Tooltip>
          <span className='text-S1 leading-H1 text-gray-G2'>{t('appDebug.bestModel')}</span>
        </div>
        {/* 选择模型弹框 */}
        <ModelParameterTrigger
          triggerClassName={'grow'}
          basicParamMode={basicParamMode}
          onBasicParamsModeChange={handleBasicParamsModeChange}
          modelAndParameter={modelAndParameter}
          onModelParamsModeChange={handleModelParamsModeChange}
          getModelConfigStoraged={getModelConfigStoraged}

        />
        {/* 关闭模型 */}
        <TextButton onClick={handleRemove} disabled={multipleModelConfigs.length <= 2}>
          <Tooltip popupContent={t('appDebug.removeModel')}>
            <Close className='w-4 h-4 text-gray-G3'/>
          </Tooltip>
        </TextButton>
      </div>
      <div style={{ height: 'calc(100% - 46px)' }}>
        {
          (mode === 'chat' || mode === 'agent-chat') && currentProvider && currentModel && currentModel.status === ModelStatusEnum.active && (
            <ChatItem modelAndParameter={modelAndParameter} />
          )
        }
      </div>
    </div>
  )
}

export default memo(DebugItem)
