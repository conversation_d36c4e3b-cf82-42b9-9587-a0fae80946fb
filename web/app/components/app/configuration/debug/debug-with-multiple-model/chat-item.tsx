import type { FC } from 'react'
import {
  memo,
  useCallback,
  useMemo,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import type { ModelAndParameter } from '../types'
import { APP_CHAT_WITH_MULTIPLE_MODEL, APP_CHAT_WITH_MULTIPLE_MODEL_RESTART } from '../types'
import { useConfigFromDebugContext, useFormattingChangedSubscription } from '../hooks'
import Chat from '@/app/components/base/chat/pref-chat'
import { useChat } from '@/app/components/base/chat/pref-chat/hooks'
import { useDebugConfigurationContext } from '@/context/debug-configuration'
import { useEventEmitterContextContext } from '@/context/event-emitter'
import { useProviderContext } from '@/context/provider-context'
import {
  fetchConversationMessages,
  fetchSuggestedQuestions,
  stopChatMessageResponding,
} from '@/service/debug'
import Avatar from '@/app/components/base/avatar'
import { useStore as useAppStore } from '@/app/components/app/store'
import { useAppContext } from '@/context/app-context'
import { useFeatures } from '@/app/components/base/features/hooks'
import { getLastAnswer } from '@/app/components/base/chat/utils'
import cn from '@/utils/classnames'
import style from '@/app/components/base/chat/chat-with-history/styles/style.module.css'
import { ToastContext } from '@/app/components/base/toast'
import { updateLogMessageFeedbacks } from '@/service/log'
import type { ChatConfig, FeedbackType, InputForm, OnSend } from '@/types/chat'

type ChatItemProps = {
  modelAndParameter: ModelAndParameter
}
const ChatItem: FC<ChatItemProps> = ({ modelAndParameter }) => {
  const { userProfile } = useAppContext()
  const { notify } = useContext(ToastContext)
  const { t } = useTranslation()
  const {
    modelConfig,
    appId,
    inputs,
    collectionList,
  } = useDebugConfigurationContext()
  const app = useAppStore(state => state.appDetail)
  const { textGenerationModelList } = useProviderContext()
  const features = useFeatures(s => s.features)
  const configTemplate = useConfigFromDebugContext()
  const [appPrevChatList] = useState([])
  const config = useMemo(() => {
    return {
      ...configTemplate,
      more_like_this: features.moreLikeThis,
      opening_statement: features.opening?.enabled
        ? (features.opening?.opening_statement || '')
        : '',
      suggested_questions: features.opening?.enabled
        ? (features.opening?.suggested_questions || [])
        : [],
      sensitive_word_avoidance: features.moderation,
      speech_to_text: features.speech2text,
      text_to_speech: features.text2speech,
      file_upload: features.file,
      suggested_questions_after_answer: features.suggested,
      retriever_resource: features.citation,
      annotation_reply: features.annotationReply,
      supportFeedback: true,
    } as ChatConfig
  }, [configTemplate, features])
  const inputsForm = useMemo(() => {
    return modelConfig.configs.prompt_variables
      .filter(item => item.type !== 'api')
      .map(item => ({ ...item, label: item.name, variable: item.key })) as InputForm[]
  }, [modelConfig.configs.prompt_variables])
  // 停止函数
  const stopChat = useCallback((taskId: string) => {
    stopChatMessageResponding(appId, taskId)
  }, [appId])
  const {
    chatList,
    chatListRef,
    currentChatList,
    isResponding,
    isVoiceConversation,
    handleSend,
    suggestedQuestions,
    handleRestart,
    openVoiceCall,
    closeVoiceCall,
  } = useChat(
    config,
    {
      inputs,
      inputsForm,
    },
    appPrevChatList,
    stopChat,
  )
  useFormattingChangedSubscription(chatList)

  const doSend: OnSend = useCallback(
    (message, files) => {
      const currentProvider = textGenerationModelList.find(
        item => item.provider === modelAndParameter.provider,
      )
      const currentModel = currentProvider?.models.find(
        model => model.model === modelAndParameter.model,
      )
      // const supportVision = currentModel?.features?.includes(ModelFeatureEnum.vision)
      const supportVision = true

      const configData = {
        ...config,
        model: {
          provider: modelAndParameter.provider,
          name: modelAndParameter.model,
          mode: currentModel?.model_properties.mode,
          completion_params: modelAndParameter.parameters,
        },
      }

      const data: any = {
        query: message,
        inputs,
        model_config: configData,
        parent_message_id: getLastAnswer(chatListRef.current)?.id || null,
      }

      if ((config.file_upload as any).enabled && files?.length && supportVision)
        data.files = files

      handleSend(
        `apps/${appId}/chat-messages`,
        data,
        {
          onGetConversationMessages: (conversationId, getAbortController) => fetchConversationMessages(appId, conversationId, getAbortController),
          onGetSuggestedQuestions: (responseItemId, getAbortController) => fetchSuggestedQuestions(appId, responseItemId, getAbortController),
        },
      )
    }, [appId, config, handleSend, inputs, modelAndParameter, textGenerationModelList, chatListRef])
  // 点赞回调函数
  const handleFeedback = useCallback(async (mid: string, { rating }: FeedbackType): Promise<boolean> => {
    try {
      await updateLogMessageFeedbacks({ url: `/apps/${appId}/feedbacks`, body: { message_id: mid, rating } })
      notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
      return true
    }
    catch (err) {
      notify({ type: 'error', message: t('common.actionMsg.modifiedUnsuccessfully') })
      return false
    }
  }, [appId, notify, t])
  // 语音通话
  const doVocieCall = useCallback(() => {
    openVoiceCall({})
  }, [openVoiceCall])

  const { eventEmitter } = useEventEmitterContextContext()
  eventEmitter?.useSubscription((v: any) => {
    if (v.type === APP_CHAT_WITH_MULTIPLE_MODEL)
      doSend(v.payload.message, v.payload.files)
    if (v.type === APP_CHAT_WITH_MULTIPLE_MODEL_RESTART)
      handleRestart()
  })

  const allToolIcons = useMemo(() => {
    const icons: Record<string, any> = {}
    modelConfig.agentConfig.tools?.forEach((item: any) => {
      icons[item.tool_name] = collectionList.find(
        (collection: any) => collection.id === item.provider_id,
      )?.icon
    })
    return icons
  }, [collectionList, modelConfig.agentConfig.tools])

  return (
    <Chat
      appData={app as any}
      answerIcon={app?.icon_url || app?.site.icon_url || ''}
      config={config}
      chatList={chatList}
      isResponding={isResponding}
      noChatInput
      noStopResponding
      chatContainerInnerClassName={cn('px-6 mx-auto w-full max-w-[800px]', style['debug-with-multiple-model'])}
      chatFooterClassName='bottom-[36px]'
      chatFooterInnerClassName={'mx-auto w-full max-w-[800px]'}
      suggestedQuestions={suggestedQuestions}
      onSend={doSend}
      onFeedback={handleFeedback}
      showPromptLog
      questionIcon={<Avatar name={userProfile.name} size={32} />}
      hideLogModal
      showInputBottomTip
      isVoiceCall={isVoiceConversation}
      onVoiceCall={doVocieCall}
      onCancelVoiceCall={closeVoiceCall}
      currentChatList={currentChatList}
    />
  )
}

export default memo(ChatItem)
