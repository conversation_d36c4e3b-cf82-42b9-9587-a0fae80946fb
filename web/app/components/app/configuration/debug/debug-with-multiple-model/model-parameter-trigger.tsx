import type { FC } from 'react'
import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import type { ModelAndParameter } from '../types'
import { useDebugWithMultipleModelContext } from './context'
import ModelParameterModal from '@/app/components/account-setting/model-provider-page/model-parameter-modal'
import ModelIcon from '@/app/components/account-setting/model-provider-page/model-icon'
import ModelName from '@/app/components/account-setting/model-provider-page/model-name'
import {
  MODEL_STATUS_TEXT,
  ModelStatusEnum,
} from '@/app/components/account-setting/model-provider-page/declarations'
import { AdvancedParamModeEnum, BasicParamModeEnum, type ModelConfigStorage } from '@/types/model'
import { useDebugConfigurationContext } from '@/context/debug-configuration'
import Tooltip from '@/app/components/base/tooltip'
import { WarningTriangle } from '@/app/components/base/icons/src/vender/line/tip'
import { useLanguage } from '@/app/components/account-setting/model-provider-page/hooks'

type ModelParameterTriggerProps = {
  triggerClassName?: string
  basicParamMode?: BasicParamModeEnum
  onBasicParamsModeChange: (value: BasicParamModeEnum) => void
  modelAndParameter: ModelAndParameter
  onModelParamsModeChange: (key: string, value: any) => void
  getModelConfigStoraged: (provider: string, modelId: string) => Promise<ModelConfigStorage>
}
const ModelParameterTrigger: FC<ModelParameterTriggerProps> = ({
  triggerClassName,
  basicParamMode,
  onBasicParamsModeChange,
  modelAndParameter,
  onModelParamsModeChange,
  getModelConfigStoraged,
}) => {
  const { t } = useTranslation()
  const {
    mode,
    isAdvancedMode,
  } = useDebugConfigurationContext()
  const {
    bestModel,
    multipleModelConfigs,
    onMultipleModelConfigsChange,
    onDebugWithMultipleModelChange,
  } = useDebugWithMultipleModelContext()
  const language = useLanguage()
  const index = multipleModelConfigs.findIndex(v => v.id === modelAndParameter.id)
  const handleSelectModel = async ({ modelId, provider }: { modelId: string; provider: string }) => {
    const newModelConfigs = [...multipleModelConfigs]
    const config = await getModelConfigStoraged(provider, modelId)
    newModelConfigs[index] = {
      id: modelAndParameter.id,
      model: modelId,
      provider,
      parameters: config?.params || {},
      basicParamMode: config?.basicParamMode ? config.basicParamMode : BasicParamModeEnum.normal,
      advancedParamMode: config?.advancedParamMode ? config.advancedParamMode : AdvancedParamModeEnum.normal,
      openAdvancedParamMode: config?.openAdvancedParamMode || false,
    }
    onBasicParamsModeChange(config?.basicParamMode ? config.basicParamMode : BasicParamModeEnum.normal)
    onMultipleModelConfigsChange(true, newModelConfigs, bestModel)
  }
  // 处理多模型情况下 模型参数变更
  const handleParamsChange = async (params: any, useStore?: boolean) => {
    const newModelConfigs = [...multipleModelConfigs]
    newModelConfigs[index] = {
      ...newModelConfigs[index],
      parameters: params,
    }
    // 当前开启的多模型配置存到localStorage，同时也把模型参数配置存储到数据库中
    onMultipleModelConfigsChange(true, newModelConfigs, bestModel)
    if (useStore) {
      const storaged = await getModelConfigStoraged(modelAndParameter.provider, modelAndParameter.model)
      onModelParamsModeChange('params', {
        ...storaged?.params,
        ...params,
      })
    }
  }

  return (
    <ModelParameterModal
      mode={mode}
      isAdvancedMode={isAdvancedMode}
      provider={modelAndParameter.provider}
      modelId={modelAndParameter.model}
      basicParamMode={basicParamMode}
      completionParams={modelAndParameter.parameters}
      onCompletionParamsChange={handleParamsChange}
      onModelParamsModeChange={onModelParamsModeChange}
      getModelConfigStoraged={getModelConfigStoraged}
      setModel={handleSelectModel}
      debugWithMultipleModel
      onDebugWithMultipleModelChange={() => onDebugWithMultipleModelChange(modelAndParameter)}
      triggerClassName={triggerClassName}
      renderTrigger={({
        open,
        currentProvider,
        currentModel,
      }) => (
        <div
          className={`
            flex justify-start items-center w-full cursor-pointer shrink-1 text-S1 leading-H1 space-x-1
            ${currentModel && currentModel.status !== ModelStatusEnum.active && '!bg-[#FFFAEB]'}
          `}
        >
          {
            currentProvider && (
              <ModelIcon
                className='!w-5 !h-5'
                provider={currentProvider}
                modelName={currentModel?.model}
                model={currentModel}
              />
            )
          }
          {
            currentModel && (
              <ModelName
                className='truncate text-S1 leading-H1 text-gray-G2'
                modelItem={currentModel}
              />
            )
          }
          {
            !currentModel && (
              <div className='truncate text-S1 leading-H1 text-gray-G1'>
                {t('account.modelProvider.action.selectModel')}
              </div>
            )
          }
          {
            currentModel && currentModel.status !== ModelStatusEnum.active && (
              <Tooltip popupContent={MODEL_STATUS_TEXT[currentModel.status][language]}>
                <WarningTriangle className='w-4 h-4 text-[#F79009]' />
              </Tooltip>
            )
          }
        </div>
      )}
    />
  )
}

export default memo(ModelParameterTrigger)
