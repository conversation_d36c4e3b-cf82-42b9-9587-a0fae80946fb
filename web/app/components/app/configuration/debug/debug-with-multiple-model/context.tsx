'use client'

import { createContext, useContext } from 'use-context-selector'
import type { ModelAndParameter } from '../types'

export type DebugWithMultipleModelContextType = {
  bestModel: string
  multipleModelConfigs: ModelAndParameter[]
  onMultipleModelConfigsChange: (multiple: boolean, modelConfigs: ModelAndParameter[], bestModel: string) => void
  onDebugWithMultipleModelChange: (singleModelConfig: ModelAndParameter) => void
  checkCanSend?: () => boolean
  handleNewConversation?: () => void
}
const DebugWithMultipleModelContext = createContext<DebugWithMultipleModelContextType>({
  bestModel: '',
  multipleModelConfigs: [],
  onMultipleModelConfigsChange: () => {},
  onDebugWithMultipleModelChange: () => {},
})

export const useDebugWithMultipleModelContext = () => useContext(DebugWithMultipleModelContext)

type DebugWithMultipleModelContextProviderProps = {
  children: React.ReactNode
} & DebugWithMultipleModelContextType
export const DebugWithMultipleModelContextProvider = ({
  children,
  bestModel,
  onMultipleModelConfigsChange,
  multipleModelConfigs,
  onDebugWithMultipleModelChange,
  checkCanSend,
  handleNewConversation,
}: DebugWithMultipleModelContextProviderProps) => {
  return (
    <DebugWithMultipleModelContext.Provider value={{
      bestModel,
      onMultipleModelConfigsChange,
      multipleModelConfigs,
      onDebugWithMultipleModelChange,
      checkCanSend,
      handleNewConversation,
    }}>
      {children}
    </DebugWithMultipleModelContext.Provider>
  )
}

export default DebugWithMultipleModelContext
