import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperative<PERSON>andle,
  useMemo,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import {
  useConfigFromDebugContext,
  useFormattingChangedSubscription,
} from '../hooks'
import Chat from '@/app/components/base/chat/pref-chat'
import { useChat } from '@/app/components/base/chat/pref-chat/hooks'
import { useDebugConfigurationContext } from '@/context/debug-configuration'
import {
  fetchConversationMessages,
  fetchSuggestedQuestions,
  stopChatMessageResponding,
  // generationConversationName
} from '@/service/debug'
import { getCropperFontCss } from '@/app/components/app/common/bg-cropper/utils'

import Avatar from '@/app/components/base/avatar'
import { useAppContext } from '@/context/app-context'
import { useStore as useAppStore } from '@/app/components/app/store'
import { useFeatures } from '@/app/components/base/features/hooks'
import { getLastAnswer } from '@/app/components/base/chat/utils'
import { ToastContext } from '@/app/components/base/toast'
import { updateLogMessageFeedbacks } from '@/service/log'
import cn from '@/utils/classnames'
import { FeatureEnum } from '@/app/components/base/features/types'
import { useChatWithHistoryContext } from '@/app/components/base/chat/chat-with-history/context'
import type { ChatConfig, ChatItem, FeedbackType, InputForm, OnSend } from '@/types/chat'

type DebugWithSingleModelProps = {
  checkCanSend?: () => boolean
  handleNewConversation?: () => void
}
export type DebugWithSingleModelRefType = {
  handleRestart: () => void
}
const DebugWithSingleModel = forwardRef<DebugWithSingleModelRefType, DebugWithSingleModelProps>(({
  checkCanSend,
  handleNewConversation,
}, ref) => {
  const { notify } = useContext(ToastContext)
  const { t } = useTranslation()
  const { userProfile } = useAppContext()
  const {
    modelConfig,
    appId,
    inputs,
    completionParams,
    setIsVoiceCalling,
    // isShowVisionConfig,
  } = useDebugConfigurationContext()
  const appDetail = useAppStore(state => state.appDetail)
  const features = useFeatures(s => s.features)
  const configTemplate = useConfigFromDebugContext()
  const bgConfig = useFeatures(s => s.features[FeatureEnum.chatBg])
  const {
    currentConversationId,
    setCurrentChatConversationId,
  } = useChatWithHistoryContext()
  // 对话列表
  const [appPrevChatList] = useState([])
  // 对话配置
  const config = useMemo(() => {
    return {
      ...configTemplate,
      more_like_this: features.moreLikeThis,
      opening_statement: features.opening?.enabled ? (features.opening?.opening_statement || '') : '',
      suggested_questions: features.opening?.enabled ? (features.opening?.suggested_questions || []) : [],
      sensitive_word_avoidance: features.moderation,
      speech_to_text: features.speech2text,
      text_to_speech: features.text2speech,
      file_upload: features.file,
      suggested_questions_after_answer: features.suggested,
      retriever_resource: features.citation,
      annotation_reply: features.annotationReply,
      supportFeedback: true,
      // voiceInput: features.text2speech?.voice_input,
      // voiceConversation: features.text2speech?.voice_conversation
    } as unknown as ChatConfig
  }, [configTemplate, features])
  const inputsForm = useMemo(() => {
    return modelConfig.configs.prompt_variables.filter(item => item.type !== 'api').map(item => ({ ...item, label: item.name, variable: item.key })) as InputForm[]
  }, [modelConfig.configs.prompt_variables])
  // 问题图标
  const questionIcon = useMemo(() => {
    return <Avatar name={userProfile.name} size={32} />
  }, [userProfile.name])
  const stopChat = useMemo(() => {
    return (taskId: string) => {
      stopChatMessageResponding(appId, taskId)
    }
  }, [appId])

  // 对话api
  const {
    chatList,
    chatListRef,
    currentChatList,
    isResponding,
    handleSend,
    suggestedQuestions,
    handleStop,
    handleUpdateChatList,
    handleRestart,
    handleAnnotationAdded,
    handleAnnotationEdited,
    handleAnnotationRemoved,
    isVoiceConversation,
    openVoiceCall,
    closeVoiceCall,
    isThinking,
    isConnecting,
  } = useChat(
    config,
    {
      inputs,
      inputsForm,
    },
    appPrevChatList,
    stopChat,
  )
  useFormattingChangedSubscription(chatList)

  // 发送对话信息
  const doSend: OnSend = useCallback((message, files, last_answer) => {
    if (checkCanSend && !checkCanSend())
      return
    // const supportVision = currentModel?.features?.includes(ModelFeatureEnum.vision)
    const supportVision = true
    const configData = {
      ...config,
      model: {
        provider: modelConfig.provider,
        name: modelConfig.model_id,
        mode: modelConfig.mode,
        completion_params: completionParams,
      },
    }

    const data: any = {
      query: message,
      inputs,
      model_config: configData,
      parent_message_id: last_answer?.id || getLastAnswer(chatListRef.current)?.id || null,
    }

    if ((config.file_upload as any)?.enabled && files?.length && supportVision)
      data.files = files

    handleSend(
      `apps/${appId}/chat-messages`,
      data,
      {
        onGetConversationMessages: (conversationId, getAbortController) => fetchConversationMessages(appId, conversationId, getAbortController),
        onGetSuggestedQuestions: (responseItemId, getAbortController) => fetchSuggestedQuestions(appId, responseItemId, getAbortController),
      },
    )
  }, [checkCanSend, config, modelConfig.provider, modelConfig.model_id, modelConfig.mode, completionParams, inputs, chatListRef, handleSend, appId])
  // 重新生成对话信息
  const doRegenerate = useCallback((chatItem: ChatItem) => {
    const index = chatListRef.current.findIndex(item => item.id === chatItem.id)
    if (index === -1)
      return

    const prevMessages = chatListRef.current.slice(0, index)
    const question = prevMessages.pop()
    const lastAnswer = getLastAnswer(prevMessages)

    if (!question)
      return

    handleUpdateChatList(prevMessages)
    doSend(question.content, question.message_files, lastAnswer)
  }, [chatListRef, handleUpdateChatList, doSend])
  // 点赞回调函数
  const handleFeedback = useCallback(async (mid: string, bodyValue: FeedbackType): Promise<boolean> => {
    const body = {
      ...bodyValue,
      message_id: mid,
    }
    try {
      // await updateLogMessageFeedbacks({ url: `/apps/${appId}/feedbacks`, body: { message_id: mid, rating } })
      await updateLogMessageFeedbacks({ url: `/apps/${appId}/feedbacks`, body: body as any })
      notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
      return true
    }
    catch (err) {
      notify({ type: 'error', message: t('common.actionMsg.modifiedUnsuccessfully') })
      return false
    }
  }, [appId, notify, t])
  // 语音通话
  const doVocieCall = useCallback(() => {
    const configData = {
      ...config,
      model: {
        provider: modelConfig.provider,
        name: modelConfig.model_id,
        mode: modelConfig.mode,
        completion_params: completionParams,
      },
    }
    openVoiceCall({
      conversation_id: currentConversationId,
      onGetConversationId: (conversationId) => {
        setCurrentChatConversationId(conversationId)
      },
      bodyParams: configData,
    })
  }, [config, modelConfig, completionParams, openVoiceCall, currentConversationId, setCurrentChatConversationId])
  // 取消语音通话
  const cancelVoiceCall = useCallback(() => {
    closeVoiceCall()
  }, [closeVoiceCall])

  useImperativeHandle(ref, () => {
    return {
      handleRestart,
    }
  }, [handleRestart])
  useEffect(() => {
    setIsVoiceCalling(isVoiceConversation || isConnecting)
  }, [isVoiceConversation, isConnecting, setIsVoiceCalling])

  return (
    <Chat
      appData={appDetail as any}
      answerIcon={appDetail?.icon_url || appDetail?.site.icon_url || ''}
      config={config}
      chatList={chatList}
      isResponding={isResponding}
      chatContainerClassName='px-6'
      chatContainerInnerClassName={cn('mx-auto w-full max-w-[800px]', getCropperFontCss(bgConfig))}
      chatFooterClassName='bottom-0'
      chatFooterInnerClassName={'mx-auto w-full max-w-[800px]'}
      showFileUpload={false}
      suggestedQuestions={suggestedQuestions}
      onSend={doSend}
      inputs={inputs}
      inputsForm={inputsForm}
      onRegenerate={doRegenerate}
      onStopResponding={handleStop}
      showPromptLog
      questionIcon={questionIcon}
      onAnnotationEdited={handleAnnotationEdited}
      onAnnotationAdded={handleAnnotationAdded}
      onAnnotationRemoved={handleAnnotationRemoved}
      onFeedback={handleFeedback}
      showInputBottomTip
      chartHasPrev={false}
      handleNewConversation={handleNewConversation}
      isVoiceCall={isVoiceConversation}
      isThinking={isThinking}
      isConnecting={isConnecting}
      onVoiceCall={doVocieCall}
      onCancelVoiceCall={cancelVoiceCall}
      currentChatList={currentChatList}
    />
  )
})

DebugWithSingleModel.displayName = 'DebugWithSingleModel'

export default memo(DebugWithSingleModel)
