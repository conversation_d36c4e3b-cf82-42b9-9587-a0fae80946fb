'use client'
import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import cn from '@/utils/classnames'

export type IOperationBtnProps = {
  className?: string
  type: 'add' | 'edit'
  actionName?: string
  onClick?: () => void
}

const iconMap = {
  add: (<svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
    <path d="M8.79688 5.5L8.79687 10.5" stroke="currentColor" strokeLinecap="round"/>
    <path d="M11.2969 8L6.29688 8" stroke="currentColor" strokeLinecap="round"/>
    <circle cx="8.79688" cy="8" r="6.5" stroke="currentColor"/>
  </svg>),
  edit: (<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
    <path d="M0.536133 12.627H12.5361" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M1.86963 7.53359V9.96029H4.3087L11.203 3.06299L8.76799 0.626953L1.86963 7.53359Z" stroke="currentColor" strokeLinejoin="round"/>
  </svg>
  ),
}

const OperationBtn: FC<IOperationBtnProps> = ({
  className,
  type,
  actionName,
  onClick = () => { },
}) => {
  const { t } = useTranslation()
  return (
    <div
      className={cn(className, 'flex items-center h-6 space-x-1 text-primary-P1 cursor-pointer select-none')}
      onClick={onClick}>
      <div>
        {iconMap[type]}
      </div>
      <div className='text-sm leading-6 font-semibold text-primary-P1'>
        {actionName || t(`common.operation.${type}`)}
      </div>
    </div>
  )
}
export default React.memo(OperationBtn)
