'use client'
import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import WarningMask from '.'
import Button from '@/app/components/base/button'

export type IHasNotSetAPIProps = {
  isTrailFinished: boolean
  onSetting: () => void
}

const HasNotSetAPI: FC<IHasNotSetAPIProps> = ({
  isTrailFinished,
  onSetting,
}) => {
  const { t } = useTranslation()

  return (
    <WarningMask
      title={isTrailFinished ? t('appDebug.notSetAPIKey.trailFinished') : t('appDebug.notSetAPIKey.title')}
      description={t('appDebug.notSetAPIKey.description')}
      footer={
        <Button variant='primary' className='flex space-x-2' onClick={onSetting}>
          <span>{t('appDebug.notSetAPIKey.settingBtn')}</span>
        </Button>}
    />
  )
}
export default React.memo(HasNotSetAPI)
