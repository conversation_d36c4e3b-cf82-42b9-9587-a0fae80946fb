'use client'
import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import WarningMask from '.'
import Button from '@/app/components/base/button'

export type IFormattingChangedProps = {
  onConfirm: () => void
}

const FormattingChanged: FC<IFormattingChangedProps> = ({
  onConfirm,
}) => {
  const { t } = useTranslation()

  return (
    <WarningMask
      title={t('appDebug.feature.dataSet.queryVariable.unableToQueryDataSet')}
      description={t('appDebug.feature.dataSet.queryVariable.unableToQueryDataSetTip')}
      footer={
        <div className='flex space-x-4'>
          <Button variant='primary' onClick={onConfirm}>
            {t('appDebug.feature.dataSet.queryVariable.ok')}
          </Button>
        </div>
      }
    />
  )
}
export default React.memo(FormattingChanged)
