.mask {
  background: rgba(241, 247, 255, 0.90);
  backdrop-filter: blur(3px);
}
.wrap {
  @apply flex justify-start items-center flex-col w-1/2 h-full mx-auto;
}
.warningImg {
  @apply shrink-0 w-[160px] h-[130px] bg-no-repeat bg-center;
  background: url('/assets/warning.png');
  background-size: contain;
}
.icon {
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
}
.title {
  @apply text-gray-G1 mb-3;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}
.description {
  @apply text-gray-G2;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
}
.footer {
  @apply mt-6;
}