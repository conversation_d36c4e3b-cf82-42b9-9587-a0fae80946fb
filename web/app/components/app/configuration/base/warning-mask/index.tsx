'use client'
import type { FC } from 'react'
import React from 'react'
import s from './style.module.css'

export type IWarningMaskProps = {
  title: string
  description: string
  footer: React.ReactNode
  className?: string
}

const WarningMask: FC<IWarningMaskProps> = ({
  title,
  description,
  footer,
  className
}) => {
  return (
    <div className={`${s.mask} absolute z-10 inset-0 pt-[160px] ${className}`}
    >
      <div className={s.wrap}>
        <div className={s.warningImg}></div>
        <div className={s.title}>
          {title}
        </div>
        <div className={s.description}>
          {description}
        </div>
        <div className={s.footer}>
          {footer}
        </div>
      </div>

    </div>
  )
}
export default React.memo(WarningMask)
