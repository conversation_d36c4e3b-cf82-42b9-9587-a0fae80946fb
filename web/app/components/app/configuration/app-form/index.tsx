'use client'
import React, { forwardRef, useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Input } from 'antd'
import { debounce } from 'lodash-es'
import { getRandomAppHead } from '../../utils'
import { useProviderContext } from '@/context/provider-context'
import type { App, AppIconType } from '@/types/app'
import { MAX_APP_DESC_LENGTH, MAX_APP_NAME_LENGTH } from '@/config'
// 公共组件
import Avatar from '@/app/components/base/avatar'
import { secureRandom } from '@/utils'

export type AppFormProps = {
  className?: string
  app: App
  size?: number
  appName: string
  appDescription: string
  appIcon: string
  isEditModal?: boolean
  onConfirm: (info: {
    name: string
    icon_type: AppIconType
    icon: string
    icon_background?: string
    description: string
    use_icon_as_answer_icon?: boolean
  }) => Promise<void>
  blurToSave?: boolean
}

export type AppFormMethods = {
  submit: () => void
}

const AppForm = forwardRef<HTMLDivElement, AppFormProps>(
  (
    {
      className,
      app,
      size = 36,
      appIcon: _appIcon,
      appName,
      appDescription,
      isEditModal = false,
      onConfirm,
      blurToSave,
    }: AppFormProps,
    ref,
  ) => {
    const { t } = useTranslation()
    const { plan, enableBilling } = useProviderContext()
    const [form] = Form.useForm()
    const values = Form.useWatch([], form)
    const isAppsFull = (enableBilling && plan.usage.buildApps >= plan.total.buildApps)

    // 应用图标
    const initAppIcon = (app: App) => ({
      type: 'image',
      icon: app?.icon || app?.icon_url || getRandomAppHead(),
      url: app?.icon_url || app?.icon || getRandomAppHead(),
      fileId: secureRandom(),
    })
    const [appIcon, setAppIcon] = useState(initAppIcon(app))
    useEffect(() => {
      setAppIcon(initAppIcon(app))
    }, [app])

    // 表单提交动作
    const submit = useCallback((url?: string) => {
      const { name, description } = form.getFieldsValue()
      if (!name.trim()) {
        form.setFieldValue('name', appName)
        return
      }
      onConfirm({
        name,
        icon_type: 'image',
        icon: url || appIcon?.icon,
        icon_background: undefined,
        description,
        use_icon_as_answer_icon: false,
      })
    }, [appIcon, form])

    // 变更应用头像
    const changeAppHead = (value: string) => {
      setAppIcon({
        type: 'image',
        icon: value,
        url: value,
        fileId: secureRandom(),
      })
      if (blurToSave)
        submit(value)
    }

    // 是否可提交
    const [disabled, setDisabled] = useState(false)

    useEffect(() => {
      form.setFieldsValue({
        name: appName,
        description: appDescription,
      })
    }, [appDescription, appName, form])
    useEffect(debounce(() => {
      form.validateFields({ validateOnly: true }).then(() => {
        setDisabled(false || (!isEditModal && isAppsFull))
      },
      ).catch(() => {
        setDisabled(true)
      })
    }, 100), [form, values])
    return (
      <div ref={ref} className='flex items-start justify-start w-full space-x-3'>
        <Avatar
          avatar={appIcon.url}
          size={size}
          showUpload
          className='!rounded'
          onChange={changeAppHead}
        ></Avatar>
        <Form className={className} form={form} layout='vertical'>
          <Form.Item
            required
            validateTrigger='onBlur'
            label={t('app.info.appName')}
            name={'name'}
            className='w-full'
          >
            <Input
              placeholder={t('app.placeholder.appName') || ''}
              onBlur={blurToSave ? () => submit() : () => {}}
              className={`h-[${size}px] !bg-transparent !shadow-none`}
              autoComplete='off'
              maxLength={MAX_APP_NAME_LENGTH}
            />
          </Form.Item>
          <Form.Item
            name={'description'}
            label={t('app.info.appDescription')}
            rules={[
              { required: true, whitespace: true, max: MAX_APP_DESC_LENGTH },
            ]}
          >
            <Input.TextArea
              placeholder={t('app.placeholder.appDescription') || ''}
              onBlur={blurToSave ? () => submit() : () => {}}
              className='!bg-transparent !shadow-none !h-[80px]'
              autoSize={{ minRows: 3, maxRows: 3 }}
              maxLength={MAX_APP_DESC_LENGTH}
            ></Input.TextArea>
          </Form.Item>
        </Form>
      </div>
    )
  },
)

AppForm.displayName = 'AppForm'
export default AppForm
