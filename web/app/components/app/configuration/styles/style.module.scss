/* agent页面三分栏左侧白色背景 */
.linearBg {
  background: linear-gradient(103deg, rgba(255, 255, 255, 0.70) 0%, #FFF 100%);
}
/* flex 布局 */
// .flexHalf {
//   @apply relative flex flex-col w-1/2 h-full overflow-y-auto grow;
// }
// .flexOneThird {
//   @apply relative flex flex-col w-1/2 sm:w-1/3 h-full overflow-y-auto grow;
// }
.flexColGap20 {
  @apply flex flex-col gap-[20px];
}
.flexColGap8 {
  @apply flex flex-col gap-[8px];
}

.advancedPromptMode {
  position: relative;
}
.appModuleHeader {
  @apply flex justify-between items-center;
  padding: 0 24px;
  height: 56px;
  line-height: 56px;
}
.appConfigContent {
  @apply flex w-full;
  height: calc(100% - 57px);
}
.cardDivider {
  @apply border-gray-G6;
  min-width: calc(100% - 48px);
  width: calc(100% - 48px);
  margin: 0 24px;
}
.content {
  @apply px-3 md:px-6 py-[20px] w-full h-full;
}
.appDebugWrap {
  @apply flex flex-col h-full w-full grow;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.09) 0%, rgba(255, 255, 255, 0.09) 100%), linear-gradient(179deg, #F8F7F9E6 0.84%, #F0F5FFE6 103.39%);
}

.appConfig {
  @apply w-full space-y-2;
  :global {
    .ant-form-item-label {
      display: none !important;
    }
    .ant-form-item {
      margin-bottom: 0 !important;
    }
    .ant-input {
      // height: 40px;
    }
  }
}

.normalText {
  @apply text-gray-G1 text-[14px] font-normal leading-H3;
}

.iconBtnDefault {
  @apply w-4 h-4 text-gray-G3 hover:text-gray-G1 cursor-pointer;
}
.iconBtn {
  @apply w-4 h-4 text-gray-G3 hover:text-primary-P1 cursor-pointer;
}
.textBtn {
  @apply flex gap-1 items-center text-gray-G3 hover:text-primary-P1 cursor-pointer ;
}

.cardItemWrap {
 @apply relative flex justify-between items-center mb-2 last-of-type:mb-0 h-[40px] py-2 px-3 w-full rounded border border-gray-G5;
}

.InputDragging {
  filter: drop-shadow(0px 4px 6px rgba(222, 226, 234, 0.60));
}

.bgCardWrap {
  border-radius: 6px;
  border: 1px solid #FFF;
  padding: 20px;
  background: #FFFFFFCC;

  box-shadow: 0px 1px 2px 0px rgba(159, 176, 201, 0.20);
}

.debugDivider {
  width: 100%;
  height: 1px;
  background: rgba(222, 226, 234, 0.60);
}

.advancedPromptMode::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: -1px;
  width: 100%;
  height: 3px;
  background-color: rgba(68, 76, 231, 0.18);
  transform: skewX(-30deg);
}
.appConfigCardBg {
  background: linear-gradient(103deg, rgba(255, 255, 255, 0.70) 0%, #FFF 100%);
  background-image: url('/assets/icons/app/agentChatBg.jpg');
}

.resizeTrigger {
  @apply absolute flex justify-center w-3 cursor-col-resize resize-x h-full border-gray-G6 active:border-gray-G5;
}
.verticalDivider {
  @apply m-0 h-full border-gray-G6 active:border-gray-G5;
  border-color: inherit;
}
