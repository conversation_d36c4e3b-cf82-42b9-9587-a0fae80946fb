'use client'
import type { <PERSON> } from 'react'
import React from 'react'
import Image from 'next/image'
import { useTranslation } from 'react-i18next'

export type IAutomaticBtnProps = {
  onClick: () => void
}
const AutomaticBtn: FC<IAutomaticBtnProps> = ({
  onClick,
}) => {
  const { t } = useTranslation()

  return (
    <div className='flex space-x-1 items-center cursor-pointer text-primary-P1'
      onClick={onClick}
    >
      <Image src='/assets/icons/app/automatic.svg' width={16} height={16} alt='automatic' />
      <span className='title-14-24 !text-primary-P1'>{t('appDebug.operation.automatic')}</span>
    </div>
  )
}
export default React.memo(AutomaticBtn)
