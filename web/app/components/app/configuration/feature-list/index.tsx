'use client'
import React from 'react'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'

import cn from 'classnames'
import DatasetConfig from './dataset-config'
import AgentTools from './agent-tools'
import ConfigContext from '@/context/debug-configuration'
import style from '@/app/components/app/configuration/styles/style.module.scss'

import type { OnFeaturesChange } from '@/app/components/base/features/types'
import ConversationOpener from '@/app/components/base/features/new-feature-panel/conversation-opener'
import FollowUp from '@/app/components/base/features/new-feature-panel/follow-up'
import Citation from '@/app/components/base/features/new-feature-panel/citation'
import ChatBg from '@/app/components/base/features/new-feature-panel/chat-bg'
import NewFeaturePanel from '@/app/components/base/features/new-feature-panel'
import NewVoiceInput from '@/app/components/base/features/new-feature-panel/new-voice-input'
import NewVoiceConversation from '@/app/components/base/features/new-feature-panel/new-voice-conversation'
import FeatureCollapseGroup from '@/app/components/base/features/feature-collapse-group'
import Scrollbar from '@/app/components/base/scrollbar'
// import type { NewLanguageType, NewVoiceConfigType } from '@/models/app'
import { useSystemContext } from '@/context/system-context'
import { useProviderContext } from '@/context/provider-context'

type FeatureListProp = {
  isChatMode: boolean
  disabled?: boolean
  onChange?: OnFeaturesChange
}

// 自主规划显示的feature
const FeatureList = ({
  isChatMode,
  disabled,
  onChange,
}: FeatureListProp) => {
  const { t } = useTranslation()
  const { isPrivate } = useSystemContext()
  const {
    mode,
    isAgent,
    isVoiceCalling,
    voicesConfigData,
  } = useContext(ConfigContext)
  const { useXIYANRag } = useProviderContext()

  return (
    <>
      <Scrollbar className={cn(style.content, style.flexColGap20)}>
        <div className={cn('title-14-24')}>{t('appDebug.pageTitle.feature')}</div>
        {/* 知识 */}
        <FeatureCollapseGroup title={t('appDebug.featureCollapseGroup.title.dataSet')}>
          <DatasetConfig />
        </FeatureCollapseGroup>

        {/* 工具 */}
        <FeatureCollapseGroup title={t('appDebug.featureCollapseGroup.title.tools')}>
          {isAgent && (
            <AgentTools />
          )}
          <NewFeaturePanel
            inWorkflow={false}
            showFileUpload={false}
            isChatMode={mode !== 'completion'}
            disabled={false}
            onChange={onChange}
          />
        </FeatureCollapseGroup>

        {/* 对话 */}
        <FeatureCollapseGroup title={t('appDebug.featureCollapseGroup.title.conversation')}>
          {/* 对话开场白 */}
          {isChatMode && (
            <ConversationOpener
              disabled={disabled}
              onChange={onChange}
            />
          )}
          {/* 追问 下一步问题与建议 */}
          {isChatMode && (
            <FollowUp disabled={disabled} onChange={onChange} />
          )}
          {/* 引用和归属 */}
          {!useXIYANRag && isChatMode && (
            <Citation disabled={disabled} onChange={onChange} />
          )}
          {/* 会话背景图 */}
          {isChatMode && (
            <ChatBg disabled={disabled} onChange={onChange}></ChatBg>
          )}
          {/* </FeatureCollapseGroup> */}
          {/* 自主规划-语音 */}
          {/* <FeatureCollapseGroup title={t('appDebug.featureCollapseGroup.title.voice')}> */}
          {/* 语音输入 */}
          {isChatMode && !isPrivate && (
            <NewVoiceInput
              disabled={disabled}
              onChange={onChange}
              applyType="selfPlanning"
              isAutoPlay={false}
              voicesConfigData={voicesConfigData}
            />
          )}
          {/* 语音对话 */}
          {isChatMode && !isPrivate && (
            <NewVoiceConversation
              disabled={disabled || isVoiceCalling}
              onChange={onChange}
              applyType = "selfPlanning"
              voicesConfigData={voicesConfigData}
            />
          )}
        </FeatureCollapseGroup>
      </Scrollbar>
    </>
  )
}
export default React.memo(FeatureList)
