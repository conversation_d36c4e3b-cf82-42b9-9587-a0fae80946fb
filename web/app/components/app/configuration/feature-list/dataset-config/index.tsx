import type { FC } from 'react'
import React, { useState } from 'react'
import { useContext } from 'use-context-selector'
import { useFormattingChangedDispatcher } from '../../debug/hooks'
import type { DataSet } from '@/models/datasets'
// 公共能力
import ConfigContext from '@/context/debug-configuration'
import SettingsModal from '@/app/components/datasets/common/add-dataset-panel/setting-modal'
import { AddDatasetPanelConetnt } from '@/app/components/datasets/common/add-dataset-panel'

const DatasetConfig: FC = () => {
  const {
    datasetConfigs,
    setDatasetConfigs,
    dataSets,
    setDataSets,
    forkDatasets,
    showSelectDataSet,
  } = useContext(ConfigContext)
  const formattingChangedDispatcher = useFormattingChangedDispatcher()
  // 是否显示设置知识库弹窗
  const [showSettingDataSet, setShowSettingDataSet] = useState(false)
  // 选中的知识库
  const [selectDataset, setSelectDataset] = useState<DataSet>()

  // 删除知识库
  const onRemove = (id: string) => {
    setDataSets(dataSets.filter(item => item.id !== id))
    // @ts-expect-error 期望的类型
    setDatasetConfigs({ ...datasetConfigs, datasets: { datasets: datasetConfigs.datasets?.datasets.filter(item => item.id !== id) } }) // 同步删除dat
    formattingChangedDispatcher()
  }
  // 编辑知识库列表
  const handleSave = (newDataset: DataSet) => {
    const index = dataSets.findIndex(item => item.id === newDataset.id)
    const newDatasets = [...dataSets.slice(0, index), newDataset, ...dataSets.slice(index + 1)]
    setDataSets(newDatasets)
    setShowSettingDataSet(false)
    formattingChangedDispatcher()
  }
  // 调整知识库fork
  const handleOnDatasetsChange = () => {
    setDatasetConfigs({ ...datasetConfigs, fork: false })
  }

  return (
    <>
      <AddDatasetPanelConetnt
        dataSets={dataSets}
        forkDatasets={forkDatasets}
        config={datasetConfigs}
        scene='app'
        onRemove={onRemove}
        onConfig={setDatasetConfigs}
        onAdd={showSelectDataSet}
        onCloseFock={() => handleOnDatasetsChange()}
        onSetting={(value) => {
          setSelectDataset(value)
          setShowSettingDataSet(true)
        }}
        isInApp={true}
      ></AddDatasetPanelConetnt>
      {/* 编辑知识库弹窗 */}
      {
        showSettingDataSet && <SettingsModal
          onSave={handleSave}
          onCancel={() => setShowSettingDataSet(false)}
          isShowSettingsModal={true}
          currentDataset={selectDataset!}
        ></SettingsModal>
      }
    </>
  )
}
export default React.memo(DatasetConfig)
