import React, { useEffect, useMemo, useState } from 'react'
import { Modal } from 'antd'
import s from './style.module.css'
import Chat from '@/app/components/base/chat/optimize-chat/index'
import { useFeatures } from '@/app/components/base/features/hooks'
import { useChat } from '@/app/components/base/chat/pref-chat/hooks'
import { useStore as useAppStore } from '@/app/components/app/store'
import { useDebugConfigurationContext } from '@/context/debug-configuration'
import {
  useConfigFromDebugContext,
} from '@/app/components/app/configuration/debug/hooks'
import type { ChatConfig } from '@/types/chat'

type OptimizeModalProps = {
  isShow: boolean
  onClose: () => void
  setReplacePromptWord: (newWord: string) => void
  modalSource: string
  promptTemplate: string
  model?: {
    provider?: string
    name?: string
    mode?: string
    completion_params?: Record<string, any>
  }
}
const OptimizeModal: React.FC<OptimizeModalProps> = ({ isShow, onClose, setReplacePromptWord, modalSource, promptTemplate,model }) => {
  const [isShowGenerate, setIsShowGenerate] = useState(true)// 自动生成按钮显隐
  const features = useFeatures(s => s.features)
  const appDetail = useAppStore(state => state.appDetail)
  const configTemplate = useConfigFromDebugContext()
  const {
    modelConfig,
    appId,
    completionParams,
  } = useDebugConfigurationContext()
  // 对话配置
  const config = useMemo(() => {
    return {
      ...configTemplate,
      more_like_this: features.moreLikeThis,
      opening_statement: features.opening?.enabled ? (features.opening?.opening_statement || '') : '',
      suggested_questions: features.opening?.enabled ? (features.opening?.suggested_questions || []) : [],
      sensitive_word_avoidance: features.moderation,
      speech_to_text: features.speech2text,
      text_to_speech: features.text2speech,
      file_upload: features.file,
      suggested_questions_after_answer: features.suggested,
      retriever_resource: features.citation,
      annotation_reply: features.annotationReply,
      supportFeedback: true,
    } as unknown as ChatConfig
  }, [configTemplate, features])
  const {
    chatList,
    isResponding,
    currentChatList,
    conversationId,
    setCurrentChatList,
    setChatList,
    handleSend,
    handleStop,
  } = useChat(
    config,
  )
  console.log('appDetail', appDetail)
  console.log('chatListGGGGG', chatList)
  console.log("conversationId22222",conversationId)
  console.log('currentChatListFFFFF', currentChatList)
  // 监听弹窗状态变化，当弹窗关闭时重置isShowGenerate为true
  useEffect(() => {
    if (!isShow) {
      setIsShowGenerate(true)
      setCurrentChatList([]) // 弹窗关闭时清空当前对话列表
      setChatList([]) // 弹窗关闭时清空对话列表
    }
  }, [isShow])
  // 发送对话信息
  const doSend = (message = '',...args: any) => {
    console.log("conversationId11111",args,args.length,args[0])
    setIsShowGenerate(false)
    const configData = { // 跟后端沟通暂时写死
      provider: modalSource==='auto_planning'?modelConfig.provider:model?.provider,
      name: modalSource==='auto_planning'?modelConfig.model_id:model?.name,
      mode: modalSource==='auto_planning'?modelConfig.mode:model?.mode,
      completion_params: modalSource==='auto_planning'?completionParams:model?.completion_params,
      // provider: 'tong_yi',
      // name: 'qwen2.5-72b-instruct',
      // mode: 'completion',
      // completion_params: {},
    }
    const data: any = {
      conversation_id:args[0]===true?conversationId:'',
      query: message, // 优化的内容（输入框值）
      // model_config: configData, // 模型配置
      model_config: {}, // 模型配置
      pre_prompt: promptTemplate, // 用户填写的角色提示词
      app_id: appDetail?.id, // 应用ID
      app_name: appDetail?.name, // 应用名称
      app_description: appDetail?.description, // 应用描述
      from_source: modalSource, // 调用提示词位置
    }
    handleSend(
      '/prompt-generate',
      data,
      {
        // onGetConversationMessages: (conversationId, getAbortController) => fetchConversationMessages(appId, conversationId, getAbortController),
        // onGetSuggestedQuestions: (responseItemId, getAbortController) => fetchSuggestedQuestions(appId, responseItemId, getAbortController),
      },
    )
  }
  return (
    <div className={s.optimizeModalContainer}>
      <Modal
        closable={false}
        centered={true}
        open={isShow}
        onCancel={onClose}
        footer={null}
        width={480}
        getContainer={false}
        style={{ padding: '0px', margin: '0px', minHeight: '130px' }}
      >
        <div style={{ padding: '16px', position: 'relative', background: '#fff', maxHeight: 'calc(90vh - 32px)', overflowY: 'auto' }}>
          <Chat
            currentChatList ={currentChatList}
            chatList={chatList}
            onCancel={onClose} // 取消按钮事件
            setReplacePromptWord={setReplacePromptWord} // 替换按钮事件
            onSend={doSend}
            isResponding={isResponding}
            onStopResponding={handleStop}
            isShowGenerate={isShowGenerate} // 自动生成按钮显隐
          />
        </div>
      </Modal>
    </div>
  )
}

export default OptimizeModal
