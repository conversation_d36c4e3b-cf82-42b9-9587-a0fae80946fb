'use client'
import type { FC } from 'react'
import React, { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import { DatePicker, Select } from 'antd'

import { statusEnum } from './type'
import type { QueryParam } from './index'

// 公共组件
import SearchInput from '@/app/components/base/input/search-input'

const { RangePicker } = DatePicker
dayjs.extend(quarterOfYear)

type IFilterProps = {
  queryParams: QueryParam
  setQueryParams: (v: QueryParam) => void
}

const Filter: FC<IFilterProps> = ({ queryParams, setQueryParams }: IFilterProps) => {
  const { t } = useTranslation()

  const onRangeChange = (dates: any, dateStrings: any) => {
    if (dates) {
      setQueryParams({ ...queryParams, start: dayjs(dateStrings[0]).startOf('day').format('YYYY-MM-DD HH:mm'), end: dayjs(dateStrings[1]).endOf('day').format('YYYY-MM-DD HH:mm') })
    }
    else {
      const { start, end, ...props } = queryParams
      setQueryParams(props)
    }
  }
  const changeStatus = useCallback((value: string) => {
    setQueryParams({
      ...queryParams,
      status: value,
    })
  }, [queryParams])
  const changeDatasetType = useCallback((value: string) => {
    setQueryParams({
      ...queryParams,
      dataset_type: value,
    })
  }, [queryParams])
  // 赞踩情况列选项
  const statusOptions = useMemo(() => {
    return [
      { value: '', label: t('appDataReflux.filter.status.all') },
      { value: statusEnum.Perform, label: t('appDataReflux.filter.status.perform') },
      { value: statusEnum.Normal, label: t('appDataReflux.filter.status.normal') },
      { value: statusEnum.Failed, label: t('appDataReflux.filter.status.failed') },
    ]
  }, [])
  const datasetTypeOptions = useMemo(() => {
    return [
      { value: '', label: t('appLog.filter.datasetType.all') },
      { value: 'LLM-SFT', label: `${t('appLog.filter.datasetType.text')}-SFT` },
      { value: 'LLM-CoT', label: `${t('appLog.filter.datasetType.text')}-CoT` },
      { value: 'LLM-FC', label: `${t('appLog.filter.datasetType.text')}-FC` },
      { value: 'VLM-SFT', label: `${t('appLog.filter.datasetType.multimodal')}-SFT` },
      { value: 'VLM-CoT', label: `${t('appLog.filter.datasetType.multimodal')}-CoT` },
      { value: 'VLM-FC', label: `${t('appLog.filter.datasetType.multimodal')}-FC` },
    ]
  }, [])
  const today = new Date()
  return (
    <div className='flex justify-between items-center mt-1 mb-4'>
      <div className='flex gap-4 items-center text-gray-G1'>
        {/* 日志时间 */}
        <RangePicker
          onChange={onRangeChange}
          maxDate={dayjs(today)}
        />
        {/* 数据状态 */}
        <Select
          className='w-[120px]'
          defaultValue={''}
          options={statusOptions.map(item => ({ value: item.value, label: item.label }))}
          placeholder={t('appLog.filter.likeOrDislike.title')}
          onChange={value => changeStatus(value)}
        />
        {/* 关键词 */}
        <SearchInput
          className='w-[250px]'
          placeholder={t('common.operation.search')!}
          value={queryParams.keyword}
          onChange={(value: string) => {
            setQueryParams({ ...queryParams, keyword: value })
          }}
        ></SearchInput>
        {/* 数据状态 */}
        <Select
          className='w-[120px]'
          options={datasetTypeOptions}
          placeholder={t('appLog.filter.datasetType.title')}
          onChange={value => changeDatasetType(value)}
        />
      </div>
    </div>
  )
}

export default Filter
