'use client'
import { use<PERSON>outer } from 'next/navigation'
import React, { useC<PERSON>back, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import type { TableColumnsType } from 'antd'

// 组件
import { Table } from 'antd'
import Indicator from '../../base/indicator'
import type { IndicatorProps } from '../../base/indicator'
import TextButton from '../../base/button/text-button'
import Confirm from '../../base/confirm'
import Tooltip from '../../base/tooltip'
import { datasetTypeList } from './type'
import type { DataReflux, DataRefluxListResponse, statusEnum } from './type'
import type { QueryParam } from './index'
import { API_PREFIX } from '@/config'
import { ToastContext } from '@/app/components/base/toast'
import { generateMicroPath } from '@/utils/micro'
import { deleteDatasetReflux, getDatasetRefluxStatus, retryDatasetReflux } from '@/service/data-reflux'
import useTimestamp from '@/hooks/use-timestamp'
import { PromptRedIcon } from '@/app/components/base/icons/src/public/workflow'
import ShowNodeInf from '@/app/components/app/log/showNodeInf'

type props = {
  queryParams: QueryParam
  setQueryParams: (v: QueryParam) => void
  datasetList: DataRefluxListResponse
  loading: boolean
  onRefresh: () => void
  appId?: string
  mode?: 'agent-chat' | 'advanced-chat' | 'workflow'
}
// 对话列表
const ConversationList = ({ queryParams, setQueryParams, datasetList, loading, onRefresh, appId = '', mode = '' }: props) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const router = useRouter()
  const { formatTime } = useTimestamp()
  const defaultValue = '-'

  // 在ConversationList组件内部添加状态
  const [isNodeModalOpen, setIsNodeModalOpen] = useState(false)
  const [selectedDataset, setSelectedDataset] = useState<DataReflux | null>(null)

  const statusMap = useMemo(() => {
    const map: { [key in statusEnum]: { color: IndicatorProps['color']; text: string } } = {
      perform: { color: 'blue', text: t('appDataReflux.filter.status.perform') },
      normal: { color: 'green', text: t('appDataReflux.filter.status.normal') },
      failed: { color: 'red', text: t('appDataReflux.filter.status.failed') },
    }
    return map
  }, [])

  const statusPrompttext = {
    content: t('appDataReflux.table.statusPrompt.content'),
    standard: t('appDataReflux.table.statusPrompt.standard'), // 平台格式规范
    subContent: t('appDataReflux.table.statusPrompt.subContent'),
    downloadAbnormalData: t('appDataReflux.table.statusPrompt.downloadAbnormalData'), // 下载异常数据
    downloadTip: t('appDataReflux.table.statusPrompt.downloadTip'),
    downloadTipTwo: t('appDataReflux.table.statusPrompt.downloadTipTwo'),
  }

  // 表格列
  const columns: TableColumnsType<any> = [
    // 数据集英文名称
    {
      title: t('appDataReflux.table.header.datasetName'),
      key: 'datasetName',
      render: (_: any, dataset) => {
        return dataset.dataset_name || defaultValue
      },
      width: 150,
      ellipsis: true,
    },
    // 数据集别名
    {
      title: t('appDataReflux.table.header.datasetAlias'),
      key: 'datasetAlias',
      render: (_: any, dataset) => {
        if (mode === 'advanced-chat') {
          return (
            <TextButton onClick={() => handleDetail(dataset.dataset_uuid)}>
              {dataset.dataset_alias || defaultValue}
            </TextButton>
          )
        }
        else {
          return dataset.dataset_alias || defaultValue
        }
      },
      width: 150,
      ellipsis: true,
    },
    // 数据集简介
    {
      title: t('appDataReflux.table.header.datasetDesc'),
      key: 'datasetDesc',
      render: (_: any, dataset) => {
        return dataset.dataset_desc || defaultValue
      },
      width: 150,
      ellipsis: true,
    },
    // 数据类型
    {
      title: t('appDataReflux.table.header.datasetType'),
      key: 'datasetType',
      render: (_: any, dataset) => {
        return datasetTypeList.find(item => item.value === dataset.dataset_type)?.label || '-'
      },
      width: 150,
      ellipsis: true,
    },
    // 创建时间
    {
      title: t('appDataReflux.table.header.time'),
      key: 'time',
      render: (_: any, dataset) => {
        return formatTime(dataset.created_at, t('common.dateFormat.dateTime') as string)
      },
      sorter: true,
      width: 190,
    },
    // 状态
    {
      title: t('appDataReflux.table.header.status'),
      key: 'status',
      render: (_: any, dataset) => {
        // mode === 'advanced-chat' 工作流；
        return (
          <Indicator
            color={statusMap[dataset.status as statusEnum]?.color}
            textClassName='text-S3 leading-H3'
          >
            <div className='flex items-center'>
              {statusMap[dataset.status as statusEnum]?.text || dataset.status}
              {/* 错误信息 */}
              {/* {
                dataset.exception && (
                  <Tooltip
                    popupContent={
                      <div className='max-w-[260px] break-all'>{dataset.exception}</div>
                    }
                    triggerClassName='ml-1 w-4 h-4'
                  />
                )
              } */}
              {statusCol(dataset)}
            </div>
          </Indicator>
        )
      },
      width: 100,
      ellipsis: true,
    },
    // 操作
    {
      title: t('appDataReflux.table.header.action'),
      render: (_: any, dataset) => (
        <div className='flex gap-6'>
          <TextButton size='middle' onClick={() => handleRetry(dataset.dataset_uuid)} disabled={checkDisabled(dataset, 'retry')}>{t('common.operation.retry')}</TextButton>
          <TextButton size='middle' onClick={() => handleDelete(dataset.dataset_uuid)} disabled={checkDisabled(dataset, 'delete')}>{t('common.operation.delete')}</TextButton>
          {mode === 'advanced-chat'
            ? (
              <TextButton size='middle' onClick={() => handleAssociationNode(dataset.dataset_uuid)} disabled={checkDisabled(dataset, 'associationNode')}>{t('appDataReflux.action.associationNode')}</TextButton>
            )
            : (
              <TextButton size='middle' onClick={() => handleDetail(dataset.dataset_uuid)} disabled={checkDisabled(dataset, 'detail')}>{t('appDataReflux.action.detail')}</TextButton>
            )
          }
        </div>
      ),
      key: 'operation',
      align: 'left',
      width: 200,
    },
  ]

  // 是否显示确认删除
  const [showConfirmDelete, setShowConfirmDelete] = useState(false)
  const [deleteId, setDeleteId] = useState('')
  // 重试
  const handleRetry = useCallback(async (id: string) => {
    try {
      await retryDatasetReflux({ id }, appId)
      onRefresh()
    }
    catch (e) {
    }
  }, [])
  // 显示删除确认框
  const handleDelete = useCallback(async (id: string) => {
    setShowConfirmDelete(true)
    setDeleteId(id)
  }, [])
  // 确认删除
  const onConfirmDelete = useCallback(async () => {
    if (!deleteId)
      return
    try {
      setShowConfirmDelete(false)
      await deleteDatasetReflux({ id: deleteId })
      notify({ type: 'success', message: t('common.actionMsg.deleteSuccessfully') })
      onRefresh()
    }
    catch (e) {
    }
  }, [deleteId, deleteDatasetReflux])
  // 详情，跳转到训推数据集详情页
  const handleDetail = useCallback(async (id: string) => {
    try {
      const res = await getDatasetRefluxStatus({ id })
      console.log(res, 'handleDetail')
      if (res?.result === 'true') {
        const url = `myDatasets/dataDetail?datasetId=${res?.datasetId}&versionId=${res?.versionId}`
        window.open(generateMicroPath(url), '_blank', 'noopener')
      }
      else {
        notify({ type: 'error', message: res.msg || t('appDataReflux.tip.noData') })
        onRefresh()
      }
    }
    catch (e) {
      // console.log(e)
    }
  }, [])
  // 判断按钮是否禁用
  const checkDisabled = useCallback((dataset: DataReflux, action: string) => {
    //  工作流=》dataset_path为空可重试
    if (action === 'retry') { // mode === 'advanced-chat' && 
      if (dataset.status === 'failed' && !dataset.dataset_path)
        return false

      else
        return true
    }
    // // 重试 yes: 失败, no: 进行中、成功
    // if (mode === 'agent-chat' && action === 'retry') // 自主规划
    //   return dataset.status !== 'failed'

    // 删除 yes：成功、失败，no：进行中
    if (action === 'delete')
      return dataset.status === 'perform'

    // 详情 yes：成功，no：失败、进行中
    if (action === 'detail')
      return dataset.status !== 'normal'

    // 关联节点
    if (action === 'associationNode') {
      // TODO：是否禁用
    }
  }, [])
  // 排序
  const handleTableChange = useCallback((pagination: any, filters: any, sorter: any) => {
    setQueryParams({
      ...queryParams,
      sort_by: sorter.order === 'ascend' ? '-created_at' : 'created_at',
    })
  }, [])

  // 平台格式规范
  const handleStandard = () => {
    const url = 'https://k36drdpyul.feishu.cn/docx/B6htdYzRuovxiyxBIA0c2PlknPN#RgAQdp8UvoSZN0xne8pcWVMbnzG'
    window.open(url, '_blank', 'noopener')
  }

  const headers = () => {
    const token = localStorage.getItem('console_token') || ''
    const perToken = localStorage.getItem('sso_token')
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      'opertoken': perToken,
    }
  }
  const Export = async (url: string, type: string, title: string) => {
    fetch(url, {
      method: 'GET',
      headers: headers(),
    })
      .then((response) => {
        // 检查响应状态
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`)
        // 转换为 Blob 并下载文件
        response.blob().then((blob) => {
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `${title}.${type}` // 使用提取的文件名
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          URL.revokeObjectURL(url)
        })
      })
      .catch((error) => {
        console.error(t('sampleTemplate.longText.loadingerror'), error)
      })
  }

  // 下载异常数据
  const handleDownloadAbnormalData = async (dataset: DataReflux) => {
    try {
      const id = dataset.dataset_uuid || ''
      const url = `${API_PREFIX}/dataset_errorfile/download?dataset_uuid=${id}`
      const title = `异常文件${Date.now()}`
      Export(url, 'xlsx', title)
      onRefresh()
    }
    catch (e) {
      console.log(e)
    }
  }

  const statusCol = (dataset: DataReflux) => {
    if (dataset?.dataset_has_error) { // && mode === 'advanced-chat' && 
      // 下载异常数据
      return (
        <>
          &nbsp;
          <Tooltip
            popupContent={
              <div className='max-w-[260px] break-all'>
                {statusPrompttext.content}
                <TextButton onClick={ handleStandard }>{statusPrompttext.standard}</TextButton>
                {statusPrompttext.subContent} &nbsp;
                {/* {statusPrompttext.downloadTip} */}
                <TextButton onClick={ () => handleDownloadAbnormalData(dataset) }>{statusPrompttext.downloadAbnormalData} </TextButton>
                {/* {statusPrompttext.downloadTipTwo} */}
              </div>
            }
            triggerClassName='ml-1 w-4 h-4'
          >
            <PromptRedIcon className='w-4 h-4' />
          </Tooltip>
        </>
      )
    }
    else {
      if (dataset.exception) { //  && mode !== 'advanced-chat'
        // 错误信息
        return (
          <Tooltip
            popupContent={
              <div className='max-w-[260px] break-all'>{dataset.exception}</div>
            }
            triggerClassName='ml-1 w-4 h-4'
          />
        )
      }
    }
  }

  // 关联节点
  const handleAssociationNode = useCallback((dataset: DataReflux) => {
    // TODO：弹窗
    setSelectedDataset(dataset) // 设置当前选中的数据
    setIsNodeModalOpen(true) // 打开弹窗
  }, [])

  return (
    <>
      <Table
        size='middle'
        loading={loading}
        columns={columns}
        pagination={false}
        scroll={{ y: 'calc(100vh - 285px)' }}
        rowKey='id'
        dataSource={datasetList?.data || []}
        className='border-gray-G5 rounded border'
        rowClassName='cursor-pointer'
        onChange={handleTableChange}
      ></Table>
      <Confirm
        title={t('appDataReflux.confirm.deleteTitle')}
        content={t('appDataReflux.confirm.deleteTip')}
        isShow={showConfirmDelete}
        onCancel={() => setShowConfirmDelete(false)}
        onConfirm={onConfirmDelete}
      ></Confirm>
      {isNodeModalOpen && (
        <ShowNodeInf
          isOpen={true}
          onClose={() => {
            setIsNodeModalOpen(false)
            setSelectedDataset(null) // 可选：清空数据
          }}
          appID = {appId}
          rowData={selectedDataset} // 直接传递数据
        />
      )}
    </>
  )
}

export default ConversationList
