// 添加缺失的import语句
import { t } from 'i18next';


export enum statusEnum {
  Perform = 'perform',
  Normal = 'normal',
  Failed = 'failed',
}

export const datasetTypeList = [
  { label: `${t('appLog.filter.datasetType.text')}-SFT`, value: 'LLM-SFT' },
  { label: `${t('appLog.filter.datasetType.text')}-CoT`, value: 'LLM-CoT' },
  { label: `${t('appLog.filter.datasetType.text')}-FC`, value: 'LLM-FC' },
  { label: `${t('appLog.filter.datasetType.multimodal')}-SFT`, value: 'VLM-SFT' },
  { label: `${t('appLog.filter.datasetType.multimodal')}-CoT`, value: 'VLM-CoT' },
  { label: `${t('appLog.filter.datasetType.multimodal')}-FC`, value: 'VLM-FC' },
]

export const datasetTypeOptions = [
  { label: t('appLog.filter.datasetType.all'), value: '' },
  ...datasetTypeList
]

export const datasetTypeOptions_ZZ = [
  { label: t('appLog.filter.datasetType.all'), value: '' },
  { label: `${t('appLog.filter.datasetType.text')}-CoT`, value: 'LLM-CoT' },
  { label: `${t('appLog.filter.datasetType.text')}-FC`, value: 'LLM-FC' },
  { label: `${t('appLog.filter.datasetType.multimodal')}-CoT`, value: 'VLM-CoT' },
  { label: `${t('appLog.filter.datasetType.multimodal')}-FC`, value: 'VLM-FC' },
]

export type DataReflux = {
  id: string
  app_id: string
  dataset_alias: string
  dataset_desc: string
  dataset_name: string
  dataset_uuid: string
  status: statusEnum
  user_id: string
  created_at: number
  exception?: string
  dataset_has_error?: boolean
  dataset_path?: string
}

export type DataRefluxListResponse = {
  data: Array<DataReflux>
  has_more: boolean
  limit: number
  total: number
  page: number
}
