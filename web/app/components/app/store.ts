import { create } from 'zustand'
import type { App, AppSSO } from '@/types/app'
import type { IChatItem } from '@/types/chat'
type State = {
  appDetail?: App & Partial<AppSSO>
  appPublishInfo?: any
  currentLogItem?: IChatItem
  currentLogModalActiveTab: string
  showPromptLogModal: boolean
  showAgentLogModal: boolean
  showMessageLogModal: boolean
  toolPublishInfo?: string
  toolMode?: string
}

type Action = {
  setAppDetail: (appDetail?: App & Partial<AppSSO>) => void
  setAppPublishInfo: (appPublishInfo?: any) => void
  setCurrentLogItem: (item?: IChatItem) => void
  setCurrentLogModalActiveTab: (tab: string) => void
  setShowPromptLogModal: (showPromptLogModal: boolean) => void
  setShowAgentLogModal: (showAgentLogModal: boolean) => void
  setShowMessageLogModal: (showMessageLogModal: boolean) => void
  setToolPublishInfo: (toolPublishInfo?: string) => void
  setToolmode: (Toolmode?: string) => void
}

export const useStore = create<State & Action>(set => ({
  appDetail: undefined,
  setAppDetail: appDetail => set(() => ({ appDetail })),
  appPublishInfo: undefined,
  setAppPublishInfo: appPublishInfo => set(() => ({ appPublishInfo })),
  currentLogItem: undefined,
  currentLogModalActiveTab: 'DETAIL',
  setCurrentLogItem: currentLogItem => set(() => ({ currentLogItem })),
  setCurrentLogModalActiveTab: currentLogModalActiveTab => set(() => ({ currentLogModalActiveTab })),
  showPromptLogModal: false,
  setShowPromptLogModal: showPromptLogModal => set(() => ({ showPromptLogModal })),
  showAgentLogModal: false,
  setShowAgentLogModal: showAgentLogModal => set(() => ({ showAgentLogModal })),
  showMessageLogModal: false,
  setShowMessageLogModal: showMessageLogModal => set(() => {
    if (showMessageLogModal) {
      return { showMessageLogModal }
    }
    else {
      return {
        showMessageLogModal,
        currentLogModalActiveTab: 'DETAIL',
      }
    }
  }),
  toolMode: '',
  setToolmode: toolMode => set(state => ({ ...state, toolMode })),
  setToolPublishInfo: toolPublishInfo => set(() => ({ toolPublishInfo })),
}))
