import {
  memo,
  useMemo,
  useState,
} from 'react'
import { Popover } from 'antd'
import { useTranslation } from 'react-i18next'
import { RiUploadCloud2Line } from '@remixicon/react'
import FileInput from '../file-input'
import { useFile } from '../hooks'
import { useStore } from '../store'
import s from './style.module.css'
// 公共组件
import { FILE_URL_REGEX } from '@/config/file'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import type { FileUpload } from '@/app/components/base/features/types'
import cn from '@/utils/classnames'

type FileFromLinkOrLocalProps = {
  showFromLink?: boolean
  showFromLocal?: boolean
  trigger?: React.ReactNode
  fileConfig: FileUpload
}
const FileFromLinkOrLocal = ({
  showFromLink = true,
  showFromLocal = true,
  trigger,
  fileConfig,
}: FileFromLinkOrLocalProps) => {
  const { t } = useTranslation()
  const files = useStore(s => s.files)
  const { handleLoadFileFromLink } = useFile(fileConfig)

  const [isModalOpen, setIsModalOpen] = useState(false)

  // 当前连接地址
  const [url, setUrl] = useState('')
  // 是否显示错误
  const [showError, setShowError] = useState(false)

  // 是否禁用上传
  const disabled = !!fileConfig.number_limits && files.length >= fileConfig.number_limits
  // 上传方式
  const uploadItems = useMemo(() => {
    const items = []
    if (showFromLocal) {
      items.push({
        type: 'img',
        icon: '/assets/icons/chat/img.png',
        text: t('common.fileUploader.img'),
        props: { fileConfig, isOnlyImage: true },
      })
    }
    if (showFromLink) {
      items.push({
        type: 'link',
        icon: '/assets/icons/chat/link.png',
        text: t('common.fileUploader.link'),
      })
    }
    if (showFromLocal) {
      items.push({
        type: 'file',
        icon: '/assets/icons/chat/file.png',
        text: t('common.fileUploader.file'),
        props: { fileConfig },
      })
    }
    return items
  }, [fileConfig, showFromLink, showFromLocal, t])

  // 保存url变更
  const handleSaveUrl = () => {
    if (!url)
      return

    if (!FILE_URL_REGEX.test(url)) {
      setShowError(true)
      return
    }
    handleLoadFileFromLink(url)
    setUrl('')
  }
  // 显示添加链接modal
  const showModal = (type: string) => {
    if (type !== 'link' || !showFromLink)
      return
    setIsModalOpen(true)
  }
  // 关闭弹窗
  const handleCancel = () => {
    setIsModalOpen(false)
  }

  if (!trigger) {
    return (
      <>
        {
          showFromLocal && (<div className={s['mobile-uploader-content']}>
            {uploadItems.map((item, index) => (
              <div key={index} style={{ position: 'relative' }}>
                <div className={s['uploader-item']} onClick={() => showModal(item.type)}>
                  <img className={'w-6 h-6'} src={item.icon} alt={item.text} />
                </div>
                <div className={s['uploader-item-word']}>{item.text}</div>
                {item.props && <FileInput {...item.props} />}
              </div>
            ))}
          </div>)
        }
        {/* 添加链接弹窗 */}
        {isModalOpen && <Modal
          isShow={isModalOpen}
          title={t('common.fileUploader.link')}
          footer={
            <>
              <Button variant='secondary-accent' className='mr-4' onClick={handleCancel}>{t('common.operation.cancel')}</Button>
              <Button
                disabled={!url || disabled}
                variant='primary'
                onClick={handleSaveUrl}
              >
                {t('common.operation.save')}
              </Button>
            </>
          }
        >
          <div className='h-[51px]'>
            <div className={cn(
              'flex items-center w-full p-1 h-8 bg-white border border-primary-P2 rounded shadow-xs',
              showError && 'border-red-R2',
            )}>
              <input
                className='grow block mr-0.5 px-1 bg-transparent system-sm-regular outline-none appearance-none'
                placeholder={t('common.fileUploader.pasteFileLinkInputPlaceholder') || ''}
                value={url}
                onChange={(e) => {
                  setShowError(false)
                  setUrl(e.target.value)
                }}
                disabled={disabled}
              />
            </div>
            {
              showError && (
                <div className='mt-0.5 body-xs-regular text-red-R2'>
                  {t('common.fileUploader.pasteFileLinkInvalid')}
                </div>
              )
            }
          </div>
        </Modal>}
      </>
    )
  }

  return (
    <Popover
      placement='top'
      trigger={'click'}
      content={
        <div className={s['uploader-content']}>
          {/* 通过连接上传 */}
          {
            showFromLink && (
              <>
                <div className={cn(
                  'flex items-center p-1 h-8 bg-white border border-primary-P2 rounded shadow-xs',
                  showError && 'border-red-R2',
                )}>
                  <input
                    className='grow block mr-0.5 px-1 bg-transparent system-sm-regular outline-none appearance-none'
                    placeholder={t('common.fileUploader.pasteFileLinkInputPlaceholder') || ''}
                    value={url}
                    onChange={(e) => {
                      setShowError(false)
                      setUrl(e.target.value)
                    }}
                    disabled={disabled}
                  />
                  <Button
                    className='shrink-0'
                    size='small'
                    variant='primary'
                    disabled={!url || disabled}
                    onClick={handleSaveUrl}
                  >
                    {t('common.operation.ok')}
                  </Button>
                </div>
                {
                  showError && (
                    <div className='mt-0.5 body-xs-regular text-red-R2'>
                      {t('common.fileUploader.pasteFileLinkInvalid')}
                    </div>
                  )
                }
              </>
            )
          }
          {/* 分隔符 */}
          {
            showFromLink && showFromLocal && (
              <div className='flex items-center p-2 h-7 system-2xs-medium-uppercase text-gray-G2'>
                <div className='mr-2 w-[93px] h-[1px] bg-gradient-to-l from-[rgba(16,24,40,0.08)]' />
                OR
                <div className='ml-2 w-[93px] h-[1px] bg-gradient-to-r from-[rgba(16,24,40,0.08)]' />
              </div>
            )
          }
          {/* 从本地上传 */}
          {
            showFromLocal && (
              <Button
                className='relative w-full'
                variant='secondary-accent'
                disabled={disabled}
              >
                <RiUploadCloud2Line className='mr-1 w-4 h-4' />
                {t('common.fileUploader.uploadFromComputer')}
                <FileInput fileConfig={fileConfig} />
              </Button>
            )
          }
        </div>
      }
    >
      {trigger}
    </Popover>
  )
}

export default memo(FileFromLinkOrLocal)
