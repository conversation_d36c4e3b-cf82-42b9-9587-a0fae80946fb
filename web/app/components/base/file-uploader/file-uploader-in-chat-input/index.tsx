import {
  memo,
  useMemo,
} from 'react'
import FileFromLinkOrLocal from '../file-from-link-or-local'
import { TransferMethod } from '@/types/model'
// 公共组件
import { Attachment } from '@/app/components/base/icons/src/vender/line/chat'
import TextButton from '@/app/components/base/button/text-button'
import type { FileUpload } from '@/app/components/base/features/types'
import { useSystemContext } from '@/context/system-context'

type FileUploaderInChatInputProps = {
  fileConfig: FileUpload
}
const FileUploaderInChatInput = ({
  fileConfig,
}: FileUploaderInChatInputProps) => {
  const { isMobile } = useSystemContext()

  // 是否显示本地上传
  const showFromLocal = useMemo(() => {
    return fileConfig?.allowed_file_upload_methods?.includes(TransferMethod.local_file)
  }, [fileConfig?.allowed_file_upload_methods])
  // 是否显示连接上传
  const showFromLink = useMemo(() => {
    return fileConfig?.allowed_file_upload_methods?.includes(TransferMethod.remote_url)
  }, [fileConfig?.allowed_file_upload_methods])

  const renderTrigger = useMemo(() => {
    if (isMobile)
      return null
    return <TextButton>
      <Attachment className='w-8 h-8 text-gray-G1 hover:bg-gray-G7 rounded' />
    </TextButton>
  }, [isMobile])

  return (
    <FileFromLinkOrLocal
      trigger={renderTrigger}
      fileConfig={fileConfig}
      showFromLocal={showFromLocal}
      showFromLink={showFromLink}
    />
  )
}

export default memo(FileUploaderInChatInput)
