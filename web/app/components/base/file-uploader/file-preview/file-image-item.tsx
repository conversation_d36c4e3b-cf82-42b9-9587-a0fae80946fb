import { useState } from 'react'
import {
  downloadFile,
  fileIsUploaded,
} from '../utils'
import FileImageRender from './file-image-render'
import type { FileEntity } from '@/types/file'
// 公共能力
import TextButton from '@/app/components/base/button/text-button'
import Button from '@/app/components/base/button'
import ProgressCircle from '@/app/components/base/progress-bar/progress-circle'
import cn from '@/utils/classnames'
import { Download, Refresh } from '@/app/components/base/icons/src/vender/line/action'
import ImagePreview from '@/app/components/base/image-uploader/image-preview'
import { Close } from '@/app/components/base/icons/src/vender/line/general'

type FileImageItemProps = {
  file: FileEntity
  className?: string
  showDeleteAction?: boolean
  showDownloadAction?: boolean
  canPreview?: boolean
  onRemove?: (fileId: string) => void
  onReUpload?: (fileId: string) => void
}
const FileImageItem = ({
  file,
  className,
  showDeleteAction,
  showDownloadAction,
  canPreview,
  onRemove,
  onReUpload,
}: FileImageItemProps) => {
  const { id, progress, base64Url, url, name } = file

  // 图像预览地址
  const [imagePreviewUrl, setImagePreviewUrl] = useState('')
  // 图像宽度
  const [imgWidth, setImgWidth] = useState(1)

  return (
    <>
      <div
        className='group/file-image relative cursor-pointer'
        onClick={() => canPreview && setImagePreviewUrl(url || '')}
      >
        {
          showDeleteAction && (
            <Button
              size='small'
              className='hidden group-hover/file-item:flex absolute right-2 top-2 p-0 !w-4 !h-4 rounded-full z-[11]'
              variant={'primary'}
              onClick={() => onRemove?.(id)}
            >
              <Close className='w-3 h-3' />
            </Button>
          )
        }
        <FileImageRender
          className={cn('h-[150px] shadow-md', `w-[${imgWidth}px]`, className)}
          imageUrl={base64Url || url || ''}
          showDownloadAction={showDownloadAction}
          onLoad={(e) => {
            if (e)
              setImgWidth(e.target.offsetWidth)
          }}
        />
        {
          progress >= 0 && progress < 100 && !fileIsUploaded(file)
          && (<div className={
            cn(
              'absolute inset-0 flex items-center justify-center border-[2px] border-effects-image-frame bg-background-overlay-alt z-10',
              className,
            )
          }>
            <ProgressCircle
              percentage={progress}
              size={12}
              circleStrokeColor='stroke-components-progress-white-border'
              circleFillColor='fill-transparent'
              sectorFillColor='fill-components-progress-white-progress'
            />
          </div>
          )
        }
        {
          progress === -1 && (
            <div className='absolute inset-0 flex items-center justify-center border-[2px] border-state-destructive-border bg-background-overlay-destructive z-10'>
              <Refresh
                className='w-5 h-5'
                onClick={() => onReUpload?.(id)}
              />
            </div>
          )
        }
        {
          showDownloadAction && (
            <div className='hidden group-hover/file-image:block absolute inset-0.5 bg-background-overlay-alt bg-opacity-[0.3] z-10'>
              <TextButton
                className='absolute bottom-0.5 right-0.5'
                variant={'icon'}
                onClick={(e) => {
                  e.stopPropagation()
                  downloadFile(url || '', name || '')
                }}
              >
                <Download className='w-4 h-4'></Download>
              </TextButton>
            </div>
          )
        }
      </div>
      {
        imagePreviewUrl && canPreview && (
          <ImagePreview
            title={name}
            url={imagePreviewUrl}
            onCancel={() => setImagePreviewUrl('')}
          />
        )
      }
    </>
  )
}

export default FileImageItem
