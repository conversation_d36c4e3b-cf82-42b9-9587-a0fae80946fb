import { useMemo } from 'react'
import { useFile } from '../hooks'
import { useStore } from '../store'
import FileItem from './file-item'
import FileImageItem from './file-image-item'
import { SupportUploadFileTypes } from '@/types/file'
import type { FileEntity } from '@/types/file'
// 公共能力
import cn from '@/utils/classnames'
import type { FileUpload } from '@/app/components/base/features/types'
import VideoPlayer from '@/app/components/base/video-player'
import AudioPlayer from '@/app/components/base/audio-player'

type FileListProps = {
  className?: string
  files: FileEntity[]
  onRemove?: (fileId: string) => void
  onReUpload?: (fileId: string) => void
  showDeleteAction?: boolean
  showDownloadAction?: boolean
  canPreview?: boolean
  panelContentWidth?: number
  from?: string
}
export const FileList = ({
  className,
  files,
  onReUpload,
  onRemove,
  showDeleteAction = true,
  showDownloadAction = false,
  canPreview,
  panelContentWidth,
  from,
}: FileListProps) => {
  const isInChatInput = useMemo(() => {
    return showDeleteAction
  }, [showDeleteAction])

  return (
    <div className={cn('flex flex-wrap gap-2', className, from === 'FileListInChatInput' && 'pl-[12px]')}>
      {
        files.map((file) => {
          // 图片资源
          if (file.supportFileType === SupportUploadFileTypes.image) {
            return (
              <FileImageItem
                key={file.id}
                file={file}
                className={cn(
                  from === 'FileListInChatInput' && 'h-[66px] mt-[12px]',
                )}
                showDeleteAction={showDeleteAction}
                showDownloadAction={showDownloadAction}
                onRemove={onRemove}
                onReUpload={onReUpload}
                canPreview={canPreview}
              />
            )
          }
          // 视频、音频资源
          else if (!isInChatInput && file.supportFileType === SupportUploadFileTypes.video) {
            return (
              <VideoPlayer
                key={file.id}
                src={file.url || file.dataUrl || ''}
                width={panelContentWidth}
              />
            )
          }
          else if (!isInChatInput && file.supportFileType === SupportUploadFileTypes.audio) {
            return (
              <AudioPlayer
                key={file.id}
                src={file.url || file.dataUrl || ''}
              />
            )
          }
          return (
            <FileItem
              key={file.id}
              className={
                from === 'FileListInChatInput' ? 'mt-3 w-[220px]' : 'w-[220px]'
              }
              file={file}
              showDeleteAction={showDeleteAction}
              showDownloadAction={showDownloadAction}
              onRemove={onRemove}
              onReUpload={onReUpload}
            />
          )
        })
      }
    </div>
  )
}

type FileListInChatInputProps = {
  fileConfig: FileUpload
}
export const FileListInChatInput = ({
  fileConfig,
}: FileListInChatInputProps) => {
  const files = useStore(s => s.files)
  const {
    handleRemoveFile,
    handleReUploadFile,
  } = useFile(fileConfig)

  return (
    <FileList
      files={files}
      onReUpload={handleReUploadFile}
      onRemove={handleRemoveFile}
      className='pr-[118px]'
      from="FileListInChatInput"
    />
  )
}

export const FileListInAnswer = ({
  className,
  files,
  onRemove,
  showDeleteAction = true,
  showDownloadAction = false,
  panelContentWidth,
}: FileListProps) => {
  return (
    <div className={cn('flex flex-wrap gap-2', className)}>
      {
        files.map((file) => {
          // 图片资源
          if (file.supportFileType === SupportUploadFileTypes.image) {
            return (
              <FileItem
                key={file.id}
                file={file}
                showDeleteAction={showDeleteAction}
                showDownloadAction={showDownloadAction}
                onRemove={onRemove}
              />
            )
          }
          // 视频、音频资源
          else if (file.supportFileType === SupportUploadFileTypes.video) {
            return (
              <VideoPlayer
                key={file.id}
                src={file.url || file.dataUrl || ''}
                width={panelContentWidth}
              />
            )
          }
          else if (file.supportFileType === SupportUploadFileTypes.audio) {
            return (
              <AudioPlayer
                key={file.id}
                src={file.url || file.dataUrl || ''}
              />
            )
          }
          return (
            <FileItem
              key={file.id}
              file={file}
              showDeleteAction={showDeleteAction}
              showDownloadAction={showDownloadAction}
              onRemove={onRemove}
            />
          )
        })
      }
    </div>
  )
}
