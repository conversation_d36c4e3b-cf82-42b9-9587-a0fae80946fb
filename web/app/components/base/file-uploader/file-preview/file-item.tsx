import {
  downloadFile,
  fileIsUploaded,
  getFileAppearanceType,
  getFileExtension,
} from '../utils'
import FileTypeIcon from '../file-type-icon'
import FileImageRender from './file-image-render'
import { SupportUploadFileTypes } from '@/types/file'
import type { FileEntity } from '@/types/file'
// 公共能力
import cn from '@/utils/classnames'
import { formatFileSize } from '@/utils/format'
import ProgressCircle from '@/app/components/base/progress-bar/progress-circle'
import { Delete, Download, Refresh } from '@/app/components/base/icons/src/vender/line/action'
import Button from '@/app/components/base/button'
import { Close } from '@/app/components/base/icons/src/vender/line/general'
import TextButton from '@/app/components/base/button/text-button'

type FileItemProps = {
  file: FileEntity
  className?: string
  showDeleteAction?: boolean
  showDownloadAction?: boolean
  showOperation?: boolean
  onRemove?: (fileId: string) => void
  onReUpload?: (fileId: string) => void
}
const FileItem = ({
  file,
  className,
  showOperation,
  showDeleteAction,
  showDownloadAction = true,
  onRemove,
  onReUpload,
}: FileItemProps) => {
  const { id, name, type, progress, url, base64Url, supportFileType } = file
  const ext = getFileExtension(name, type)
  // 判断是否上传错误
  const uploadError = progress === -1
  // 判断是否是图片
  const isImageFile = supportFileType === SupportUploadFileTypes.image

  return (
    <div
      className={cn(
        className,
        'flex items-center gap-2 h-16 group/file-item relative p-2 rounded border border-gray-G5 bg-white',
        !uploadError && 'hover:bg-white',
        uploadError && 'border border-state-destructive-border bg-state-destructive-hover',
        uploadError && 'hover:border-[0.5px] hover:border-state-destructive-border bg-state-destructive-hover-alt',
      )}
    >
      {/* 类型图标 */}
      {
        isImageFile && (
          <FileImageRender
            className='w-12 h-12'
            imageUrl={base64Url || url || ''}
          />
        )
      }
      {/* 文件图标 */}
      {
        !isImageFile && (
          <FileTypeIcon
            type={getFileAppearanceType(name, type)}
            size='lg'
          />
        )
      }
      {/* 文件描述 */}
      <div className='flex-1 w-0'>
        {/* 删除按钮-浮动类型的 */}
        {
          !showOperation && id && showDeleteAction && (
            <Button
              size='small'
              className='hidden group-hover/file-item:flex absolute right-2 top-2 p-0 !w-4 !h-4 rounded-full z-[11]'
              variant={'primary'}
              onClick={() => onRemove?.(id)}
            >
              <Close className='w-3 h-3' />
            </Button>
          )
        }
        {/* 文件名 */}
        <div className='mb-1 line-clamp-1 text-gray-G1 text-S1 leading-H1 break-all' title={name}>
          {name}
        </div>
        {/* 文件描述 */}
        <div className='relative flex items-center justify-between w-full'>
          <div className='truncate text-gray-G2 leading-H1 text-S1' style={{ width: 'calc(100% - 20px)' }}>
            {
              ext && (
                <>
                  {ext}&nbsp;
                </>
              )
            }
            {formatFileSize(file.size || 0) || ''}
          </div>
        </div>
      </div>
      {/* 文件操作 */}
      {showOperation && <div className='flex items-center gap-2'>
        {/* 显示下载按钮 */}
        {
          showDownloadAction && (
            <TextButton
              variant={'icon'}
              onClick={(e) => {
                e.stopPropagation()
                downloadFile(url || '', name || '')
              }}
            >
              <Download className='w-4 h-4'></Download>
            </TextButton>
          )
        }
        {/* 进度条 */}
        {
          Number(progress) >= 0 && !fileIsUploaded(file) && (
            <ProgressCircle
              percentage={progress}
              size={12}
            />
          )
        }
        {/* 重新上传 */}
        {
          id && uploadError && (
            <TextButton
              variant={'icon'}
              onClick={() => onReUpload?.(id)}
            >
              <Refresh className='w-4 h-4'/>
            </TextButton>
          )
        }
        {/* 删除按钮 */}
        {
          id && showDeleteAction && (
            <TextButton variant={'icon'} onClick={() => onRemove?.(id)}>
              <Delete className='w-4 h-4'></Delete>
            </TextButton>
          )
        }
      </div>}
    </div>
  )
}

export default FileItem
