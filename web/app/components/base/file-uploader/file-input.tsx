import { useMemo } from 'react'
import { useFile } from './hooks'
import { useStore } from './store'
import { SupportUploadFileTypes } from '@/types/file'
// 公共能力
import { FILE_EXTS } from '@/config/file'
import type { FileUpload } from '@/app/components/base/features/types'
import { useSystemContext } from '@/context/system-context'

type FileInputProps = {
  fileConfig: FileUpload
  isOnlyImage?: boolean
}
const FileInput = ({
  fileConfig,
  isOnlyImage, // 只允许上传图片或其他格式文件
}: FileInputProps) => {
  const files = useStore(s => s.files)
  const { handleLocalFileUpload } = useFile(fileConfig)
  const { isMobile } = useSystemContext()

  // 是否禁用
  const disabled = useMemo(() => {
    return !!fileConfig.number_limits && files.length >= fileConfig?.number_limits
  }, [fileConfig.number_limits, files.length])
  const allowedFileTypes = useMemo(() => {
    return isMobile
      ? (
        isOnlyImage
          ? fileConfig.allowed_file_types?.filter(item => item === 'image')
          : fileConfig.allowed_file_types?.filter(item => item !== 'image'))
      : fileConfig.allowed_file_types
  }, [fileConfig.allowed_file_types, isMobile, isOnlyImage])
  // 是否包含自定义文件类型
  const isCustom = useMemo(() => {
    return allowedFileTypes?.includes(SupportUploadFileTypes.custom)
  }, [allowedFileTypes])
  // 文件类型如果是其他文件类型【custom】从allowed_file_extensions中获取, 不然一般是从allowed_file_types取
  const exts = useMemo(() => {
    return isCustom
      ? (fileConfig.allowed_file_extensions?.map(item => `.${item}`) || [])
      : (allowedFileTypes?.map(type => FILE_EXTS[type]) || []).flat().map(item => `.${item}`)
  }, [allowedFileTypes, fileConfig.allowed_file_extensions, isCustom])
  // 支持的文件类型
  const accept = useMemo(() => {
    return isMobile ? (exts.length ? exts.join(',') : '.no-files') : exts.join(',')
  }, [exts, isMobile])

  // 本地图片校验并上传
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]

    if (file)
      handleLocalFileUpload(file)
  }

  return (
    <input
      className='absolute block inset-0 opacity-0 text-[0] w-full disabled:cursor-not-allowed cursor-pointer'
      onClick={e => ((e.target as HTMLInputElement).value = '')}
      type='file'
      onChange={handleChange}
      accept={accept}
      disabled={disabled}
    />
  )
}

export default FileInput
