'use client'
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import useS<PERSON> from 'swr'
import s from './index.module.css'
import type { CustomFile as File, FileItem } from '@/types/videos'

import { upload } from '@/service/base'
import { fetchFileUploadConfig } from '@/service/common'
import I18n from '@/context/i18n'
import { LanguagesSupported } from '@/i18n/language'
import { fetchSupportFileTypes } from '@/service/datasets'

// 公共组件
import cn from '@/utils/classnames'
import Scrollbar from '@/app/components/base/scrollbar'
import FileIcon from '@/app/components/base/file-icon'
import { ErrorFill, Loading, SuccessFill } from '@/app/components/base/icons/src/public/common'
import TextButton from '@/app/components/base/button/text-button'
import { Upload01 } from '@/app/components/base/icons/src/vender/line/action'
import { Delete } from '@/app/components/base/icons/src/vender/line/general'
import { ToastContext } from '@/app/components/base/toast'

type IFileUploaderProps = {
  // 支持的文件类型
  supportTypes?: Array<string>
  // 文件列表
  fileList: FileItem[]
  // 预上传事件
  prepareFileList: (files: FileItem[]) => void
  // 单文件更新事件
  onFileUpdate: (
    fileItem: FileItem,
    progress: number,
    list: FileItem[]
  ) => void
  // 文件列表更新事件
  onFileListUpdate?: (files: FileItem[]) => void
  // 文件预览
  onPreview?: (file: File) => void
  // 文件上传后处理结果
  onFileUploaded?: (result: any) => any
  // 上传地址
  url?: string
  // 自动上传
  autoUpload?: boolean
  // 最大上传数量
  max?: number
  // 最大上传大小
  maxSize?: number
  // 搜索参数
  searchParams?: string
  // 失败提示
  errorMsg?: {
    type?: string
    size?: string
  }
}

const FileUploaderInDrag = forwardRef(
  (
    {
      url = '/files/upload',
      searchParams = '',
      autoUpload = true,
      supportTypes: propSupportTypes,
      fileList,
      max,
      maxSize,
      errorMsg,
      prepareFileList,
      onFileUploaded,
      onFileUpdate,
      onFileListUpdate,
      onPreview,
    }: IFileUploaderProps,
    res,
  ) => {
    const { t } = useTranslation()
    const { notify } = useContext(ToastContext)
    const { locale } = useContext(I18n)
    const dropRef = useRef<HTMLDivElement>(null)
    const dragRef = useRef<HTMLDivElement>(null)
    const fileUploader = useRef<HTMLInputElement>(null)
    const fileListRef = useRef<FileItem[]>([])

    // 是否正在拖拽
    const [dragging, setDragging] = useState(false)

    // 获取文件上传配置
    const { data: fileUploadConfigResponse } = useSWR(
      { url: '/files/upload' },
      fetchFileUploadConfig,
    )
    // 获取支持的文件类型
    const { data: supportFileTypesResponse } = useSWR(
      { url: '/files/support-type' },
      fetchSupportFileTypes,
    )
    // 支持的文件类型
    const supportTypes
      = propSupportTypes || supportFileTypesResponse?.allowed_extensions || []
    // 文件上传配置
    const fileUploadConfig = useMemo(() => {
      return {
        ...fileUploadConfigResponse,
        file_size_limit:
          maxSize || fileUploadConfigResponse?.file_size_limit || 15,
        batch_count_limit:
          max || fileUploadConfigResponse?.batch_count_limit || 50,
      }
    }, [fileUploadConfigResponse, max, maxSize])
    // 支持的文件类型展示
    const supportTypesShowNames = (() => {
      return [...supportTypes]
        .map(item => item.toLowerCase()) // convert to lower case
        .filter((item, index, self) => self.indexOf(item) === index) // remove duplicates
        .filter(item => !['md', 'htm'].includes(item))
        .map(item => item.toUpperCase()) // convert to upper case
        .join(locale !== LanguagesSupported[1] ? ', ' : '、 ')
    })()
    const ACCEPTS = supportTypes.map((ext: string) => `.${ext}`)

    // 获取当前文件类型
    const getFileType = (currentFile: File) => {
      if (!currentFile)
        return ''

      const arr = currentFile.name.split('.')
      return arr[arr.length - 1]
    }
    // 判断是否为有效的文件
    const isValid = useCallback(
      (file: File) => {
        const { size } = file
        const ext = `.${getFileType(file)}`
        const isValidType = ACCEPTS.includes(ext.toLowerCase())
        if (!isValidType) {
          notify({
            type: 'error',
            message:
              errorMsg?.type || 'common.fileUploader.validation.typeError',
          })
        }

        const isValidSize
          = size <= fileUploadConfig.file_size_limit * 1024 * 1024
        if (!isValidSize) {
          notify({
            type: 'error',
            message: t('common.fileUploader.validation.size', {
              size: fileUploadConfig.file_size_limit,
            }),
          })
        }

        return isValidType && isValidSize
      },
      [ACCEPTS, fileUploadConfig.file_size_limit, notify, errorMsg?.type, t],
    )

    // 上传文件
    const fileUpload = useCallback(
      async (fileItem: FileItem): Promise<FileItem> => {
        const formData = new FormData()
        formData.append('file', fileItem.file)
        const onProgress = (e: ProgressEvent) => {
          if (e.lengthComputable) {
            const percent = Math.floor((e.loaded / e.total) * 99)
            onFileUpdate(fileItem, percent, fileListRef.current)
          }
        }

        return upload(
          {
            xhr: new XMLHttpRequest(),
            data: formData,
            onprogress: onProgress,
          },
          false,
          url,
          searchParams,
        )
          .then((res: File) => {
            const completeFile = {
              fileID: fileItem.fileID,
              file: onFileUploaded ? (onFileUploaded(res) as File) : res,
              progress: -1,
            }
            const index = fileListRef.current.findIndex(
              item => item.fileID === fileItem.fileID,
            )
            fileListRef.current[index] = completeFile
            onFileUpdate(completeFile, 100, fileListRef.current)
            return Promise.resolve({ ...completeFile })
          })
          .catch((e) => {
            notify({
              type: 'error',
              message:
                (e?.response?.code === 'forbidden'
                || e?.response?.code === 'checkcontent_result')
                  ? e?.response?.message
                  : (e || t('common.fileUploader.uploadFromComputerUploadError')),
            })
            onFileUpdate(fileItem, -2, fileListRef.current)
            return Promise.resolve({ ...fileItem })
          })
          .finally()
      },
      [notify, onFileUpdate, onFileUploaded, searchParams, t, url],
    )
    // 批量上传文件
    const uploadBatchFiles = useCallback(
      (bFiles: FileItem[]) => {
        bFiles.forEach(bf => (bf.progress = 0))
        return Promise.all(bFiles.map(fileUpload))
      },
      [fileUpload],
    )
    // 多次分批上传文件
    const uploadMultipleFiles = useCallback(
      async (files: FileItem[]) => {
        const batchCountLimit = fileUploadConfig.batch_count_limit
        const length = files.length
        let start = 0
        let end = 0

        while (start < length) {
          if (start + batchCountLimit > length)
            end = length
          else end = start + batchCountLimit
          const bFiles = files.slice(start, end)
          await uploadBatchFiles(bFiles)
          start = end
        }
      },
      [fileUploadConfig, uploadBatchFiles],
    )
    // 初始化上传
    const initialUpload = useCallback(
      (files: File[]) => {
        if (!files.length)
          return false
        if (
          files.length + fileList.length
          > fileUploadConfig.batch_count_limit
        ) {
          notify({
            type: 'error',
            message: t('common.fileUploader.validation.filesNumber', {
              filesNumber: max,
            }),
          })
          return false
        }

        const preparedFiles = files.map((file, index) => ({
          fileID: `file${index}-${Date.now()}`,
          file,
          progress: -1,
        }))
        const newFiles = [...fileListRef.current, ...preparedFiles]
        prepareFileList(newFiles)
        fileListRef.current = newFiles
        if (autoUpload)
          uploadMultipleFiles(preparedFiles)
      },
      [
        fileList.length,
        fileUploadConfig.batch_count_limit,
        prepareFileList,
        autoUpload,
        uploadMultipleFiles,
        notify,
        t,
        max,
      ],
    )
    // 变更文件——开始上传
    const fileChangeHandle = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = [...(e.target.files ?? [])] as File[]
        e.target.value = ''
        initialUpload(files.filter(isValid))
      },
      [isValid, initialUpload],
    )

    const handleDragEnter = (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      e.target !== dragRef.current && setDragging(true)
    }
    const handleDragOver = (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
    }
    const handleDragLeave = (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      e.target === dragRef.current && setDragging(false)
    }
    const handleDrop = useCallback(
      (e: DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setDragging(false)
        if (!e.dataTransfer)
          return

        const files = [...e.dataTransfer.files] as File[]
        const validFiles = files.filter(isValid)
        initialUpload(validFiles)
      },
      [initialUpload, isValid],
    )

    // 打开上传窗口
    const selectHandle = () => {
      if (fileUploader.current)
        fileUploader.current.click()
    }
    // 删除文件
    const removeFile = (fileID: string) => {
      if (fileUploader.current)
        fileUploader.current.value = ''

      fileListRef.current = fileListRef.current.filter(
        item => item.fileID !== fileID,
      )
      onFileListUpdate?.([...fileListRef.current])
    }
    // 删除文件通过索引
    const removeFileByIndex = (index: number) => {
      if (fileUploader.current)
        fileUploader.current.value = ''

      fileListRef.current.splice(index, 1)
      onFileListUpdate?.([...fileListRef.current])
    }

    useImperativeHandle(res, () => ({
      // 开始上传
      startUpload: () => {
        uploadMultipleFiles(fileListRef.current)
      },
    }))

    useEffect(() => {
      dropRef.current?.addEventListener('dragenter', handleDragEnter)
      dropRef.current?.addEventListener('dragover', handleDragOver)
      dropRef.current?.addEventListener('dragleave', handleDragLeave)
      dropRef.current?.addEventListener('drop', handleDrop)
      return () => {
        dropRef.current?.removeEventListener('dragenter', handleDragEnter)
        dropRef.current?.removeEventListener('dragover', handleDragOver)
        dropRef.current?.removeEventListener('dragleave', handleDragLeave)
        dropRef.current?.removeEventListener('drop', handleDrop)
      }
    }, [handleDrop])
    useEffect(() => {
      fileListRef.current = fileList
    }, [fileList])

    return (
      <>
        {/* 上传触发器-隐式 */}
        <input
          ref={fileUploader}
          id="fileUploader"
          style={{ display: 'none' }}
          type="file"
          multiple
          accept={ACCEPTS.join(',')}
          onChange={fileChangeHandle}
        />
        {/* 上传触发器-显式 */}
        <div ref={dropRef} className={cn(s.uploader, dragging && s.dragging)}>
          <Upload01 className={s.uploadIcon}></Upload01>
          <div className={s.uploadTitle}>
            {t('common.fileUploader.button')}
            <label className={s.browse} onClick={selectHandle}>
              {t('common.fileUploader.browse')}
            </label>
          </div>
          {
            <>
              <div className={s.uploadTip}>
                {t('common.fileUploader.tip', {
                  supportTypes: supportTypesShowNames,
                })}
              </div>
              <div className={s.uploadTip}>
                {t('common.fileUploader.tip2', {
                  size: fileUploadConfig.file_size_limit,
                  filesNumber: fileUploadConfig.batch_count_limit,
                })}
              </div>
            </>
          }

          {dragging && <div ref={dragRef} className={s.draggingCover} />}
        </div>
        {/* 文件列表 */}
        {
          fileList.length
            ? (<Scrollbar
              scrollbarPosition="relative"
              className={s.fileList}
              showEmpty={false}
            >
              {fileList.map((fileItem, index) => (
                <div
                  key={`${fileItem.fileID}-${index}`}
                  onClick={() =>
                    fileItem.file?.id && onPreview && onPreview(fileItem.file)
                  }
                  className={cn(
                    s.file,
                    'group',
                    fileItem.progress === 100 && s.success,
                    fileItem.progress > -2
                    && fileItem.progress < 100
                    && s.uploading,
                    fileItem.progress === -2 && s.error,
                  )}
                >
                  {/* 上传中 */}
                  {fileItem.progress > -2 && fileItem.progress < 100 && (
                    <Loading
                      className={cn('animate-spin', s.resultIcon)}
                    ></Loading>
                  )}
                  {/* 成功 */}
                  {fileItem.progress === 100 && (
                    <SuccessFill className={s.resultIcon}></SuccessFill>
                  )}
                  {/* 失败 */}
                  {fileItem.progress === -2 && (
                    <ErrorFill className={s.resultIcon}></ErrorFill>
                  )}
                  {/* 文件信息 */}
                  <div className={s.fileInfo}>
                    <FileIcon
                      className={s.fileIcon}
                      type={getFileType(fileItem.file)}
                    />
                    <div>{fileItem.file.name}</div>
                  </div>
                  {/* 操作按钮 */}
                  <>
                    {fileItem.progress === -2 && (
                      <div className="flex items-center gap-2">
                        <TextButton
                          variant="primary"
                          onClick={(e) => {
                            e.stopPropagation()
                            fileUpload(fileItem)
                          }}
                        >
                          {t('common.operation.retry')}
                        </TextButton>
                        <TextButton
                          className="hidden group-hover:!flex"
                          variant="text"
                          onClick={(e) => {
                            e.stopPropagation()
                            removeFileByIndex(index)
                          }}
                        >
                          <Delete className={'w-4 h-4'} />
                        </TextButton>
                      </div>
                    )}
                    {fileItem.progress < 100 && fileItem.progress >= 0 && (
                      <div className={s.percent}>{`${fileItem.progress}%`}</div>
                    )}
                    {fileItem.progress === 100 && (
                      <TextButton
                        className="hidden group-hover:!flex"
                        variant="text"
                        onClick={(e) => {
                          e.stopPropagation()
                          removeFile(fileItem.fileID)
                        }}
                      >
                        <Delete className={'w-4 h-4'} />
                      </TextButton>
                    )}
                  </>
                </div>
              ))}
            </Scrollbar>
            )
            : (
              <></>
            )}
      </>
    )
  },
)

FileUploaderInDrag.displayName = 'FileUploaderInDrag'

export default FileUploaderInDrag
