'use client'
import { Dialog } from '@headlessui/react'
import { useTranslation } from 'react-i18next'
import cn from '@/utils/classnames'
import { Close } from '@/app/components/base/icons/src/vender/line/general'

export type IDrawerProps = {
  title?: string | React.ReactNode
  description?: string | React.ReactNode
  panelClassname?: string
  children: React.ReactNode
  footer?: React.ReactNode
  mask?: boolean
  positionCenter?: boolean
  isOpen: boolean
  showClose?: boolean
  clickOutsideNotOpen?: boolean
  onClose: () => void
  onCancel?: () => void
  onOk?: () => void
}

export default function Drawer({
  title = '',
  description = '',
  panelClassname = '',
  children,
  footer,
  mask = true,
  positionCenter,
  showClose = false,
  isOpen,
  clickOutsideNotOpen,
  onClose,
  onCancel,
  onOk,
}: IDrawerProps) {
  const { t } = useTranslation()
  return (
    <Dialog
      unmount={false}
      open={isOpen}
      onClose={() => !clickOutsideNotOpen && onClose()}
      className="fixed z-[1000] inset-0 overflow-y-auto"
    >
      <div className={cn('flex w-screen h-screen justify-end', positionCenter && '!justify-center')}>
        {/* 遮盖层 */}
        <Dialog.Overlay
          className={cn('z-[999] fixed inset-0', mask && 'bg-black bg-opacity-60')}
        />
        {/* 内容层 */}
        <div className={cn(
          'relative z-[1000] flex flex-col justify-between',
          'bg-white w-full max-w-sm p-0',
          'overflow-hidden text-left align-middle',
          'shadow-[0_1px_2px_0px_rgba(122, 161, 225, 0.20)]',
          'mx-2 sm:mr-2 mb-3 mt-2 rounded',
          panelClassname)}>
          {/* 标题 */}
          <div className={cn((title || showClose) ? 'pt-6 pb-3 px-6' : '', 'flex justify-between items-center')}>
            {title && <div className='flex items-center text-S4 leading-H3 font-semibold text-gray-G9 w-full'>
              {title}
            </div>
            }
            { showClose && <Close onClick={onClose} className='cursor-pointer' />}
          </div>
          {/* 描述 */}
          {description && <div className='pl-6 pr-10 leading-[18px] text-xs font-normal text-gray-500'>
            {description}
          </div>}
          {/* 内容 */}
          <div className='grow overflow-hidden'>
            {children}
          </div>
          {/* 页脚 */}
          {footer && (
            <div className='px-6 mt-6 mb-6'>{footer}</div>
          )}
        </div>
      </div>
    </Dialog>
  )
}
