import type { <PERSON> } from 'react'
import { Close } from '../icons/src/vender/line/general'
import Scrollbar from '../scrollbar'
import cn from '@/utils/classnames'

type PartDrawerProps = {
  title?: string | React.ReactNode

  onClose: () => void
  children?: React.ReactNode
}

const PartDrawer: FC<PartDrawerProps> = ({
  title,
  onClose,
  children,
}) => {
  return (
    <div className='absolute inset-0 z-10 pt-10' style={{
      backgroundColor: 'rgba(16, 24, 40, 0.20)',
    }}>
      <div className='h-full rounded-2xl bg-white flex flex-col pb-4'>
        {/* 标题 */}
        <div className={cn((title) ? 'py-4 px-4' : '', 'flex justify-between items-center')}>
          {title && <div className='flex items-center text-S3 leading-H3 font-semibold text-gray-G9 w-full'>
            {title}
          </div>
          }
          <Close onClick={onClose} className='cursor-pointer' />
        </div>
        <Scrollbar className='h-0 grow'>
          {children}
        </Scrollbar>
      </div>
    </div>
  )
}

export default PartDrawer
