import { useCallback, useEffect, useMemo } from 'react'
import Chat from '../pref-chat'
import { useChat } from '../pref-chat/hooks'
import { getLastAnswer } from '../utils'
import { useEmbeddedChatbotContext } from './context'
import ConfigPanel from './config-panel'
import type {
  ChatConfig,
  ChatItem,
  OnSend,
} from '@/types/chat'
import {
  fetchSuggestedQuestions,
  getUrl,
  stopChatMessageResponding,
} from '@/service/share'
// 公共组件
import { getCropperFontCss } from '@/app/components/app/common/bg-cropper/utils'
import CropperWrapper from '@/app/components/base/cropper/wrapper'
import cn from '@/utils/classnames'

const ChatWrapper = () => {
  const {
    appData,
    appParams,
    appPrevChatList,
    currentConversationId,
    currentConversationItem,
    inputsForms,
    newConversationInputs,
    handleNewConversation,
    handleNewConversationCompleted,
    isMobile,
    isInstalledApp,
    appId,
    appMeta,
    handleFeedback,
    currentChatInstanceRef,
    appChatListDataLoading,
    appInfoLoading,
    showConfigPanelBeforeChat,
  } = useEmbeddedChatbotContext()
  // 对话是否正在准备
  const chatReady = !showConfigPanelBeforeChat || !!appPrevChatList.length
  // 应用配置
  const appConfig = useMemo(() => {
    const config = appParams || {}

    return {
      ...config,
      supportFeedback: true,
      opening_statement: currentConversationId ? currentConversationItem?.introduction : (config as any).opening_statement,
    } as ChatConfig
  }, [appParams, currentConversationItem?.introduction, currentConversationId])
  // 停止函数
  const stopChat = useCallback((taskId: string) => {
    stopChatMessageResponding('', taskId, isInstalledApp, appId)
  }, [isInstalledApp, appId])
  const {
    chatListRef,
    chatList,
    currentChatList,
    handleSend,
    handleStop,
    handleRestart,
    isResponding,
    suggestedQuestions,
    handleUpdateChatList,
    isVoiceConversation,
    openVoiceCall,
    closeVoiceCall,
    isThinking,
    isConnecting,
  } = useChat(
    appConfig,
    {
      inputs: (currentConversationId ? currentConversationItem?.inputs : newConversationInputs) as any,
      inputsForm: inputsForms,
    },
    appPrevChatList,
    stopChat,
  )

  // 发送消息
  const doSend: OnSend = useCallback((message, files, last_answer) => {
    const data: any = {
      query: message,
      files,
      inputs: currentConversationId ? currentConversationItem?.inputs : newConversationInputs,
      conversation_id: currentConversationId,
      parent_message_id: last_answer?.id || getLastAnswer(chatListRef.current)?.id || null,
    }

    handleSend(
      getUrl('chat-messages', isInstalledApp, appId || ''),
      data,
      {
        onGetSuggestedQuestions: responseItemId => fetchSuggestedQuestions(responseItemId, isInstalledApp, appId),
        onConversationComplete: currentConversationId ? undefined : handleNewConversationCompleted,
        isPublicAPI: !isInstalledApp,
      },
    )
  }, [chatListRef, currentConversationId, currentConversationItem, handleSend, newConversationInputs, handleNewConversationCompleted, isInstalledApp, appId])
  // 重新生成
  const doRegenerate = useCallback((chatItem: ChatItem) => {
    const index = chatListRef.current.findIndex(item => item.id === chatItem.id)
    if (index === -1)
      return

    const prevMessages = chatListRef.current.slice(0, index)
    const question = prevMessages.pop()
    const lastAnswer = getLastAnswer(prevMessages)

    if (!question)
      return

    handleUpdateChatList(prevMessages)
    doSend(question.content, question.message_files, lastAnswer)
  }, [chatListRef, handleUpdateChatList, doSend])
  // 语音通话
  const doVocieCall = useCallback(() => {
    openVoiceCall({
      conversation_id: currentConversationId,
    })
  }, [currentConversationId, openVoiceCall])

  useEffect(() => {
    if (currentChatInstanceRef.current) {
      currentChatInstanceRef.current.handleStop = handleStop
      currentChatInstanceRef.current.handleRestart = handleRestart
    }
  }, [])

  const chatNode = useMemo(() => {
    if (inputsForms.length > 0) {
      return (
        <>
          {
            !currentConversationId && (
              <div className={`mx-auto w-full max-w-[800px] ${isMobile && 'px-4'}`}>
                <div className='mb-6' />
                <ConfigPanel />
              </div>
            )
          }
        </>
      )
    }
    return null
  }, [currentConversationId, inputsForms, isMobile])

  return (
    <CropperWrapper
      config={appParams?.background_configs?.enabled ? appParams?.background_configs?.wide : undefined}
      className='w-full h-full bg-chatbot-bg overflow-hidden'
    >
      <Chat
        loading={(appChatListDataLoading && chatReady) || appInfoLoading }
        appData={appData}
        answerIcon={appData?.site.icon_url || ''}
        config={appConfig}
        chatList={chatList}
        currentChatList={currentChatList}
        isResponding={isResponding}
        chatContainerClassName='px-6'
        chatContainerInnerClassName={cn('mx-auto w-full sm:w-2/3 md:w-[800px] max-w-[800px]', getCropperFontCss(appParams?.background_configs))}
        chatFooterClassName='bottom-[0px]'
        chatFooterInnerClassName={'mx-auto w-full sm:w-2/3 md:w-[800px] max-w-[800px]'}
        onSend={doSend}
        inputs={currentConversationId ? currentConversationItem?.inputs as any : newConversationInputs}
        inputsForm={inputsForms}
        isPublicAPI={true}
        onRegenerate={doRegenerate}
        onStopResponding={handleStop}
        chatNode={chatNode}
        onFeedback={handleFeedback}
        suggestedQuestions={suggestedQuestions}
        hideProcessDetail
        isVoiceCall={isVoiceConversation}
        onVoiceCall={doVocieCall}
        isThinking={isThinking}
        isConnecting={isConnecting}
        onCancelVoiceCall={closeVoiceCall}
        handleNewConversation={handleNewConversation}
      />
    </CropperWrapper>
  )
}

export default ChatWrapper
