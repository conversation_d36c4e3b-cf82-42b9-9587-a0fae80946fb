import type {
  FC,
  ReactNode,
} from 'react'
import {
  memo,
} from 'react'
import { ChatStaticProvider } from '@/app/components/base/chat/pref-chat/context'
import NewChat from './new-chat-content'
import type { AppData } from '@/types/share'
import type { ChatConfig, ChatItem, Feedback, OnRegenerate, OnSend, InputForm } from '@/types/chat'

export type ChatProps = {
  className?: string
  chatContainerClassName?: string
  chatContainerInnerClassName?: string
  chatFooterClassName?: string
  chatFooterInnerClassName?: string
  chatAnswerContainerInner?: string
  hideProcessDetail?: boolean
  hideLogModal?: boolean
  showFileUpload?: boolean
  chartHasPrev?: boolean
  showPromptLog?: boolean
  noStopResponding?: boolean
  // 应用数据
  appData?: AppData
  // 对话列表
  chatList: ChatItem[]
  // 正在响应的对话列表
  currentChatList?: ChatItem[]
  // 聊天配置
  config?: ChatConfig
  // 是否存在聊天输入
  noChatInput?: boolean
  // 输入值
  inputs?: Record<string, any>
  // 输入表单配置
  inputsForm?: InputForm[]
  // 是否为公共API
  isPublicAPI?: boolean
  // 尝试问列表
  suggestedQuestions?: string[]
  // 问题图标
  questionIcon?: ReactNode
  // 回答图标
  answerIcon?: string
  // 聊天信息前展示节点
  chatNode?: ReactNode
  // 是否显示底部提示
  showInputBottomTip?: boolean
  // 是否正在响应
  isResponding?: boolean
  // 加载中
  loading?: boolean
  // 自动生成按钮显隐
  isShowGenerate?: boolean
  // 发送事件
  onSend?: OnSend
  // 重新生成事件
  onRegenerate?: OnRegenerate
  // 停止响应事件
  onStopResponding?: () => void
  // 处理新增对话
  handleNewConversation?: () => void
  // 反馈回调函数
  onFeedback?: (messageId: string, feedback: Feedback) => void
  // 取消按钮事件
  onCancel?: () => void
  // 替换按钮事件
  setReplacePromptWord: (newWord: string) => void
}

const Chat: FC<ChatProps> = ({
  className,
  appData,
  config,
  onSend,
  inputs,
  inputsForm,
  isPublicAPI = false,
  onRegenerate,
  chatList,
  currentChatList = [],
  isResponding,
  noStopResponding,
  onStopResponding,
  noChatInput,
  chatContainerClassName,
  chatContainerInnerClassName,
  chatFooterClassName,
  chatFooterInnerClassName,
  suggestedQuestions,
  showPromptLog,
  questionIcon,
  answerIcon,
  onFeedback,
  chatAnswerContainerInner,
  hideProcessDetail,
  hideLogModal,
  showInputBottomTip = true,
  loading,
  isShowGenerate,
  onCancel,
  setReplacePromptWord,
}) => {
  return (
    <ChatStaticProvider
      chatAnswerContainerInner={chatAnswerContainerInner}
      config={config}
      appData={appData}
      noChatInput={noChatInput}
      isPublicAPI={isPublicAPI}
      questionIcon={questionIcon}
      answerIcon={answerIcon}
      hideProcessDetail={hideProcessDetail}
      hideLogModal={hideLogModal}
      showPromptLog={showPromptLog}
      showInputBottomTip={showInputBottomTip}
      onSend={onSend}
      onFeedback={onFeedback}
      onRegenerate={onRegenerate}
    >
      <NewChat
        className={className}
        chatContainerClassName={chatContainerClassName}
        chatContainerInnerClassName={chatContainerInnerClassName}
        chatFooterClassName={chatFooterClassName}
        chatFooterInnerClassName={chatFooterInnerClassName}
        inputs={inputs}
        inputsForm={inputsForm}
        chatList={chatList}
        currentChatList={currentChatList}
        isResponding={isResponding}
        noStopResponding={noStopResponding}
        suggestedQuestions={suggestedQuestions}
        loading={loading}
        isShowGenerate={isShowGenerate}
        onStopResponding={onStopResponding}
        onCancel={onCancel}
        setReplacePromptWord={setReplacePromptWord}
      ></NewChat>
    </ChatStaticProvider>
  )
}

export default memo(Chat)
