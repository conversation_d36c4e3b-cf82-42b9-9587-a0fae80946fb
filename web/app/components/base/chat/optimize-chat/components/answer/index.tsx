import type { FC } from 'react'
import { memo } from 'react'
import { useChatStatic } from '@/app/components/base/chat/pref-chat/hooks'
import style from './styles/index.module.css'
import type { ChatItem } from '@/types/chat'
// 公共组件
import cn from '@/utils/classnames'
import LoadingAnim from '@/app/components/base/chat/pref-chat/components/loading-anim'

type AnswerProps = {
  item: ChatItem
  responding?: boolean
}
const Answer: FC<AnswerProps> = ({
  item,
  responding,
}) => {
  const chatAnswerContainerInner = useChatStatic(store => store.chatAnswerContainerInner)
  const { content } = item
  // 过滤掉<think>和<THINK>标签及其内容（不区分大小写）
  const filteredContent = content?.replace(/<think>[\s\S]*?<\/think>/gi, '').trim()
// 处理包含#符号的行，为其添加颜色
const renderContent = () => {
  if (!filteredContent) return null 
  return filteredContent.split('\n').map((line, index) => (
    <div 
      key={index} 
      style={line.includes('#') ? { color: '#2FBD81' } : undefined}
    >
      {line}
    </div>
  ))
}

  return (
    <div className="flex gap-x mt25">
      <div className="w-0 chat-answer-container group grow">
        <div className={cn('group relative', chatAnswerContainerInner)}>
          <div className={cn(style.answerPanel)}>
            {/* 加载动画 */}
            {responding && !content && (
              <div className="flex items-center justify-center w-6 h-5">
                <LoadingAnim type="text" />
              </div>
            )}
            {/* 返回信息流不带有agentThoughts数据的话 */}
            {filteredContent && <pre className="whitespace-pre-wrap break-words w-full overflow-x-hidden">{renderContent()}</pre>}
          </div>
        </div>
      </div>
    </div>
  )
}

export default memo(Answer)
