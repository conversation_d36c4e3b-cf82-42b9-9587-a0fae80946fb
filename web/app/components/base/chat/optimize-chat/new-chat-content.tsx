import type {
  FC,
  ReactNode,
} from 'react'
import {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from 'react'
import { useTranslation } from 'react-i18next'
import { useShallow } from 'zustand/react/shallow'
import { Button } from 'antd'
import Answer from './components/answer'
import { ChatContextProvider } from '@/app/components/base/chat/pref-chat/context'
import { useChatStatic } from '@/app/components/base/chat/pref-chat/hooks'
import type { ChatItem, InputForm } from '@/types/chat'

import cn from '@/utils/classnames'
import Loading from '@/app/components/base/loading'
import { Generate } from '@/app/components/base/icons/src/public/chat'
import ChatInputArea from '@/app/components/base/chat/pref-chat/components/chat-input-area'

export type ChatProps = {
  className?: string
  chatContainerClassName?: string
  chatContainerInnerClassName?: string
  chatFooterClassName?: string
  chatFooterInnerClassName?: string
  showFileUpload?: boolean
  chartHasPrev?: boolean
  noStopResponding?: boolean
  // 对话列表
  chatList: ChatItem[]
  // 正在响应的对话列表
  currentChatList?: ChatItem[]
  // 输入值
  inputs?: Record<string, any>
  // 输入表单配置
  inputsForm?: InputForm[]
  // 尝试问列表
  suggestedQuestions?: string[]
  // 聊天信息前展示节点
  chatNode?: ReactNode
  // 是否正在响应
  isResponding?: boolean
  // 加载中
  loading?: boolean
  // 自动生成按钮显隐
  isShowGenerate?: boolean
  // 停止响应事件
  onStopResponding?: () => void
  // 取消按钮事件
  onCancel?: () => void
  // 替换按钮事件
  setReplacePromptWord: (newWord: string) => void
}

const Chat: FC<ChatProps> = ({
  className,
  chatContainerClassName,
  chatContainerInnerClassName,
  chatFooterClassName,
  chatFooterInnerClassName,
  inputs,
  inputsForm,
  chatList,
  currentChatList = [],
  isResponding,
  noStopResponding,
  suggestedQuestions,
  loading,
  isShowGenerate,
  onStopResponding,
  onCancel,
  setReplacePromptWord,
}) => {
  const { t } = useTranslation()
  const {
    config,
    appData,
    noChatInput,
    handleStop,
    onSend,
  } = useChatStatic(
    useShallow(state => ({
      config: state.config,
      appData: state.appData,
      noChatInput: state.noChatInput,
      hideLogModal: state.hideLogModal,
      onSend: state.onSend,
      handleStop: state.onCloseVoice,
    })),
  )
  const chatWrapRef = useRef<HTMLDivElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const chatContainerInnerRef = useRef<HTMLDivElement>(null)
  const chatFooterRef = useRef<HTMLDivElement>(null)
  const chatFooterInnerRef = useRef<HTMLDivElement>(null)
  const scrollCheckRef = useRef<boolean>(false)

  const appId = useMemo(() => (appData?.app_id || appData?.id || ''), [appData?.app_id, appData?.id])
  const visionConfig = useMemo(() => (config?.file_upload), [config])
  const voiceInput = useMemo(() => (config?.text_to_speech?.voice_input), [config])
  const voiceConversation = useMemo(() => (config?.text_to_speech?.voice_conversation), [config])
  // *****************************************

  // 用户是否滚动
  const userScrolledRef = useRef(false)
  // 是否拥有尝试去问
  const hasTryToAsk
        = config?.suggested_questions_after_answer?.enabled && !!suggestedQuestions?.length && onSend

  // 处理滚动到底部
  const handleScrollToBottom = useCallback(() => {
    if (chatList.length > 1 && chatContainerRef.current && !userScrolledRef.current)
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
  }, [chatList.length])

  useEffect(() => {
    if (chatContainerRef.current && !scrollCheckRef.current) {
      requestAnimationFrame(() => {
        handleScrollToBottom()
      })
    }
  })
  useEffect(() => {
    if (chatFooterRef.current && chatContainerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { blockSize } = entry.borderBoxSize[0]

          // chatContainerRef.current!.style.paddingBottom = `${blockSize}px`
          if (chatWrapRef.current)
            chatWrapRef.current.style.paddingBottom = `${blockSize}px`
          handleScrollToBottom()
        }
      })

      resizeObserver.observe(chatFooterRef.current)

      return () => {
        resizeObserver.disconnect()
      }
    }
  }, [handleScrollToBottom, loading])
  // 在处理滚动的useEffect中修改setUserScrolled函数
  useEffect(() => {
    const chatContainer = chatContainerRef.current
    if (chatContainer) {
      const setUserScrolled = () => {
        if (chatContainer) {
          const { scrollTop, scrollHeight, clientHeight } = chatContainer
          // 更精确地判断用户是否在底部
          const isNearBottom = scrollHeight - scrollTop - clientHeight < 50
          userScrolledRef.current = !isNearBottom
        }
      }
      // 添加滚动事件监听
      chatContainer.addEventListener('scroll', setUserScrolled)
      return () => {
        chatContainer.removeEventListener('scroll', setUserScrolled)
      }
    }
  }, [loading])
  // 完整的自动滚动useEffect /（有用******）、
  useEffect(() => {
    const container = chatContainerInnerRef.current
    if (!container)
      return

    // 滚动到底部的函数
    const scrollToBottom = () => {
      container.scrollTop = container.scrollHeight
    }

    // 检查是否应该自动滚动
    const shouldAutoScroll = () => {
    // 如果用户没有手动滚动离开底部，则应该自动滚动
      return !userScrolledRef.current
    }

    // 只有在应该自动滚动时才滚动到底部
    if (shouldAutoScroll())
      requestAnimationFrame(scrollToBottom)
  }, [currentChatList, isResponding]) // 依赖于currentChatList和isResponding

  // 修改现有的滚动事件处理useEffect中的setUserScrolled函数
  useEffect(() => {
    const chatContainer = chatContainerInnerRef.current // 注意这里改为chatContainerInnerRef
    if (chatContainer) {
      const handleScroll = () => {
        const { scrollTop, scrollHeight, clientHeight } = chatContainer
        // 当距离底部超过30px时，认为用户已手动滚动离开底部
        const isAtBottom = scrollHeight - scrollTop - clientHeight < 30

        // 只有当用户离开底部时才设置userScrolledRef为true
        if (!isAtBottom && !userScrolledRef.current)
          userScrolledRef.current = true

        // 当用户滚动回底部时，重置userScrolledRef为false
        else if (isAtBottom && userScrolledRef.current)
          userScrolledRef.current = false
      }

      chatContainer.addEventListener('scroll', handleScroll)
      return () => {
        chatContainer.removeEventListener('scroll', handleScroll)
      }
    }
  }, []) // 空依赖数组，只在组件挂载时执行一次
  // 切换页面关闭语音播放
  useEffect(() => {
    return () => {
      handleStop?.()
    }
  }, [])

  // 重新生成按钮状态
  const regenerateFlag = true
  // 过滤掉<think>和<THINK>标签及其内容（不区分大小写）
  const targetList = currentChatList?.length > 0 ? currentChatList : chatList;
  const lastItem = targetList?.[targetList.length - 1];
  const filteredContent = lastItem?.content?.replace(/<think>[\s\S]*?<\/think>/gi, '').trim();
  // 确定要显示的聊天列表
  let displayList = currentChatList;
  if (!currentChatList || currentChatList.length === 0) {
    // 如果 currentChatList 为空，则取 chatList 的最后一条消息
    const lastItem = chatList[chatList.length - 1];
    displayList = lastItem ? [lastItem] : [];
  }
  return (
    <ChatContextProvider chatList={chatList}>
      {loading
        ? <Loading></Loading>
        : (
          <div
            ref={chatWrapRef}
            className={cn('flex flex-col h-full', className)}
            style={{ position: 'relative' }}
          >
            {/* 聊天对话容器 */}
            <div
              ref={chatContainerRef}
              className={cn(
                'flex-1 overflow-y-auto',
                chatContainerClassName,
              )}
              style={{ maxHeight: '500px' }}
            >
              <div
                className="h-full overflow-y-auto"
              >
                {/* 对话列表 */}
                {(
                  <div
                    ref={chatContainerInnerRef}
                    className={cn('w-full', chatContainerInnerClassName)}
                    // style={{ maxHeight: '280px' }}
                    style={{ maxHeight: '230px', overflowY: 'auto' }}
                  >
                    {/* 当前对话 */}
                    {displayList.map((item) => {
                      if (item.isAnswer) {
                        return (
                          <Answer
                            key={item.id}
                            item={item}
                            responding={isResponding}
                          />
                        )
                      }
                      return <></>
                    })}
                  </div>
                )}
              </div>
            </div>
            {/* 底部输入框 */}
            <div
              className={cn('flex-shrink-0 mt', {
                [chatFooterClassName as string]: (hasTryToAsk || !noChatInput || !noStopResponding),
              })}
              // style={isMobile ? foooterBg : {}}
              style={{ width: '100%', position: 'absolute', bottom: '-16px' }}
              ref={chatFooterRef}
            >
              {/* 操作按钮  替换/取消/重新生成 */}
              {(!isResponding && (currentChatList.length !== 0 || chatList.length !== 0)) && <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <div>
                  <Button
                    type="primary"
                    style={{ marginRight: '10px' }}
                    onClick={() => {
                      console.log('替换成功')
                      setReplacePromptWord?.(filteredContent)
                      onCancel?.()
                    }}>
                    {t('common.operation.replace')}
                  </Button>
                  <Button onClick={() => { onSend(filteredContent, regenerateFlag) }}>
                    {t('common.operation.regenerate')}
                  </Button>
                </div>
                <div>
                  <Button onClick={onCancel}>
                    {t('common.operation.cancel')}
                  </Button>
                </div>
              </div>}
              {/* 自动生成按钮 */}
              {isShowGenerate && <div
                className="flex items-center justify-center w-[100px] h-8 text-[#3168F5] rounded-full bg-[#A6CAFF] bg-opacity-20 mb-[16px] cursor-pointer"
                onClick={() => { onSend() }}
              >
                <Generate className="w-4 h-4 mr-1" />
                <span>{t('common.operation.languageGeneration')}</span>
              </div>}
              <div ref={chatFooterInnerRef} className={cn('relative', chatFooterInnerClassName)} style={{ width: '100%' }}>
                {!noChatInput && (
                  <ChatInputArea
                    appId={appId}
                    visionConfig={visionConfig}
                    voiceInput={voiceInput}
                    voiceConversation={voiceConversation}
                    isResponding={isResponding}
                    inputs={inputs}
                    inputsForm={inputsForm}
                    onStopResponding={onStopResponding}
                  />
                )}
              </div>
            </div>
          </div>
        )}
    </ChatContextProvider>
  )
}

export default memo(Chat)
