import type {
  FC,
  ReactNode,
} from 'react'
import React, {
  memo,
  useEffect,
  useState,
} from 'react'
// import { QuestionTriangle } from '@/app/components/base/icons/src/vender/solid/general'
import style from './styles/index.module.css'
import type { ChatItem } from '@/types/chat'
import { User } from '@/app/components/base/icons/src/public/avatar'
import { Markdown } from '@/app/components/base/markdown'
import { FileList } from '@/app/components/base/file-uploader'

  type QuestionProps = {
    item: ChatItem
    index: number
    questionIcon?: ReactNode
  }
const Question: FC<QuestionProps> = ({
  item,
  index,
  questionIcon,
}) => {
  const {
    content,
    message_files,
  } = item
  const chatRef = React.useRef<HTMLDivElement>(null)
  const [panelContentWidth, setPanelContentWidth] = React.useState(0)
  const [isTop, setIsTop] = useState<boolean>(false)
  const [fileName, setFileName] = useState<any>('')// 文件名
  const [extension, setExtension] = useState<any>('')// 文件名
  const fileTypeToAvatarMap: Record<string, string> = {
    '.docx': '/assets/image/word.png',
    '.doc': '/assets/image/word.png',
    '.pdf': '/assets/image/pdf.png',
    '.txt': '/assets/image/txt.png',
    '.xls': '/assets/image/excel.png',
    '.xlsx': '/assets/image/excel.png',
  }
  const getAvatarForFileType = (fileName: string): string => {
    const extension = fileName.slice(fileName.lastIndexOf('.')).toLowerCase()
    return fileTypeToAvatarMap[extension] || '/assets/image/default.png' // 默认图标
  }
  useEffect(() => {
    if (chatRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        const width = chatRef.current?.offsetWidth
        setPanelContentWidth(width ? width - 44 - 32 : 0)
      })
      resizeObserver.observe(chatRef.current)
      if (item.content?.startsWith('帮我写一篇关于') && index == 0 && item.input && item.input.template_file && item.input.template_file?.filename) {
        const ar = item.input?.template_file?.filename.split('.')
        if (ar && ar.length > 1) {
          setFileName(ar[0])
          setExtension(ar[1])
          setIsTop(true)
        }
      }
      return () => {
        resizeObserver.disconnect()
      }
    }
  }, [])
  return (
    <div ref={chatRef} className='p-2-24 flex gap-x w-full'>
      <div className={style.chatIcon}>
        {questionIcon || (
          <div className="w-full h-full rounded-full">
            <User className="w-full h-full" />
          </div>
        )}
      </div>
      <div className='relative group'>
        {/* <QuestionTriangle
            className='absolute -right-2 top-0 w-2 h-3 text-[#D1E9FF]/50'
            style={theme ? { color: theme.chatBubbleColor } : {}}
          /> */}
        <div
          className={style.questionPanel}
        >
          {
            !!message_files?.length && (
              <FileList
                files={message_files}
                showDeleteAction={false}
                showDownloadAction={true}
                panelContentWidth={panelContentWidth}
              />
            )
          }
          <Markdown content={content} />
          {isTop
          && <div className={`xq-nowrap xq-flex-vcenter xq-border-box ${style.docDiv}`}>
            <img src={getAvatarForFileType(item.input?.template_file?.filename)} className={`${style.docPic}`} alt="top" />
            <div>
              <p className={`${style.fileName}`}>{fileName}</p>
              <p className={`${style.fileSize}`}>{extension}{item.input?.template_file.size ? ',' : ''}<span>{item.input?.template_file?.size ? `${item.input.template_file.size}kb` : ''}</span></p>
            </div>
          </div>
          }
        </div>
        {/* <div className='h-[24px]' /> */}
      </div>
    </div>
  )
}

export default memo(Question)
