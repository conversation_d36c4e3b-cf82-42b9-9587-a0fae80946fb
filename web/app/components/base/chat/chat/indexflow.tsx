import type { FC, ReactNode } from 'react'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { debounce } from 'lodash-es'
import { useShallow } from 'zustand/react/shallow'
import type { ThemeBuilder } from '../embedded-chatbot/theme/theme-context'
import ChatIntro from './chat-intro'
import Question from './question'
import Answer from './answer'
import ChatInputArea from './chat-input-area/indexflow'
import TryToAsk from './try-to-ask'
import { ChatContextProvider } from './context'
import type { ChatConfig, ChatItem, Feedback, InputForm, OnRegenerate, OnSend } from '@/types/chat'
import cn from '@/utils/classnames'
import type { Emoji } from '@/app/components/tools/types'
import AgentLogModal from '@/app/components/base/log-modal/agent'
import PromptLogModal from '@/app/components/base/log-modal/prompt'
import { useStore as useAppStore } from '@/app/components/app/store'
import type { AppData } from '@/models/share'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'

export type ChatProps = {
  // 应用数据
  appData?: AppData
  // 对话列表
  chatList: ChatItem[]
  config?: ChatConfig
  // 是否正在响应
  isResponding?: boolean
  noStopResponding?: boolean
  
  //hq 判断是否以生成长文 
  islong?: boolean

  isBook?:boolean

  // 停止响应事件
  onStopResponding?: () => void
  // 是否存在聊天输入
  noChatInput?: boolean
  onSend?: OnSend
  inputs?: Record<string, any>
  inputsForm?: InputForm[]
  onRegenerate?: OnRegenerate
  chatContainerClassName?: string
  chatContainerInnerClassName?: string
  chatFooterClassName?: string
  chatFooterInnerClassName?: string
  // 尝试问列表
  suggestedQuestions?: string[]
  showPromptLog?: boolean
  questionIcon?: ReactNode
  answerIcon?: string
  allToolIcons?: Record<string, string | Emoji>
  // 编辑标注
  onAnnotationEdited?: (question: string, answer: string, index: number) => void
  // 添加标注
  onAnnotationAdded?: (
    annotationId: string,
    authorName: string,
    question: string,
    answer: string,
    index: number
  ) => void
  // 删除标注事件
  onAnnotationRemoved?: (index: number) => void
  // 聊天信息前展示节点
  chatNode?: ReactNode
  onFeedback?: (messageId: string, feedback: Feedback) => void
  chatAnswerContainerInner?: string
  hideProcessDetail?: boolean
  hideLogModal?: boolean
  themeBuilder?: ThemeBuilder
  showFeatureBar?: boolean
  showFileUpload?: boolean
  onFeatureBarClick?: (state: boolean) => void
  chartHasPrev?: boolean
  showInputBottomTip?: boolean
  handleNewConversation?: () => void
  currentConversationId?: string
}

const Chat: FC<ChatProps> = ({
  appData,
  config,
  onSend,
  inputs,
  inputsForm,
  onRegenerate,
  chatList,
  isResponding,
  islong,
  isBook=false,
  noStopResponding,
  onStopResponding,
  noChatInput,
  chatContainerClassName,
  chatContainerInnerClassName,
  chatFooterClassName,
  chatFooterInnerClassName,
  suggestedQuestions,
  showPromptLog,
  questionIcon,
  answerIcon,
  onAnnotationAdded,
  onAnnotationEdited,
  onAnnotationRemoved,
  chatNode,
  onFeedback,
  chatAnswerContainerInner,
  hideProcessDetail,
  hideLogModal,
  themeBuilder,
  showFeatureBar,
  showFileUpload,
  onFeatureBarClick,
  chartHasPrev = false,
  showInputBottomTip = true,
  handleNewConversation,
  currentConversationId,
}) => {
  const {
    currentLogItem,
    setCurrentLogItem,
    showPromptLogModal,
    setShowPromptLogModal,
    showAgentLogModal,
    setShowAgentLogModal,
  } = useAppStore(
    useShallow(state => ({
      currentLogItem: state.currentLogItem,
      setCurrentLogItem: state.setCurrentLogItem,
      showPromptLogModal: state.showPromptLogModal,
      setShowPromptLogModal: state.setShowPromptLogModal,
      showAgentLogModal: state.showAgentLogModal,
      setShowAgentLogModal: state.setShowAgentLogModal,
    })),
  )
  const chatWrapRef = useRef<HTMLDivElement>(null)
  const chatIntroRef = useRef<HTMLDivElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const chatContainerInnerRef = useRef<HTMLDivElement>(null)
  const chatFooterRef = useRef<HTMLDivElement>(null)
  const chatFooterInnerRef = useRef<HTMLDivElement>(null)

  // 聊天对话窗口宽度
  const [width, setWidth] = useState(0)
  // 用户是否滚动
  const userScrolledRef = useRef(false)
  // 是否拥有尝试去问
  const hasTryToAsk
    = config?.suggested_questions_after_answer?.enabled && !!suggestedQuestions?.length && onSend
  // 对话列表为空、或者只有一条，且为开场白
  const onlyOepnState
    = chatList.length === 0 || (chatList.length === 1 && chatList[0].isOpeningStatement)

  // 是否显示滚动到底部按钮
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  const [showScrollButton, setShowScrollButton] = useState(false)
  const scrollCheckRef = useRef<NodeJS.Timeout>()

  // 底部输入框毛玻璃
  const foooterBg = {
    background: 'rgba(240, 245, 255, 0.10)',
    backdropFilter: 'blur(25px)',
    paddingTop: '12px',
    paddingBottom: '12px',
    bottom: '0px',
  }

  // 处理滚动到底部
  const handleScrollToBottom = useCallback(() => {
    if (chatList.length > 1 && chatContainerRef.current && !userScrolledRef.current)
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
  }, [chatList.length])
  // 处理屏幕尺寸变化
  const handleWindowResize = useCallback(() => {
    if (chatContainerRef.current)
      setWidth(document.body.clientWidth - (chatContainerRef.current?.clientWidth + 16) - 8)
    if (chatContainerRef.current && chatFooterRef.current)
      chatFooterRef.current.style.width = `${chatContainerRef.current.clientWidth}px`
    if (chatContainerInnerRef.current && chatFooterInnerRef.current)
      chatFooterInnerRef.current.style.width = `${chatContainerInnerRef.current.clientWidth}px`
    else if (chatIntroRef.current && chatFooterInnerRef.current)
      chatFooterInnerRef.current.style.width = `${chatIntroRef.current.clientWidth}px`
  }, [])

  const clickScrollToBottom = useCallback(() => {
    if (!chatContainerRef.current)
      return
    chatContainerRef.current.scrollTo({
      top: chatContainerRef.current.scrollHeight,
      behavior: 'smooth',
    })
  }, [])

  const hasNewConversationBtn = useMemo(() => {
    return !!handleNewConversation
  }, [handleNewConversation])

  useEffect(() => {
    const chatContainer = chatContainerRef.current
    if (chatContainer) {
      const handleScroll = debounce(() => {
        if (scrollCheckRef.current)
          clearTimeout(scrollCheckRef.current)

        scrollCheckRef.current = setTimeout(() => {
          const hasScroll = chatContainer.scrollHeight > chatContainer.clientHeight
          const isNotAtBottom = chatContainer.scrollHeight - chatContainer.scrollTop - chatContainer.clientHeight > 100
          setShowScrollButton(hasScroll && isNotAtBottom)
        }, 100)
      }, 100)

      handleScroll()
      chatContainer.addEventListener('scroll', handleScroll)
      return () => {
        chatContainer.removeEventListener('scroll', handleScroll)
        if (scrollCheckRef.current)
          clearTimeout(scrollCheckRef.current)
      }
    }
  }, [])

  useEffect(() => {
    handleScrollToBottom()
    handleWindowResize()
  }, [handleScrollToBottom, handleWindowResize])
  useEffect(() => {
    if (chatContainerRef.current) {
      requestAnimationFrame(() => {
        handleScrollToBottom()
        handleWindowResize()
      })
    }
  })
  useEffect(() => {
    window.addEventListener('resize', debounce(handleWindowResize))
    return () => window.removeEventListener('resize', handleWindowResize)
  }, [handleWindowResize])
  useEffect(() => {
    if (chatFooterRef.current && chatContainerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { blockSize } = entry.borderBoxSize[0]

          // chatContainerRef.current!.style.paddingBottom = `${blockSize}px`
          if (chatWrapRef.current)
            chatWrapRef.current.style.paddingBottom = `${blockSize + 24}px`
          handleScrollToBottom()
        }
      })

      resizeObserver.observe(chatFooterRef.current)

      return () => {
        resizeObserver.disconnect()
      }
    }
  }, [handleScrollToBottom])
  const [showForm, setshowForm] = useState(false);
  useEffect(() => {
    const chatContainer = chatContainerRef.current
    if (chatContainer) {
      const setUserScrolled = () => {
        if (chatContainer) {
          userScrolledRef.current
            = chatContainer.scrollHeight - chatContainer.scrollTop >= chatContainer.clientHeight + 300
        }
      }
      chatContainer.addEventListener('scroll', setUserScrolled)
      return () => chatContainer.removeEventListener('scroll', setUserScrolled)
    }
  }, [])

  useEffect(() => {
     if(chatList.length>1){
      chatList.forEach((item, index) => {
     if(item.content.includes('正在生成大纲...') && !item.workflowProcess){
            setshowForm(true)
      }else if(item.content.includes('正在生成大纲...') && item.workflowProcess){
            setshowForm(true)
        }
       })
     }
  }, [chatList])

  return (
    <ChatContextProvider
      config={config}
      chatList={chatList}
      isResponding={isResponding}
      showPromptLog={showPromptLog}
      questionIcon={questionIcon}
      answerIcon={answerIcon}
      onSend={onSend}
      onRegenerate={onRegenerate}
      onAnnotationAdded={onAnnotationAdded}
      onAnnotationEdited={onAnnotationEdited}
      onAnnotationRemoved={onAnnotationRemoved}
      onFeedback={onFeedback}
      showInputBottomTip={showInputBottomTip}
    >
      {(inputsForm?.find(form => form.type === 'longTxt'))
        ? (
          <div ref={chatWrapRef} className={cn('relative h-full py-5')}>
            {/* 聊天对话容器 */}
            <div
              ref={chatContainerRef}
              className={cn(
                'relative h-full overflow-y-auto overflow-x-hidden',
                chatContainerClassName,
              )}
            >
              <div ref={chatContainerInnerRef} className={cn('w-full', chatContainerInnerClassName)}>
                {!showForm&&chatNode}
                {(!onlyOepnState || chartHasPrev || isResponding) && (
                  <div
                    className='w-full'
                    // className={cn('w-full midWen', chatContainerInnerClassName)}
                  >
                    {chatList.map((item, index) => {
                      if (item.isAnswer) {
                        const isLast = item.id === chatList[chatList.length - 1]?.id
                        return (
                          <Answer
                          appData={appData}
                          key={item.id}
                          item={item}
                          question={chatList[index - 1]?.content}
                          index={index}
                          config={config}
                          onSend={onSend as (message: any) => void}
                          answerIcon={answerIcon}
                          responding={isLast && isResponding}
                          showPromptLog={showPromptLog}
                          chatAnswerContainerInner={chatAnswerContainerInner}
                          hideProcessDetail={hideProcessDetail}
                          noChatInput={noChatInput}
                          onShowModel={() => {}}
                          onAc={()=>{}}
                          isBook={isBook}
                          inputsForm={inputsForm}
                        />
                        )
                      }
                      return (
                        <Question
                        key={item.id}
                        item={item}
                        index={index}
                        questionIcon={questionIcon}
                        theme={themeBuilder?.theme}
                      />
                      )
                    })}
                  </div>
                )}
              </div>
            </div>
            {/* 底部输入框 */}
            <div
              className={`absolute flex items-center justify-center w-full bot20 ${
                (hasTryToAsk || noChatInput || !noStopResponding) && chatFooterClassName
              }`}
              style={isMobile ? foooterBg : {}}
              ref={chatFooterRef}
            >
              {/* <div ref={chatFooterInnerRef} className={cn('relative')}> */}
              <div ref={chatFooterInnerRef} className={cn('relative', chatFooterInnerClassName)}>
                {hasTryToAsk && <TryToAsk suggestedQuestions={suggestedQuestions} onSend={onSend} />}
                {noChatInput && (
                  <div className="">
                    <ChatInputArea
                      showFeatureBar={showFeatureBar}
                      showFileUpload={showFileUpload}
                      featureBarDisabled={isResponding}
                      onFeatureBarClick={onFeatureBarClick}
                      visionConfig={config?.file_upload}
                      // speechToTextConfig={config?.speech_to_text}
                      isResponding={isResponding}
                      isLongTxt={islong}
                      onSend={onSend}
                      onStopResponding={onStopResponding}
                      inputs={inputs}
                      inputsForm={inputsForm}
                      theme={themeBuilder?.theme}
                      showInputBottomTip={showInputBottomTip}
                      handleNewConversation={handleNewConversation}
                    />
                  </div>
                )}
              </div>
            </div>
            {showPromptLogModal && !hideLogModal && (
              <PromptLogModal
                width={width}
                currentLogItem={currentLogItem}
                onCancel={() => {
                  setCurrentLogItem()
                  setShowPromptLogModal(false)
                }}
              />
            )}
            {showAgentLogModal && !hideLogModal && (
              <AgentLogModal
                width={width}
                currentLogItem={currentLogItem}
                onCancel={() => {
                  setCurrentLogItem()
                  setShowAgentLogModal(false)
                }}
              />
            )}
          </div>
        )
        : (
          <div ref={chatWrapRef} className={cn('relative h-full py-5')}>
            {/* 聊天对话容器 */}
            <div
              ref={chatContainerRef}
              className={cn(
                'relative h-full overflow-y-auto overflow-x-hidden mb-5',
                chatContainerClassName,
                isMobile && 'pt-10',
              )}
            >
              {/* 前缀节点 */}
              <div className={cn('w-full', chatContainerInnerClassName)}>
                {chatNode}
              </div>
              {/* 对话未开启时展示界面 展示应用信息和开场白预设问题之类的内容 */}
              {onlyOepnState && !chartHasPrev && !isResponding && (
                <ChatIntro
                  ref={chatIntroRef}
                  appConfig={config}
                  appData={appData}
                  className={chatContainerInnerClassName}
                />
              )}
              {/* 对话列表 */}
              {(!onlyOepnState || chartHasPrev || isResponding) && (
                <div
                  ref={chatContainerInnerRef}
                  className={cn('w-full', chatContainerInnerClassName)}
                >
                  {chatList.map((item, index) => {
                    if (item.isAnswer) {
                      const isLast = item.id === chatList[chatList.length - 1]?.id
                      return (
                        <Answer
                          appData={appData}
                          key={item.id}
                          item={item}
                          question={chatList[index - 1]?.content}
                          index={index}
                          config={config}
                          onSend={onSend as (message: any) => void}
                          answerIcon={answerIcon}
                          responding={isLast && isResponding}
                          showPromptLog={showPromptLog}
                          chatAnswerContainerInner={chatAnswerContainerInner}
                          hideProcessDetail={hideProcessDetail}
                          noChatInput={noChatInput}
                          onShowModel={() => {}}
                          onAc={()=>{}}
                        />
                      )
                    }
                    return (
                      <Question
                        key={item.id}
                        index={index}
                        item={item}
                        questionIcon={questionIcon}
                        theme={themeBuilder?.theme}
                      />
                    )
                  })}
                </div>
              )}
            </div>
            {/* 底部输入框 */}
            <div
              className={`absolute ${
                (hasTryToAsk || !noChatInput || !noStopResponding) && chatFooterClassName
              }`}
              style={isMobile ? foooterBg : {}}
              ref={chatFooterRef}
            >
              <div ref={chatFooterInnerRef} className={cn('relative', chatFooterInnerClassName)}>
                {hasTryToAsk && (
                  <TryToAsk
                    questionsClassName={hasNewConversationBtn ? 'ml-[44px]' : ''}
                    suggestedQuestions={suggestedQuestions}
                    onSend={onSend}
                  />
                )}
                {!noChatInput && (
                  <div className="">
                    <ChatInputArea
                      showFeatureBar={showFeatureBar}
                      showFileUpload={showFileUpload}
                      featureBarDisabled={isResponding}
                      onFeatureBarClick={onFeatureBarClick}
                      visionConfig={config?.file_upload}
                      // speechToTextConfig={config?.speech_to_text}
                      isResponding={isResponding}
                      onSend={onSend}
                      onStopResponding={onStopResponding}
                      inputs={inputs}
                      inputsForm={inputsForm}
                      isBook={isBook}
                      theme={themeBuilder?.theme}
                      showInputBottomTip={showInputBottomTip}
                      handleNewConversation={handleNewConversation}
                    />
                  </div>
                )}
              </div>
            </div>
            {showPromptLogModal && !hideLogModal && (
              <PromptLogModal
                width={width}
                currentLogItem={currentLogItem}
                onCancel={() => {
                  setCurrentLogItem()
                  setShowPromptLogModal(false)
                }}
              />
            )}
            {showAgentLogModal && !hideLogModal && (
              <AgentLogModal
                width={width}
                currentLogItem={currentLogItem}
                onCancel={() => {
                  setCurrentLogItem()
                  setShowAgentLogModal(false)
                }}
              />
            )}
          </div>
        )}
      {/* Fix mixed operators */}
      {(isMobile && showScrollButton)
        ? (
          <div
            className='fixed right-[20px] bottom-[140px] cursor-pointer hover:opacity-80 transition-opacity'
            onClick={clickScrollToBottom}
          >
            <img src="/assets/icons/scroll-bot.png" alt="scroll to bottom" />
          </div>
        )
        : null}
    </ChatContextProvider>
  )
}

export default memo(Chat)
