import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { LogIcon } from '@/app/components/base/icons/src/vender/line/files'
import type { IChatItem } from '@/types/chat'
import { useStore as useAppStore } from '@/app/components/app/store'
import Tooltip from '@/app/components/base/tooltip'
import cn from '@/utils/classnames'

type LogProps = {
  logItem: IChatItem
  className?: string
}
const Log: FC<LogProps> = ({
  logItem,
  className,
}) => {
  const { t } = useTranslation()
  const setCurrentLogItem = useAppStore(s => s.setCurrentLogItem)
  const setShowPromptLogModal = useAppStore(s => s.setShowPromptLogModal)
  const setShowAgentLogModal = useAppStore(s => s.setShowAgentLogModal)
  const setShowMessageLogModal = useAppStore(s => s.setShowMessageLogModal)
  const { workflow_run_id: runID, agent_thoughts } = logItem
  const isAgent = agent_thoughts && agent_thoughts.length > 0
  return (
    <Tooltip popupContent={t('appLog.log')}>
      <div
        className={className}
        onClick={(e) => {
          e.stopPropagation()
          e.nativeEvent.stopImmediatePropagation()
          setCurrentLogItem(logItem)
          if (runID)
            setShowMessageLogModal(true)
          else if (isAgent)
            setShowAgentLogModal(true)
          else
            setShowPromptLogModal(true)
        }}
      >
        <LogIcon className={cn('w-4 h-4')}/>
      </div>
    </Tooltip>
  )
}

export default Log
