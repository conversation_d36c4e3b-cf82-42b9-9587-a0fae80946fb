import { FC, useEffect, memo, useMemo, useState, useRef, useLayoutEffect, useCallback } from 'react'
import type { ChatItem } from '@/types/chat'
import { Markdown } from '@/app/components/base/markdown'
import cn from '@/utils/classnames'
import { Input, message, Popconfirm, Modal, Progress, Spin,Popover} from 'antd'
import './styles/style.content.scss'
import BasicModal from './Basic-Modal'
import BasicLongChatModel from './basic-longChat-model'
import { useChatWithHistoryContext } from '../../chat-with-history/context'
import { debounce, set, throttle } from 'lodash'
import {
  DeleteOutlined,
  HolderOutlined,
  LoadingOutlined,
  PaperClipOutlined,
  PlusOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons'
import { DndContext, closestCenter } from '@dnd-kit/core'
import {
  SortableContext,
  useSortable,
  arrayMove,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import { useRouter } from 'next/navigation'
import { CSS, Transform } from '@dnd-kit/utilities'
import { t } from 'i18next'
import {
  fetchConversationsUpdate,
  fetchConversationsVariables,
  fetchChatListUpdate
} from '@/service/share'
import Image from 'next/image'
import style from '@/app/components/base/chat/chat-with-history/styles/style.module.css'
import BasicLongChatView from './basic-longChat-view'
import { useStore } from '@/app/components/workflow/store'

const { TextArea } = Input

type BasicContentProps = {
  item: ChatItem
  onSend: (message: any) => void,
  islongModel?: any,
  onShowModel: (item: any) => void,
  inputForm:any[]
}

type TreeNode = {
  id: string
  title: string | React.ReactNode
  children?: TreeNode[]
  content?: string
  addNode?: boolean
  reference_content?: any[]
}

interface ConversationVariable {
  value: any
  id: string
  description: string
  name: string
  value_type: string
}

const ReferenceContent: FC<{ index: number; data: any; updateKey: string }> = ({
  index,
  data,
  updateKey
}) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const [content, setContent] = useState<any>(null)

  useLayoutEffect(() => {
    // 创建 MutationObserver
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          setContent(data || null)
        }
      })
    })

    // 开始观察
    if (contentRef.current) {
      observer.observe(contentRef.current, {
        childList: true,
        subtree: true
      })
    }

    // 初始设置内容
    if (data) {
      setContent(data)
    }

    // 清理
    return () => observer.disconnect()
  }, [data, index, updateKey])

  return (
    <div ref={contentRef} key={`content-wrapper-${updateKey}`} className="w-full flex justify-end">
      {content &&
        content.slice(0, 3).map((item: any, idx: number) => {
          return item?.title.includes('.pdf') ? (
            <Image
              key={`${idx}-${updateKey}`}
              src={'/assets/image/pdf.png'}
              alt="word"
              width={48}
              height={48}
              style={{ marginLeft: '-6px' }}
              className="rounded-full w-5 h-5"
            />
          ) : item?.title.includes('.doc') || item?.title.includes('.docx') ? (
            <Image
              key={`${idx}-${updateKey}`}
              src={'/assets/image/word.png'}
              alt="word"
              width={48}
              height={48}
              style={{ marginLeft: '-6px' }}
              className="rounded-full w-5 h-5"
            />
          ) : item?.title.includes('.xls') || item?.title.includes('.xlsx') ? (
            <Image
              key={`${idx}-${updateKey}`}
              src={'/assets/image/excel.png'}
              alt="excel"
              width={48}
              height={48}
              style={{ marginLeft: '-6px' }}
              className="rounded-full w-5 h-5"
            />
          ) : item?.title.includes('.txt') ? (
            <Image
              key={`${idx}-${updateKey}`}
              src={'/assets/image/txt.png'}
              alt="txt"
              width={48}
              height={48}
              style={{ marginLeft: '-6px' }}
              className="rounded-full w-5 h-5"
            />
          ) : null
        })}
    </div>
  )
}

const SortableItem = ({
  id,
  value,
  index,
  onChange,
  deleteNode,
  CompositionTree,
  isBasicLongChatModel
}: {
  id: string
  index: number
  isBasicLongChatModel?: boolean
  value: { title: string; content: string }
  onChange: (id: string, index: number, field: 'title' | 'content', newValue: string) => void
  deleteNode: (id: string) => void
  CompositionTree: (
    e: any,
    id: string,
    index: number,
    field: 'title' | 'content',
    newValue: string
  ) => void
}) => {
  const { listeners, setNodeRef, transform, transition } = useSortable({ id })
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    marginBottom: '8px',
    padding: '8px',
    borderRadius: '8px',
    border: '1px solid #f0f0f0',
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    height: 'auto'
  }

  return (
    <li ref={setNodeRef} key={id} style={style} className="li-style-icon">
      <div {...listeners} style={{ cursor: 'grab', marginRight: '8px' }}>
        <HolderOutlined />
      </div>
      <div style={{ width: '93%' }}>
        <TextArea
          value={value.title}
          onChange={e => {
            onChange(id, index, 'title', e.target.value)
          }}
          onCompositionStart={e =>
            CompositionTree(e, id, index, 'title', (e.target as HTMLInputElement).value)
          }
          onCompositionEnd={e =>
            CompositionTree(e, id, index, 'title', (e.target as HTMLInputElement).value)
          }
          placeholder={t('sampleTemplate.Markdown.MarkdownSecondTitle') || ''}
          onMouseDown={e => e.stopPropagation()}
          autoSize
          className="border-none shadow-none hover:border-none hover:shadow-none"
        />
        <TextArea
          value={value.content}
          onCompositionStart={e =>
            CompositionTree(e, id, index, 'content', (e.target as HTMLInputElement).value)
          }
          onCompositionEnd={e =>
            CompositionTree(e, id, index, 'content', (e.target as HTMLInputElement).value)
          }
          onChange={e => onChange(id, index, 'content', e.target.value)}
          placeholder={t('sampleTemplate.Markdown.MarkdownContent') || ''}
          onMouseDown={e => e.stopPropagation()}
          autoSize
          style={{ marginTop: '4px' }}
          className="border-none shadow-none hover:border-none hover:shadow-none"
        />
      </div>
      <div className={`delete-icon`} onClick={() => (!isBasicLongChatModel ? deleteNode(id) : null)}>
        <DeleteOutlined />
        <Popconfirm
          placement="topLeft"
          title={t('sampleTemplate.BasicModal.DeleteConfirm')}
          overlayClassName="custom-popconfirm"
          okText="Yes"
          cancelText="No"
          onConfirm={() => (!isBasicLongChatModel ? deleteNode(id) : null)}
        >
        </Popconfirm>
      </div>
    </li>
  )
}

const Block = ({
  id,
  title,
  index,
  item,
  load,
  content,
  dataLoaded,
  FetchDataListRef,
  handleTitleChange,
  handleContentChange,
  handleAddSection,
  onCompositionStart,
  onCompositionEnd,
  deleteNode,
  handleShowModal,
  CompositionTree,
  isBasicLongChatModel,
  handleDeleteOutlined
}: {
  id: string
  title: string
  index: number
  content: string
  item: any
  load: boolean
  isBasicLongChatModel?: boolean
  dataLoaded: boolean
  onCompositionStart: (e: any, id: string) => void
  onCompositionEnd: (e: any, id: string) => void
  FetchDataListRef: any
  handleTitleChange: (id: string, newValue: string) => void
  handleContentChange: (
    blockId?: string,
    index?: number,
    field?: string,
    item?: any,
    newChildren?: any[]
  ) => void
  handleDeleteOutlined: (id: number) => void
  handleAddSection: (level: number, index?: number) => void
  deleteNode: (id: string) => void
  handleShowModal: (item: any, index: number) => void
  CompositionTree: (
    e: any,
    blockId?: string,
    index?: number,
    field?: string,
    item?: any,
    newChildren?: any[]
  ) => void
}) => {
  const { listeners, setNodeRef, transform, transition } = useSortable({ id })
  const parsedContent = JSON.parse(content || '[]')

  const modifiedTransform = {
    x: transform ? transform.x : 0,
    y: transform ? transform.y : 0,
    scaleX: 1,
    scaleY: 1
  }

  const blockStyle = {
    transform: CSS.Transform.toString(modifiedTransform),
    transition,
    border: '1px solid #ddd',
    borderRadius: '8px',
    padding: '12px',
    background: '#fff',
    marginBottom: '12px',
    width: '100%',
    height: 'auto'
  }

  const handleSortEnd = ({ active, over }: { active: any; over: any }) => {
    if (!over) return

    const oldIndex = parsedContent.findIndex((item: any) => item.id === active.id)
    const newIndex = parsedContent.findIndex((item: any) => item.id === over.id)

    if (oldIndex !== -1 && newIndex !== -1) {
      const newContent = arrayMove(parsedContent, oldIndex, newIndex)
      handleContentChange(id, 0, '', newContent)
    }
  }

  const tip = (
    <div className='tipDiv'>
      <p>参考资料作为可靠的信息来源，能够帮助作者核<br/>
      实各种细节，如事件发生的时间、地点、人物姓<br/>
      名、具体数据等。</p>
    </div>
  );

  return (
    <div ref={setNodeRef} style={blockStyle}>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
        <div {...listeners} style={{ cursor: 'grab', marginRight: '8px' }}>
          <HolderOutlined />
        </div>
        <TextArea
          value={title}
          onChange={e => handleTitleChange(id, e.target.value)}
          onCompositionStart={e => onCompositionStart(e, id)}
          onCompositionEnd={e => onCompositionEnd(e, id)}
          placeholder={t('sampleTemplate.Markdown.MarkdownFirstTitle') || ''}
          autoSize
          className="border-none shadow-none hover:border-none hover:shadow-none"
        />

        {/* <div className="flex items-center rounded-md cursor-pointer">
          <div
            className="ml-2"
            onClick={() => {
              if (!isBasicLongChatModel) {
                FetchDataListRef && !load
                  ? handleShowModal(item, index)
                  : message.info(t('sampleTemplate.BasicModal.LoadingDescription'))
              }
            }}
          >
            <div className="flex items-center justify-end w-24">
              {FetchDataListRef.length > 0 && (
                <ReferenceContent
                  key={`reference-${index}-${id}`}
                  index={index}
                  data={FetchDataListRef}
                  updateKey={id}
                />
              )}
              <div style={{ display: 'inline-block', fontSize: '14px' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span
                    style={{
                      color: '#757575',
                      fontSize: '12px',
                      margin: '0 4px',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {t('sampleTemplate.BasicModal.ContentTitle')}
                  </span>
                  
                </div>
              </div>
            </div>
          </div>
          <Popover placement="top" title="" content={tip}>
            <QuestionCircleOutlined className='tipioc' />
           </Popover>
        </div> */}

        <div className={`ml-2 mr-2 cursor-pointer ${style.shield}`} onClick={() => !isBasicLongChatModel ? handleDeleteOutlined(index) : null}>
          <DeleteOutlined />
          {/* <Popconfirm
            placement="topLeft"
            title={t('sampleTemplate.BasicModal.DeleteConfirm')}
            overlayClassName="custom-popconfirm"
            okText="Yes"
            cancelText="No"
            onConfirm={() => !isBasicLongChatModel ? handleDeleteOutlined(index) : null}
          >
            <DeleteOutlined />
          </Popconfirm> */}


        </div>

        <div
          className={`ml-2 mr-2 cursor-pointer ${style.shield}`}
          onClick={() => (!isBasicLongChatModel ? handleAddSection(2, index) : null)}
        >
          <PlusOutlined />
        </div>
      </div>
      <DndContext collisionDetection={closestCenter} onDragEnd={handleSortEnd}>
        <SortableContext
          items={parsedContent.map((item: any) => item.id)}
          strategy={verticalListSortingStrategy}
        >
          <ul>
            {parsedContent.map((item: any, index: number) => (
              <SortableItem
                key={item.id}
                id={item.id}
                index={index}
                value={item}
                isBasicLongChatModel={isBasicLongChatModel}
                onChange={(id, index, field, newValue) => {
                  const updatedContent = parsedContent.map((child: any, idx: number) =>
                    idx === index ? { ...child, [field]: newValue } : child
                  )
                  handleContentChange(id, index, field, updatedContent)
                }}
                CompositionTree={(id, index, field, newValue) => {
                  const updatedContent = parsedContent.map((child: any, idx: number) =>
                    idx === Number(index) ? { ...child, [field]: newValue } : child
                  )
                  CompositionTree(id, index, field, updatedContent)
                }}
                deleteNode={deleteNode}
              />
            ))}
          </ul>
        </SortableContext>
      </DndContext>
    </div>
  )
}

const BasicContent: FC<BasicContentProps> = ({ item, onSend, islongModel,onShowModel,inputForm}) => {
  const { content, workflowProcess } = item
  const {
    currentConversationId,
    isBasicLongChatModel,
    setIsBasicLongChatModel,
    handleChangeConversation
  } = useChatWithHistoryContext()
  const longTxtForm = inputForm.find(form => form.type === 'longTxt')
  const [treeData, setTreeData] = useState<TreeNode[]>([])
  // 用 ref 保存最新的 treeData，避免 debounceSearch 每次 treeData 改变时重建
  const treeDataRef = useRef(treeData)
  useEffect(() => {
    treeDataRef.current = treeData
  }, [treeData])

  const [dataLoaded, setDataLoaded] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const CompositionFlag = useRef(false)
  const [IsLongTxt, setIsLongTxt] = useState(false)
  const fetchDataCalled = useRef(false)
  const setIsLongTitle = useRef<string>(t('sampleTemplate.BasicModal.notitle'))
  const [BasicLongChatModels, setBasicLongChatModels] = useState(false)
  const [isSpinning, setIsSpinning] = useState(false)
  const [updateKey, setUpdateKey] = useState(0)
  const TableRef = useRef<HTMLDivElement>(null)
  const [progress, setProgress] = useState(0)
  const progressRef = useRef(progress)
  const [modalData, setModalData] = useState<{
    item: any
    index: number
    fetchDataList: any
  }>({ item: null, index: -1, fetchDataList: null })
  const FetchDataListRef = useRef<any>({})
  // 新增：标识是否为初始内容（页面刚加载后由服务端内容解析得到的 treeData）
  const isInitialContent = useRef(true)

  const fileTypeToAvatarMap: Record<string, string> = {
    '.docx': '/assets/image/word.png',
    '.doc': '/assets/image/word.png',
    '.pdf': '/assets/image/pdf.png',
    '.txt': '/assets/image/txt.png',
    '.xls': '/assets/image/excel.png',
    '.xlsx': '/assets/image/excel.png'
  }

  const [isHistoryOpen, setisHistoryOpen] = useState(false);//是否展示历

  let timesNum = 0;
  let timeavl:any=null;

  useEffect(() => {
    if (!content) {
      setTreeData([])
      fetchDataCalled.current = false
      return
    }
    const outline = content.includes('正在生成大纲...')
    if (outline) {
      const sections = content.replace('正在生成大纲...', '').replace(/[^#\n\s\w\u4e00-\u9fa5，。？！、；：'"\-()（）【】《》.]/g, '').split('\n')
      const parsedData: TreeNode[] = []
      let currentSection: TreeNode | null = null
      let currentSubSection: TreeNode | null = null

      for (const line of sections) {
        if (line.startsWith('# ')) {
          currentSection = {
            id: `section-${Date.now()}-${Math.random()}`,
            title: line.substring(1).trim(),
            children: [], // 不预先创建子节点
            content: ''
          }
          parsedData.push(currentSection)
          currentSubSection = null // 一级标题后，重置二级标题
        } else if (line.startsWith('## ')) {
          currentSubSection = {
            id: `subsection-${Date.now()}-${Math.random()}`,
            title: line.substring(2).trim(),
            children: [],
            content: '',
            reference_content: []
          }
          parsedData.push(currentSubSection) // 让二级标题和一级标题同级
        } else if (line.startsWith('### ')) {
          if (!currentSubSection) continue
          const subSubSection = {
            id: `subsubsection-${Date.now()}-${Math.random()}`,
            title: line.substring(3).trim(),
            children: [],
            content: ''
          }
          currentSubSection.children?.push(subSubSection) // 三级标题归属于二级标题
        } else if (line.trim()) {
          const cleanedLine = line.trim().replace(/^#+\s*/, '')
          if (
            currentSubSection &&
            currentSubSection.children &&
            currentSubSection.children.length > 0
          ) {
            currentSubSection.children[currentSubSection.children.length - 1].content +=
              cleanedLine.trim() + '\n'
          } else if (currentSubSection) {
            currentSubSection.content += cleanedLine.trim() + '\n'
          } else if (currentSection) {
            currentSection.content += cleanedLine.trim() + '\n'
          }
        }
      }

      setTreeData(parsedData)
      // 解析后的内容属于初始内容，不触发更新请求
      isInitialContent.current = true
      if (currentConversationId) {
        fetchData()
      }
    } else {
      const outlines = content.includes('正在生成长文...')
      if (outlines) {
        const sections = content.replace('正在生成长文...', '').split('\n')
        let found = false
        for (const line of sections) {
          if (line.startsWith('# ')) {
            // setIsLongTitle.current = line.substring(1).trim() as string
            const title = line.substring(1).trim()
            // 检查标题中是否包含《》
            if (title.includes('《') && title.includes('》')) {
              setIsLongTitle.current = title
              found = true
              break
            }
          }
        }
        setIsLongTxt(true)
      }
    }
  }, [content, currentConversationId])

  useEffect(() => {
    if (!fetchDataCalled.current && currentConversationId && treeData.length > 0) {
      // fetchData()
      fetchDataCalled.current = true
    }
  }, [treeData, currentConversationId])

  useEffect(() => {
    if (workflowProcess?.status === 'running') {
      setIsSpinning(true)
    }
    if (workflowProcess?.status === 'succeeded') {
      setIsSpinning(false)
    }
  }, [workflowProcess?.status])

  useEffect(() => {
    const outline = item.content.includes(t('sampleTemplate.BasicModal.generatingLongtxt'))
    const outline2 = item.content.includes(t('sampleTemplate.BasicModal.generatingOutline'))
    if(outline2 && item.workflowProcess?.status==='succeeded'){
      setDataLoaded(true)
    }
    if (item.workflowProcess?.status === 'running' && outline) {
      triggerEvent()
      timesNum=0;
      if(timeavl){clearInterval(timeavl);}
    }
    if (item.workflowProcess?.status === 'succeeded' && outline) {
      setProgress(100)
      timesNum=0;
      if(timeavl){clearInterval(timeavl);}
    }else if(item.workflowProcess && item.workflowProcess?.status != 'succeeded' && outline){
      timesNum=0;
      clearInterval(timeavl);
      timeavl=null;
      timeavl=setInterval(() => {
        timesNum++;
        if(timesNum>30){
          timesNum=0;
          clearInterval(timeavl);
          if(item.workflowProcess && item.workflowProcess.tracing){
            item.workflowProcess.tracing.forEach((v:any)=>{
              if(v.status==='succeeded' && v.title==t('sampleTemplate.longText.flow5')){
                setProgress(100)
                setIsSpinning(false)
              } 
            })
          }
        }
      },1000)
    }
  }, [item])

  const triggerEvent = () => {
    const currentProgress = progressRef.current
    if (currentProgress < 99.9) {
      const randomIncrement = Math.random() * 0.07 + 0.05
      const newProgress = Math.min(currentProgress + randomIncrement, 100)
      progressRef.current = Number(newProgress.toFixed(2))
      setProgress(Number(newProgress.toFixed(2)))
    }
  }

  // 修改 debounceSearch，移除 treeData 依赖，使用 treeDataRef.current 获取最新值
  const debounceSearch = useCallback(
    debounce(async (data: any) => {
      if (data.slice(1).every((item: any) => item.title.trim() !== '')) {
        try {
          if (!isSpinning && treeDataRef.current.length > 0 && !isBasicLongChatModel) {
            setTimeout(async () => {
              const res = await fetchChatListUpdate(item.id, data, '0')
              if (res.result !== 'success') {
                message.error(res.message)
              }
            }, 500)
          }
        } catch (error) {
          console.error('修改失败:', error)
        }
      } else {
        if (!isSpinning) {
          message.error('标题不能为空！')
        }
      }
    }, 800),
    [isSpinning, isBasicLongChatModel, item.id]
  )

  // 修改 useEffect：如果当前内容仍为初始状态，则不触发 debounceSearch
  useEffect(() => {
    if (isInitialContent.current) return
    if (!CompositionFlag.current && treeData.length > 0) {
      debounceSearch(treeData)
    }
  }, [treeData, debounceSearch])
  const fetchData = async () => {
    try {
      const res = (await fetchConversationsVariables(
        currentConversationId,
        'outline_reference'
      )) as Record<string, ConversationVariable>

      if (res && res.data) {
        FetchDataListRef.current = res.data
        if (treeData && FetchDataListRef.current.value) {
          setTreeData(prev => {
            const newTreeData = [...prev]
            newTreeData.map((item: any) => {
              FetchDataListRef.current.value.forEach((i: any) => {
                if (item.title === i.outline_content) {
                  item.reference_content = i.reference_content
                }
              })
              return newTreeData
            })
            return newTreeData
          })
        }
        setDataLoaded(true)
        setTimeout(() => {
          setUpdateKey(prev => prev + 1)
        }, 0)
      }
    } catch (error) {
      console.error('获取参考文献数据失败:', error)
    }
  }

  const handleDragEnd = ({ active, over }: { active: any; over: any }) => {
    if (active.id !== over.id) {
      const oldIndex = treeData.findIndex(item => item.id === active.id)
      const newIndex = treeData.findIndex(item => item.id === over.id)
      setTreeData(arrayMove(treeData, oldIndex, newIndex))
    }
  }

  const handleTitleChange = (id: string, newValue: string) => {
    // setTreeData(prev => prev.map(node => (node.id === id ? { ...node, title: newValue } : node)))
    // 用户编辑时取消“初始内容”的标识
    isInitialContent.current = false
    if (newValue.trim() === '' && treeData[0]?.id != id) {
      setTreeData(prev => {
        return prev
          .map(node => {
            if (node.id === id) {
              if (node.children && node.children.length === 0) {
                return null
              } else {
                return { ...node, title: '' }
              }
            }
            return node
          })
          .filter(node => node !== null)
      })
    } else {
      setTreeData(prev => prev.map(node => (node.id === id ? { ...node, title: newValue } : node)))
    }
  }

  const handleContentChange = (
    id?: string,
    index?: number,
    blockId?: string,
    newChildren?: any[]
  ) => {
    // 同样认为用户操作了内容
    isInitialContent.current = false
    setTreeData(prevData =>
      prevData.map(block => {
        if (blockId) {
          const children = block.children || []
          if (children.length > 0 && children[index!]?.id === id) {
            return { ...block, children: newChildren }
          }
          return block
        } else {
          if (block.id === id) {
            return { ...block, children: newChildren }
          }
          return block
        }
      })
    )
  }

  const handleSave = () => {
    const collectStructuredContent = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.map(node => {
        const newNode: TreeNode = {
          id: node.id,
          title: node.title,
          content: node.content,
          children: node.children ? collectStructuredContent(node.children) : []
        }
        return newNode
      })
    }
    onSend(t('sampleTemplate.Generate.GenerateDocument'))
  }

  const handleLongModalOpen = () => {
    setisHistoryOpen(true);
  }
  const numberToChinese = (num: number): string => {
    const chineseNumbers = t('sampleTemplate.BasicModal.basicNum').split(",");
    return chineseNumbers[num]
  }

  const handleAddSection = (level: number, index?: number) => {
    const newKey = `${Math.random()}`
    const newChildKey = `${Math.random()}`
    const newSubChildKey = `${Math.random()}`

    switch (level) {
      case 1:
        const FirstLevel = {
          id: newKey,
          title: numberToChinese(treeData.length === 0 ? 0 : treeData.length - 1),
          content: '',
          children: [],
          reference_content: []
        }
        setTreeData(prevData => [...prevData, FirstLevel])
        fetchData()
        break

      case 2:
        const TwoLevel = {
          id: newKey,
          title: '',
          content: '',
          children: []
        }
        setTreeData(prevData => {
          const newData = [...prevData]
          newData[index ? index + 1 : 1]!.children!.push(TwoLevel)
          return newData
        })
        break
      default:
        const DefaultLevel = { id: newKey, title: '', content: '', children: [] }
        setTreeData(prevData => [...prevData, DefaultLevel])
        break
    }
  }

  const deleteNode = (id: string): void => {

    Modal.confirm({
      className: 'del-modal',
      title: t('sampleTemplate.BasicModal.deltitle'),
      content: t('sampleTemplate.BasicModal.delcontent'),
      onOk() {
        const deleteFromData = (data: TreeNode[]): TreeNode[] => {
          return data.filter(node => {
            if (node.id === id) {
              return false
            }
            if (node.children) {
              node.children = deleteFromData(node.children)
              // 检查父节点的条件
              if (node.children.length === 0 && node.title === '') {
                return false
              }
            }
            return true
          })
        }
        setTreeData(deleteFromData(treeData))
      },
      onCancel() {
  
      },
    });
  }

  // const deleteNode = (id: string): void => {
  //   const deleteFromData = (data: TreeNode[]): TreeNode[] => {
  //     return data.filter(node => {
  //       if (node.id === id) {
  //         return false
  //       }
  //       if (node.children) {
  //         node.children = deleteFromData(node.children)
  //       }
  //       return true
  //     })
  //   }
  //   setTreeData(deleteFromData(treeData))
  // }

  const handleShowModal = (item: any, index: number) => {
    let fetchData = [...FetchDataListRef.current.value]
    fetchData[index] = {
      outline_content: item.title,
      word_count: 1,
      reference_content: item.reference_content ? item.reference_content : []
    }
    setModalData({
      item,
      index,
      fetchDataList: fetchData || null
    })
    setIsModalOpen(true)
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  const LongChatModelClose = () => {
    handleChangeConversation(currentConversationId, true)
    setIsBasicLongChatModel(false)
    setBasicLongChatModels(false)
  }

  const Composition = (e: any, id: string) => {
    switch (e.type) {
      case 'compositionstart':
        CompositionFlag.current = true
        break
      case 'compositionend':
        CompositionFlag.current = false
        handleTitleChange(id, (e.target as HTMLInputElement).value)
        break
    }
  }

  const CompositionTree = (
    e: any,
    id?: string,
    index?: number,
    blockId?: string,
    newChildren?: any[]
  ) => {
    switch (e.type) {
      case 'compositionstart':
        CompositionFlag.current = true
        break
      case 'compositionend':
        CompositionFlag.current = false
        handleContentChange(id, index, '', newChildren)
        break
    }
  }

  const handleOk = async (updatedData: any[]) => {
    const data = {
      [FetchDataListRef.current.id]: {
        ...FetchDataListRef.current,
        variable_name: 'outline_reference',
        value: updatedData
      }
    }
    const res = await fetchConversationsUpdate(currentConversationId, data)
    if ((res as { result: string }).result == 'success') {
      setIsModalOpen(false)
      fetchData()
    }
  }

  const handleDeleteOutlined = (index: number) => {
    Modal.confirm({
      className: 'del-modal',
      title:t('sampleTemplate.BasicModal.deltitle'),
      content: t('sampleTemplate.BasicModal.delcontent'),
      onOk() {
        const deleteFromData = (data: TreeNode[]): TreeNode[] => {
          return data.filter((item, i) => i !== index + 1)
        }
        setTreeData(deleteFromData(treeData))
      },
      onCancel() {

      },
    });
    return;
  }
  const renderMarkdownContent = useMemo(() => {
    return (
      <>
        {treeData.length > 0 ? (
          <div className="mt-2" ref={TableRef}>
           {/* <div className={style.markdown_class}>
            以下为您生成的大纲，请根据实际情况进行编辑
           </div> */}
            {/* <div className="flex items-center mb-3 border border-gray-200 rounded-md p-2"> */}
            <div className={`flex items-center mb-3 border border-gray-200 rounded-md p-2 ${style.title_class}`}>
              <div className="xq-no-scale">{t('sampleTemplate.Markdown.Title')}</div>
              <TextArea
                value={typeof treeData[0]?.title === 'string' ? treeData[0]?.title : ''}
                onCompositionStart={e => Composition(e, treeData[0]?.id)}
                onCompositionEnd={e => Composition(e, treeData[0]?.id)}
                onChange={e => handleTitleChange(treeData[0]?.id, e.target.value)}
                placeholder={t('sampleTemplate.Markdown.MarkdownTitle') || ''}
                autoSize
                className="border-none shadow-none hover:border-none hover:shadow-none"
              />
            </div>
            <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext
                items={treeData.map(block => block.id)}
                strategy={verticalListSortingStrategy}
              >
                {treeData.slice(1).map((block, index) => (
                  <Block
                    key={block.id}
                    id={block.id}
                    index={index}
                    item={block}
                    load={isSpinning}
                    dataLoaded={dataLoaded}
                    FetchDataListRef={block.reference_content!}
                    title={typeof block.title === 'string' ? block.title : ''}
                    content={JSON.stringify(block.children || [])}
                    handleTitleChange={handleTitleChange}
                    handleContentChange={handleContentChange}
                    onCompositionStart={Composition}
                    onCompositionEnd={Composition}
                    handleAddSection={handleAddSection}
                    handleDeleteOutlined={handleDeleteOutlined}
                    deleteNode={deleteNode}
                    handleShowModal={handleShowModal}
                    CompositionTree={CompositionTree}
                    isBasicLongChatModel={isBasicLongChatModel}
                  />
                ))}
              </SortableContext>
            </DndContext>
            {dataLoaded && (
              <>
                <div className={`w-full border border-dashed border-blue-600 mt-2 p-1 rounded-md ${style.dagang_view}`}>
                  <button
                    onClick={() => handleAddSection(1)}
                    className={`w-full border-none bg-transparent text-black flex items-center justify-center ${style.dagang_button}`}
                  >
                    {/* <PlusOutlined className={"mr-2"} /> */}
                    <svg  className={"mr-2"} xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <circle cx="8" cy="8" r="8" fill="#3168F5"/>
                    <path d="M7.54891 4.66797H8.45557V7.57464H11.3622V8.4813H8.45557V11.4013H7.54891V8.4813H4.62891V7.57464H7.54891V4.66797Z" fill="white"/>
                    </svg>
                    {t('sampleTemplate.Markdown.AddChapter')}
                  </button>
                </div>
                <div className="w-52 h-10 border border-blue-600 bg-blue-600 text-white text-base mt-8 mb-4 p-1 smallBrd flex items-center justify-center">
                    <button onClick={handleSave} className="flex items-center justify-center">
                    <svg className='onsendsvg' xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M8.92004 3.53602C9.01537 3.57802 9.09137 3.65435 9.13337 3.74935L10.1567 6.06169C10.4097 6.63371 10.8668 7.09091 11.4387 7.34402L13.7504 8.36669C13.8247 8.39949 13.8879 8.45318 13.9322 8.52124C13.9766 8.5893 14.0002 8.66878 14.0002 8.75002C14.0002 8.83126 13.9766 8.91074 13.9322 8.9788C13.8879 9.04686 13.8247 9.10056 13.7504 9.13335L11.4384 10.156C10.8663 10.4091 10.4091 10.8663 10.156 11.4384L9.13371 13.75C9.10091 13.8243 9.04721 13.8875 8.97915 13.9319C8.9111 13.9762 8.83161 13.9999 8.75037 13.9999C8.66914 13.9999 8.58965 13.9762 8.52159 13.9319C8.45353 13.8875 8.39984 13.8243 8.36704 13.75L7.34437 11.438C7.09133 10.8659 6.63412 10.4087 6.06204 10.1557L3.75037 9.13335C3.67605 9.10056 3.61287 9.04686 3.56851 8.9788C3.52415 8.91074 3.50054 8.83126 3.50054 8.75002C3.50054 8.66878 3.52415 8.5893 3.56851 8.52124C3.61287 8.45318 3.67605 8.39949 3.75037 8.36669L6.06237 7.34402C6.63445 7.09098 7.09166 6.63377 7.34471 6.06169L8.36704 3.75002C8.3893 3.69966 8.42127 3.65419 8.46111 3.61619C8.50096 3.57819 8.5479 3.54842 8.59926 3.52858C8.65062 3.50874 8.70538 3.49921 8.76042 3.50054C8.81546 3.50188 8.8697 3.51405 8.92004 3.53635V3.53602ZM4.32304 2.01535C4.36404 2.03335 4.39637 2.06602 4.41471 2.10702L4.85304 3.09802C4.96149 3.34306 5.15733 3.53891 5.40237 3.64735L6.39337 4.08569C6.42522 4.09976 6.45229 4.12279 6.47129 4.15196C6.4903 4.18114 6.50041 4.2152 6.50041 4.25002C6.50041 4.28484 6.4903 4.31891 6.47129 4.34808C6.45229 4.37726 6.42522 4.40028 6.39337 4.41435L5.40237 4.85269C5.15733 4.96113 4.96149 5.15698 4.85304 5.40202L4.41471 6.39302C4.40063 6.42487 4.37761 6.45194 4.34843 6.47094C4.31926 6.48994 4.28519 6.50006 4.25037 6.50006C4.21556 6.50006 4.18149 6.48994 4.15231 6.47094C4.12314 6.45194 4.10011 6.42487 4.08604 6.39302L3.64771 5.40202C3.53926 5.15698 3.34341 4.96113 3.09837 4.85269L2.10704 4.41435C2.07519 4.40028 2.04812 4.37726 2.02912 4.34808C2.01012 4.31891 2 4.28484 2 4.25002C2 4.2152 2.01012 4.18114 2.02912 4.15196C2.04812 4.12279 2.07519 4.09976 2.10704 4.08569L3.09804 3.64735C3.34308 3.53891 3.53893 3.34306 3.64737 3.09802L4.08571 2.10702C4.09525 2.08544 4.10895 2.06595 4.12602 2.04966C4.1431 2.03337 4.16321 2.02061 4.18522 2.0121C4.20723 2.00359 4.2307 1.99949 4.25429 2.00005C4.27788 2.00061 4.30113 2.00581 4.32271 2.01535H4.32304Z" fill="white"/>
                    </svg>
                    {t('sampleTemplate.BasicModal.GenerateDocument')}
                    </button>
                  </div>
              </>
            )}
          </div>
        ) : IsLongTxt ? (
          <div>
            {isSpinning ? (
              <div className='proessTop'>
                <div className='xq-nowrap xq-flex-vcenter'>
                  <LoadingOutlined style={{ fontSize:16, fontWeight: 'bold',color:"#3168F5" }}  />
                  <div className='proesstel'>{t('sampleTemplate.BasicModal.GenerateDocumentDescription')}</div>
                </div>
                <div className='proessLeft'>
                  <div className="flex flex-col justify-center h-[70px] border mt-2 cursor-pointer p-2 pressDivflow">
                    <div className="text-sm text-gray-500">
                    AI正在聚精会神创作中
                    </div>
                    <Progress percent={progress} type="line" />
                  </div>
                </div>
              </div>
            ) : (
              <>
               <div className="mb-2 mtopdiv xq-nowrap xq-flex-vcenter">
                 <img src='/assets/mask.svg'  className="lcpic" />
                  <div className="text-sm">
                    {t('sampleTemplate.BasicModal.GenerateDocumentDescription2')}
                  </div>
                </div>
                <div className='xq-nowrap prodiv'>
                  <div className='docfileDiv xq-nowrap xq-flex-vcenter xq-border-box' onClick={() =>  handleLongModalOpen()}>
                    <div className='docfileflow xq-nowrap xq-flex-vcenter'>
                      <img src={fileTypeToAvatarMap['.docx']} className='docfilepic' />
                      <div>
                        <div className='docfileNameTxt'>{setIsLongTitle.current}</div>
                        <div className='docfileNameType'>Doc</div>
                      </div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M2.33203 14H14.332" stroke="#878EA0" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M3.66797 8.90663V11.3333H6.10703L13.0013 4.43603L10.5663 2L3.66797 8.90663Z" stroke="#878EA0" stroke-linejoin="round"/>
                      </svg>
                  </div>
                </div>
                {
                  isHistoryOpen && (
                    <div className='webModel'>
                    <BasicLongChatView
                      isOpen={isHistoryOpen}
                      item={item}
                      LongChatModelClose={()=>{
                        setisHistoryOpen(false);
                      }}
                    />
                  </div>
                  )
               }
                {/* <div
                  className="flex items-center h-[70px] border border-blue-400 rounded-2xl mt-2 cursor-pointer"
                  onClick={() => (!isBasicLongChatModel ? handleLongModalOpen() : null)}
                >
                  <div className="w-[80px] h-full bg-gradient-to-r from-blue-300 via-blue-500 to-white p-3  rounded-2xl">
                    <img
                      src="/assets/image/article.png"
                      alt=""
                      style={{ objectFit: 'contain', width: '90%', height: '104%' }}
                    />
                  </div>
                  <div className="flex items-center h-full border-l pl-2 border-blue-400 font-bold ">
                    {setIsLongTitle.current}
                    {isSpinning && (
                      <Spin
                        percent="auto"
                        indicator={
                          <LoadingOutlined style={{ fontSize: 18, fontWeight: 'bold' }} spin />
                        }
                      />
                    )}
                  </div>
                </div> */}
              </>
            )}
          </div>
        ) : (
          <Markdown className={cn('', item.isError && '!text-[#F04438]')} content={content} />
        )}
      </>
    )
  }, [
    treeData,
    updateKey,
    isSpinning,
    setIsLongTitle.current,
    isBasicLongChatModel,
    progress,
    IsLongTxt,
    isHistoryOpen
  ])

  useEffect(() => {
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          setUpdateKey(prev => prev + 1)
        }
      })
    })

    if (TableRef.current) {
      observer.observe(TableRef.current, {
        childList: true,
        subtree: false
      })
    }

    return () => observer.disconnect()
  }, [])

  return (
    <>
      {longTxtForm ? (
        <>
          <BasicModal
            isOpen={isModalOpen}
            onOk={handleOk}
            onCancel={handleCancel}
            modalData={modalData}
          />

          <BasicLongChatModel
            isOpen={BasicLongChatModels}
            LongChatModelClose={LongChatModelClose}
            item={item}
          />

          {renderMarkdownContent}
        </>
      ) : (
        <Markdown className={cn('', item.isError && '!text-[#F04438]')} content={content} />
      )}
    </>
  )
}

export default memo(BasicContent)
