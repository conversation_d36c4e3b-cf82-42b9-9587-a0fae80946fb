import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import Operation from './operation'
import AgentContent from './agent-content'
import BasicContent from './basic-content'
import WorkflowProcess from './workflow-process'
import type { ChatConfig, ChatItem } from '@/types/chat'
import LoadingAnim from '@/app/components/base/chat/chat/loading-anim'
import Citation from '@/app/components/base/chat/chat/citation'
import type { AppData } from '@/types/share'
import { FileList } from '@/app/components/base/file-uploader'
import cn from '@/utils/classnames'
import style from '@/app/components/base/chat/chat/styles/style.module.css'

type AnswerContentProps = {
  item: ChatItem
  question: string
  index: number
  onSend: (message: any) => void
  config?: ChatConfig
  responding?: boolean
  showPromptLog?: boolean
  chatAnswerContainerInner?: string
  hideProcessDetail?: boolean
  appData?: AppData
  noChatInput?: boolean
  containerWidth: number
  contentWidth: number
  contentRef: React.RefObject<HTMLDivElement>
}

const AnswerContent: FC<AnswerContentProps> = ({
  item,
  question,
  index,
  onSend,
  config,
  responding,
  showPromptLog,
  chatAnswerContainerInner,
  hideProcessDetail,
  appData,
  noChatInput,
  containerWidth,
  contentWidth,
  contentRef
}) => {
  const { t } = useTranslation()
  const {
    content,
    citation,
    agent_thoughts,
    workflowProcess,
    allFiles,
    message_files,
    annotation
  } = item
  const hasAgentThoughts = !!agent_thoughts?.length

  return (
    <div className={cn('group relative pr-10', chatAnswerContainerInner)}>
      <div
        ref={contentRef}
        className={cn(style.answerPanel, `${workflowProcess && 'w-full'}`)}
        style={{ width: '100%' }}
      >
        {/* ... 其余代码保持不变 ... */}
        {!responding && (
          <Operation
            hasWorkflowProcess={!!workflowProcess}
            maxSize={containerWidth - contentWidth - 4}
            contentWidth={contentWidth}
            item={item}
            question={question}
            index={index}
            showPromptLog={showPromptLog}
            noChatInput={noChatInput}
          />
        )}
        {workflowProcess && !hideProcessDetail && (
          <WorkflowProcess
            data={workflowProcess}
            item={item}
            hideProcessDetail={hideProcessDetail}
          />
        )}
        {workflowProcess && hideProcessDetail && appData && appData.site.show_workflow_steps && (
          <WorkflowProcess
            data={workflowProcess}
            item={item}
            hideProcessDetail={hideProcessDetail}
          />
        )}
        {responding && !content && !hasAgentThoughts && (
          <div className="flex items-center justify-center w-6 h-5">
            <LoadingAnim type="text" />
          </div>
        )}
        {content && !hasAgentThoughts && <BasicContent item={item} onSend={onSend} />}
        {hasAgentThoughts && <AgentContent item={item} responding={responding} />}
        {!!allFiles?.length && (
          <FileList
            className="my-1"
            files={allFiles}
            showDeleteAction={false}
            showDownloadAction
            canPreview
          />
        )}
        {!!message_files?.length && (
          <FileList
            className="my-1"
            files={message_files}
            showDeleteAction={false}
            showDownloadAction
            canPreview
          />
        )}
        {!!citation?.length && !responding && (
          <Citation data={citation} showHitInfo={config?.supportCitationHitInfo} />
        )}
      </div>
    </div>
  )
}

export default AnswerContent
