import React, { use, useCallback,useContext, useEffect, useMemo, useRef, useState } from 'react'
import {
  Affix,
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Select,
  Table,
  Tabs,
  Image,
  Skeleton,
  Empty,
  Dropdown
} from 'antd'
import type { MenuProps } from 'antd';
import BasicLongChatView from './basic-longChat-view'
import { IS_CE_EDITION, PUBLIC_API_PREFIX, TEMPLATE_URL } from '@/config'
import { AiEditor, AiEditorPro } from 'aieditor-pro'

import 'aieditor-pro/dist/style.css'
import './styles/style.longTxetModal.scss'
import { useChatWithHistoryContext } from '@/app/components/base/chat/chat-with-history/context'
import { useChatContext } from '@/app/components/base/chat/chat/context'
import Chat from '@/app/components/base/chat/chat'
import type { GetRef, InputRef, TableProps, TabsProps } from 'antd'
import { set, throttle } from 'lodash'
import { useToastContext } from '@/app/components/base/toast'
import { t } from 'i18next'
import {
  fetchChatListUpdate,
  fetchConversationsVariables,
  fetchWorkflowsRun,
  GetreferencefileId,
  GetHistroyList,
  GetHistroyDetail,
  GetSummary
} from '@/service/share'
import { DeleteOutlined,VerticalAlignBottomOutlined,ClockCircleOutlined,RollbackOutlined,DownOutlined,UpOutlined,PaperClipOutlined,DownloadOutlined,AlignLeftOutlined,LoadingOutlined} from '@ant-design/icons'
import { CheckCard } from '@ant-design/pro-components'
import style from '@/app/components/base/chat/chat-with-history/styles/style.module.css'

//hq 新增底部chat功能
import type { ChatConfig, OnSend } from '@/types/chat'
import { getLastAnswer } from '@/app/components/base/chat/utils'
import { useChat } from '@/app/components/base/chat/chat/hooks'
import { fetchSuggestedQuestions, getUrl, stopChatMessageResponding } from '@/service/share'
import ConfigPanel from '@/app/components/base/chat/chat-with-history/config-panel'
import TemplateDrawer from '../../chat-with-history/config-panel/comments/templateDrawer'




interface ChatData {
  write_type?: string
  write_theme?: string
  word_count?: string
  requirement?: string
  request_type?: string
}


interface Outline {
  id: string
  level: number
  text: string
  pos: number
  size: number
}

interface DataType {
  key: React.Key
  label: string
  value: string
}

interface EditableRowProps {
  index: number
}

interface Item {
  key: string
  label: string
  value: string
}

interface ChatItem {
  id: string
  content: string
}

interface IsChatItem {
  id: string
  answer: string
}

type FormInstance<T> = GetRef<typeof Form<T>>
type ColumnTypes = Exclude<TableProps<DataType>['columns'], undefined>

interface EditableCellProps {
  title: React.ReactNode
  editable: boolean
  dataIndex: keyof Item
  record: Item
  handleSave: (record: Item) => void
}

interface ConversationVariable {
  value: any
  id: string
  description: string
  name: string
  value_type: string
}

interface BasicLongChatModelProps {
  isOpen: boolean
  item: any
  LongChatModelClose: () => void,
  changeItem?:any
}

let  islisten=false;
let  isfuse=false;//是否光标定位
let  timeNum=0;
let  timeref:any=null
let  titlestring="";
let  curcontentdetail:any="";
let  idtimesvalue:any="";

const BasicLongChatModel: React.FC<BasicLongChatModelProps> = ({
  isOpen,
  item,
  LongChatModelClose,
  changeItem
}) => {
  const { notify } = useToastContext()
  //const { currentConversationId, handleChatList } = useChatWithHistoryContext()

  // hq chat能力
  const {
    appParams,
    appPrevChatList,
    currentConversationId,
    handleChatList,
    currentConversationItem,
    inputsForms,
    newConversationInputs,
    handleAutoRename,
    setCurrentChatConversationId,
    handleNewConversationCompleted,
    isMobile,
    isInstalledApp,
    appId,
    appMeta,
    handleFeedback,
    currentChatInstanceRef,
    appData,
    themeBuilder,
    fromMarket,
    chatAllTypeResult,
    setChatAllTypeResult
  } = useChatWithHistoryContext()

  const [chartHasPrev, setchartHasPrev] = useState<boolean>(false)
  const longTxtForm = inputsForms.find(form => form.type === 'longTxt')

    useEffect(() => {
      setchartHasPrev(!!appPrevChatList.length)
    }, [appPrevChatList])




  const { content, workflowProcess } = item
  const [isSpinning, setIsSpinning] = useState(false)
  const beforeTextRef = useRef('')
  const EditableContext = React.createContext<FormInstance<any> | null>(null)
  const [count, setCount] = useState(3)
  const [chartName, setChartName] = useState('')
  const [chartType, setChartType] = useState<string | undefined>(undefined)
  const [LongModalDatas, setLongModalDatas] = useState<any>({})
  const [dataSource, setDataSource] = useState<DataType[]>([
    {
      key: '0',
      label: t('sampleTemplate.longText.remark1'),
      value: '100'
    },
    {
      key: '1',
      label: t('sampleTemplate.longText.remark2'),
      value: '56'
    }
  ])

  //hq 顶部导出文档
  const itemsl: MenuProps['items'] = [
    {
      label: t('sampleTemplate.longText.pdfout'),
      key: '1',
    },
    {
      label: t('sampleTemplate.longText.wordout'),
      key: '2',
    }
  ];


  const AiEditorRef = useRef(null)
  const editorRef = useRef<AiEditorPro | null>(null)
  const [isImageModalOpen, setisImageModalOpen] = useState(false)
  const [isImageModalLoading, setisImageModalLoading] = useState(true)
  const [isTitle, setIsTitle] = useState('')
  const [SelectedImage, setSelectedImage] = useState('')
  const [isAddImage, setisAddImage] = useState(t('sampleTemplate.longText.inserted'))
  const [ImageCollection, setImageCollection] = useState<any[]>([])
  const [ischatList, setIschatList] = useState<any[]>([])






 //hq 是否展示历史记录
  const [isHistoryOpen, setisHistoryOpen] = useState(false);//是否展示历史记录
  const [isHistory, setisHistory] = useState(false)
  const [historyItem, sethistoryItem] = useState<any>('');//当前选中的历史记录
  const [HisList, setHisList] = useState<any[]>([])
  const [HisIds, setHisIds] = useState<any[]>([0])


  const [open, setOpen] = useState(false)
  const [drawerData, setDrawerData] = useState<any>({})

  const [abstract, setAbstract] = useState<any>({});
  const [abstractDetail, setAbstractDetail] = useState<any>({});

  const [parentItem, setParentItem] = useState<any>({});

  const [isGdata, setIsGdata] = useState(false);//是否在请求中

  const [curcontent, setCurcontent] = useState<any>('')
  const [curarr, setcurarr] = useState<any[]>([-1,-1])



  //hq 添加默认生成长文
  const [islong, setislong] = useState(true)
  const appConfig = useMemo(() => {
    const config = appParams || {}
    return {
      ...config,
      supportFeedback: !fromMarket,
      opening_statement: currentConversationId
        ? currentConversationItem?.introduction
        : (config as any).opening_statement
    } as ChatConfig
  }, [appParams, currentConversationItem?.introduction, currentConversationId])
  const {
    chatListRef,
    chatList,
    handleUpdateChatList,
    handleSend,
    handleStop,
    handleRestart,
    isResponding,
    suggestedQuestions
  } = useChat(
    appConfig,
    {
      inputs: (currentConversationId
        ? currentConversationItem?.inputs
        : newConversationInputs) as any,
      inputsForm: inputsForms
    },
    appPrevChatList,
    taskId => {
      return stopChatMessageResponding('', taskId, isInstalledApp, appId);
    }
  )
  let { chatList:chatList2 } = useChatContext()

  useEffect(() => {
    if (AiEditorRef.current && !editorRef.current) {
      const cleanedItem = content.replace(new RegExp(`${t('sampleTemplate.longText.longtxt') as string}\.\.\.\n`, 'g'), '').split('\n')
      const Editor = new AiEditorPro({
        element: AiEditorRef.current,
        placeholder:t('sampleTemplate.longText.placeholder') as string,
        dragActionsEnable: false,
        contentIsMarkdown: true,
        draggable: false,
        ai: {
          models: {
            custom: {
              url: `${PUBLIC_API_PREFIX}/chat-messages`,
              headers: () => {
                return headers()
              },
              wrapPayload: (message: string) => {
                if(message.startsWith(t('sampleTemplate.longText.editmsg1') as string) || message.startsWith(t('sampleTemplate.longText.editmsg2')) || message.startsWith(t('sampleTemplate.longText.editmsg3')) || message.startsWith(t('sampleTemplate.longText.editmsg4'))){
                  doSend(message);
                  return;
                }
                const dat = {
                  request_type: t('sampleTemplate.longText.rewrite')
                }
                const data = {
                  conversation_id: '',
                  is_delete_conversation: true,
                  inputs: {
                    sample: JSON.stringify(dat)
                  },
                  parent_message_id: null,
                  query: message,
                  response_mode: 'streaming'
                }
                return data
              },
              parseMessage: (message: string) => {
                const res = JSON.parse(message)
                if (res.event === 'message' || res.event === 'message_end') {
                  return {
                    role: 'assistant',
                    content: res.answer,
                    // index: number,
                    // //0 代表首个文本结果；1 代表中间文本结果；2 代表最后一个文本结果。
                    status: res.event === 'message' ? 1 : res.event === 'message_end' ? 2 : null
                  }
                }
              },
              protocol: 'sse'
            } as any
          },
            bubblePanelMenusNo:[t('sampleTemplate.longText.rewrite'),t('sampleTemplate.longText.expandConten'),t('sampleTemplate.longText.streamlining'),t('sampleTemplate.longText.editRemark')],
            bubblePanelMenus:[
              {
                prompt: `${t('sampleTemplate.longText.editmsg1')}：\n{content}`,
                icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M15.1986 9.94447C14.7649 9.5337 14.4859 8.98613 14.4085 8.39384L14.0056 5.31138L11.275 6.79724C10.7503 7.08274 10.1433 7.17888 9.55608 7.06948L6.49998 6.50015L7.06931 9.55625C7.17871 10.1435 7.08257 10.7505 6.79707 11.2751L5.31121 14.0057L8.39367 14.4086C8.98596 14.4861 9.53353 14.7651 9.94431 15.1987L12.0821 17.4557L13.4178 14.6486C13.6745 14.1092 14.109 13.6747 14.6484 13.418L17.4555 12.0823L15.1986 9.94447ZM15.2238 15.5079L13.0111 20.1581C12.8687 20.4573 12.5107 20.5844 12.2115 20.442C12.1448 20.4103 12.0845 20.3665 12.0337 20.3129L8.49229 16.5741C8.39749 16.474 8.27113 16.4096 8.13445 16.3918L3.02816 15.7243C2.69958 15.6814 2.46804 15.3802 2.51099 15.0516C2.52056 14.9784 2.54359 14.9075 2.5789 14.8426L5.04031 10.3192C5.1062 10.1981 5.12839 10.058 5.10314 9.92253L4.16 4.85991C4.09931 4.53414 4.3142 4.22086 4.63997 4.16017C4.7126 4.14664 4.78711 4.14664 4.85974 4.16017L9.92237 5.10331C10.0579 5.12855 10.198 5.10637 10.319 5.04048L14.8424 2.57907C15.1335 2.42068 15.4979 2.52825 15.6562 2.81931C15.6916 2.88421 15.7146 2.95507 15.7241 3.02833L16.3916 8.13462C16.4095 8.2713 16.4739 8.39766 16.5739 8.49245L20.3127 12.0338C20.5533 12.2617 20.5636 12.6415 20.3357 12.8821C20.2849 12.9357 20.2246 12.9795 20.1579 13.0112L15.5078 15.224C15.3833 15.2832 15.283 15.3835 15.2238 15.5079ZM16.0206 17.435L17.4348 16.0208L21.6775 20.2634L20.2633 21.6776L16.0206 17.435Z"></path></svg>`,
                title:t('sampleTemplate.longText.rewrite'),
            },
            {
                prompt: `${t('sampleTemplate.longText.editmsg2')}：\n{content}`,
                icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M21 6.75736L19 8.75736V4H10V9H5V20H19V17.2426L21 15.2426V21.0082C21 21.556 20.5551 22 20.0066 22H3.9934C3.44476 22 3 21.5501 3 20.9932V8L9.00319 2H19.9978C20.5513 2 21 2.45531 21 2.9918V6.75736ZM21.7782 8.80761L23.1924 10.2218L15.4142 18L13.9979 17.9979L14 16.5858L21.7782 8.80761Z"></path></svg>`,
                title: t('sampleTemplate.longText.expandConten'),
            },
            {
                prompt: `${t('sampleTemplate.longText.editmsg3')}：\n{content}`,
                icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M20 2C20.5523 2 21 2.44772 21 3V6.757L19 8.757V4H5V20H19V17.242L21 15.242V21C21 21.5523 20.5523 22 20 22H4C3.44772 22 3 21.5523 3 21V3C3 2.44772 3.44772 2 4 2H20ZM21.7782 8.80761L23.1924 10.2218L15.4142 18L13.9979 17.9979L14 16.5858L21.7782 8.80761ZM13 12V14H8V12H13ZM16 8V10H8V8H16Z"></path></svg>`,
                title:t('sampleTemplate.longText.streamlining'),
            },
            {
              prompt: `${t('sampleTemplate.longText.editmsg4')}：\n{content}`,
              icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M13.666 1.66669H2.33268C1.96449 1.66669 1.66602 1.96516 1.66602 2.33335V13.6667C1.66602 14.0349 1.96449 14.3334 2.33268 14.3334H13.666C14.0342 14.3334 14.3327 14.0349 14.3327 13.6667V2.33335C14.3327 1.96516 14.0342 1.66669 13.666 1.66669Z" stroke="#181818" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M1.66602 6H14.3327" stroke="#181818" stroke-linecap="round"/>
                    <path d="M1.66602 10H14.3327" stroke="#181818" stroke-linecap="round"/>
                    <path d="M5.66602 1.66669V14.3334" stroke="#181818" stroke-linecap="round"/>
                    <path d="M10 1.66669V14.3334" stroke="#181818" stroke-linecap="round"/>
                    </svg>`,
              title:t('sampleTemplate.longText.editRemark'),
            },
            {
              prompt: `${t('sampleTemplate.longText.editlong11')}\n${t('sampleTemplate.longText.editlong12')}\n{content}`,
              icon: ` <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 19C12.8284 19 13.5 19.6716 13.5 20.5C13.5 21.3284 12.8284 22 12 22C11.1716 22 10.5 21.3284 10.5 20.5C10.5 19.6716 11.1716 19 12 19ZM6.5 19C7.32843 19 8 19.6716 8 20.5C8 21.3284 7.32843 22 6.5 22C5.67157 22 5 21.3284 5 20.5C5 19.6716 5.67157 19 6.5 19ZM17.5 19C18.3284 19 19 19.6716 19 20.5C19 21.3284 18.3284 22 17.5 22C16.6716 22 16 21.3284 16 20.5C16 19.6716 16.6716 19 17.5 19ZM13 2V4H19V6L17.0322 6.0006C16.2423 8.3666 14.9984 10.5065 13.4107 12.302C14.9544 13.6737 16.7616 14.7204 18.7379 15.3443L18.2017 17.2736C15.8917 16.5557 13.787 15.3326 12.0005 13.7257C10.214 15.332 8.10914 16.5553 5.79891 17.2734L5.26257 15.3442C7.2385 14.7203 9.04543 13.6737 10.5904 12.3021C9.46307 11.0285 8.50916 9.58052 7.76789 8.00128L10.0074 8.00137C10.5706 9.03952 11.2401 10.0037 11.9998 10.8772C13.2283 9.46508 14.2205 7.81616 14.9095 6.00101L5 6V4H11V2H13Z"></path></svg>`,
              title: 'check-spelling-and-grammar',
            },
            '<hr/>',
            {
                prompt: `${t('sampleTemplate.longText.editlong21')}\n{content}`,
                icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M5 15V17C5 18.0544 5.81588 18.9182 6.85074 18.9945L7 19H10V21H7C4.79086 21 3 19.2091 3 17V15H5ZM18 10L22.4 21H20.245L19.044 18H14.954L13.755 21H11.601L16 10H18ZM17 12.8852L15.753 16H18.245L17 12.8852ZM8 2V4H12V11H8V14H6V11H2V4H6V2H8ZM17 3C19.2091 3 21 4.79086 21 7V9H19V7C19 5.89543 18.1046 5 17 5H14V3H17ZM6 6H4V9H6V6ZM10 6H8V9H10V6Z"></path></svg>`,
                title: 'translate',
            },
            {
                prompt: `${t('sampleTemplate.longText.editlong31')}\n${t('sampleTemplate.longText.editlong32')}\n{content}`,
                icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M18 3C19.6569 3 21 4.34315 21 6C21 7.65685 19.6569 9 18 9H15C13.6941 9 12.5831 8.16562 12.171 7.0009L11 7C9.9 7 9 7.9 9 9L9.0009 9.17102C10.1656 9.58312 11 10.6941 11 12C11 13.3059 10.1656 14.4169 9.0009 14.829L9 15C9 16.1 9.9 17 11 17L12.1707 17.0001C12.5825 15.8349 13.6937 15 15 15H18C19.6569 15 21 16.3431 21 18C21 19.6569 19.6569 21 18 21H15C13.6941 21 12.5831 20.1656 12.171 19.0009L11 19C8.79 19 7 17.21 7 15H5C3.34315 15 2 13.6569 2 12C2 10.3431 3.34315 9 5 9H7C7 6.79086 8.79086 5 11 5L12.1707 5.00009C12.5825 3.83485 13.6937 3 15 3H18ZM18 17H15C14.4477 17 14 17.4477 14 18C14 18.5523 14.4477 19 15 19H18C18.5523 19 19 18.5523 19 18C19 17.4477 18.5523 17 18 17ZM8 11H5C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13H8C8.55228 13 9 12.5523 9 12C9 11.4477 8.55228 11 8 11ZM18 5H15C14.4477 5 14 5.44772 14 6C14 6.55228 14.4477 7 15 7H18C18.5523 7 19 6.55228 19 6C19 5.44772 18.5523 5 18 5Z"></path></svg>`,
                title: 'summarize',
            },
        ]
        },
        image: {
          bubbleMenuItems: [
            {
              id: 'image_update',
              icon: `<svg t="1738813232216" class="icon" viewBox="0 0 1028 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1679" width="200" height="200"><path d="M683.731669 1024H230.040562c-127.800312 0-223.650546-102.24025-230.040562-223.650546V346.658346c0-127.800312 102.24025-223.650546 230.040562-223.650546H511.201248c31.950078 0 57.51014 25.560062 57.51014 57.510141s-25.560062 57.51014-57.51014 57.51014H230.040562c-63.900156 0-115.020281 51.120125-115.020281 115.020281v453.691108c0 63.900156 51.120125 115.020281 115.020281 115.02028h453.691107c63.900156 0 115.020281-51.120125 115.020281-115.02028V525.578783c0-31.950078 25.560062-57.51014 57.51014-57.51014s57.51014 25.560062 57.510141 57.51014v281.160687c-6.390016 115.020281-108.630265 217.26053-230.040562 217.26053z" fill="#333333" p-id="1680"></path><path d="M355.859969 770.444181c-25.560062 0-51.120125-19.170047-57.510141-44.730109-19.170047-95.850234-38.340094-153.360374-44.730109-198.090484 0-38.340094-12.780031-95.850234 38.340094-121.410296 31.950078-12.780031 70.290172 0 89.460218 25.560062 12.780031 12.780031 19.170047 25.560062 31.950078 44.730109 19.170047 25.560062 31.950078 51.120125 57.510141 70.290172 6.390016 12.780031 19.170047 19.170047 31.950078 25.560062 25.560062-51.120125 38.340094-102.24025 38.340093-159.75039 6.390016-31.950078 31.950078-51.120125 63.900156-51.120124 31.950078 6.390016 51.120125 31.950078 51.120125 63.900156-19.170047 159.75039-57.51014 242.820593-127.800312 255.600624-51.120125 6.390016-95.850234-6.390016-127.800312-44.73011 0 25.560062 6.390016 44.730109 12.780031 63.900156 6.390016 31.950078-12.780031 63.900156-44.730109 70.290172h-12.780031zM706.096724 378.608424c-12.780031 0-31.950078-6.390016-38.340094-19.170046-25.560062-25.560062-25.560062-57.51014 0-83.070203l261.99064-261.99064c25.560062-19.170047 57.51014-19.170047 83.070203 0 19.170047 19.170047 19.170047 57.51014 0 76.680187l-261.99064 261.99064c-12.780031 19.170047-31.950078 19.170047-44.730109 25.560062z" fill="#333333" p-id="1681"></path></svg>`,
              title:t('sampleTemplate.longText.changepic') as string,
              onClick: aiEditor => {
                if (aiEditor) {
                  setisAddImage(t('sampleTemplate.longText.changepic') as string)
                  insertPicture()
                }
              }
            },
            'delete'
          ]
        },
        onSave: (editor: AiEditor) => {
          return true
        },
        onChange: aiEditor => {
          if(titlestring==''){
            updateOutLine(aiEditor)
            debouncedLogTreeData(aiEditor.getMarkdown())
          }
        },
        onFocus: (editor: AiEditor) => {
          isfuse=true;
        },
        onBlur:(editor:AiEditor)=>{
          setTimeout(() => {
            isfuse=false;
          },500);
        },
        onTransaction: (aiEditor: any) => {
          if (aiEditor && !isSpinning) {
            const aieContentElements = document.querySelectorAll('.aie-content')
            const handleClick = (event: { target: any }) => {
              if (event.target.tagName === 'IMG') {
                let textElement = event.target.parentNode.parentNode.previousElementSibling
                while (textElement) {
                  if (textElement.tagName === 'P' && textElement.textContent.trim()) {
                    setIsTitle(textElement.textContent)
                    return
                  }
                  textElement = textElement.previousElementSibling
                }
              } else {
                let textElement = event.target
                while (textElement) {
                  if (textElement.textContent && textElement.textContent.trim()) {
                    setIsTitle(textElement.textContent)
                    return
                  }
                  textElement = textElement.previousElementSibling
                  if (!textElement) {
                    let parent = textElement.parentNode
                    if (parent && parent !== textElement) {
                      textElement = parent
                    } else {
                      break
                    }
                  }
                }
              }
            }
            aieContentElements.forEach(element => {
              element.removeEventListener('click', handleClick)
              element.addEventListener('click', handleClick)
            })

            return () => {
              aieContentElements.forEach(element => {
                element.removeEventListener('click', handleClick)
              })
            }
          }
        },
        onCreated: editor => {
          updateOutLine(editor)
          setLession(editor)
        },
        footerPoweredText: (count: any) => {
           return `${t('sampleTemplate.longText.aiGenerated')}　${t('sampleTemplate.longText.numberName')}：${count}`
        },
        toolbarKeys: [
          {
            icon: '<svg class="icon-GoBack" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M7.82843 10.9999H20V12.9999H7.82843L13.1924 18.3638L11.7782 19.778L4 11.9999L11.7782 4.22168L13.1924 5.63589L7.82843 10.9999Z"></path></svg>',
            onClick: (event, editor) => {
              closemodel()
              Editor.clear()
              Editor.destroy()
            },
            tip:t('sampleTemplate.longText.back') as string
          },
          'heading',
          'font-size',
          'bold',
          'italic',
          'underline',
          'font-color',
          {
            icon: '<svg t="1738743665853" class="icon-Img" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4689" width="256" height="256"><path d="M938.666667 553.92V768c0 64.8-52.533333 117.333333-117.333334 117.333333H202.666667c-64.8 0-117.333333-52.533333-117.333334-117.333333V256c0-64.8 52.533333-117.333333 117.333334-117.333333h618.666666c64.8 0 117.333333 52.533333 117.333334 117.333333v297.92z m-64-74.624V256a53.333333 53.333333 0 0 0-53.333334-53.333333H202.666667a53.333333 53.333333 0 0 0-53.333334 53.333333v344.48A290.090667 290.090667 0 0 1 192 597.333333a286.88 286.88 0 0 1 183.296 65.845334C427.029333 528.384 556.906667 437.333333 704 437.333333c65.706667 0 126.997333 16.778667 170.666667 41.962667z m0 82.24c-5.333333-8.32-21.130667-21.653333-43.648-32.917333C796.768 511.488 753.045333 501.333333 704 501.333333c-121.770667 0-229.130667 76.266667-270.432 188.693334-2.730667 7.445333-7.402667 20.32-13.994667 38.581333-7.68 21.301333-34.453333 28.106667-51.370666 13.056-16.437333-14.634667-28.554667-25.066667-36.138667-31.146667A222.890667 222.890667 0 0 0 192 661.333333c-14.464 0-28.725333 1.365333-42.666667 4.053334V768a53.333333 53.333333 0 0 0 53.333334 53.333333h618.666666a53.333333 53.333333 0 0 0 53.333334-53.333333V561.525333zM320 480a96 96 0 1 1 0-192 96 96 0 0 1 0 192z m0-64a32 32 0 1 0 0-64 32 32 0 0 0 0 64z" fill="#000000" p-id="4690"></path></svg>',
            onClick: (event, aiEditor) => {
              setisAddImage(t('sampleTemplate.longText.insertedpic') as string)
              insertPicture()
            },
            tip:t('sampleTemplate.longText.insertedpic') as string
          }//,
          // {
          //   icon: '<svg t="1736412794541" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12592" width="200" height="200"><path d="M526.5 379.9c5.2-36.7-24.1-35.6-25.8-12.8-1.2 15.7 0.3 51.1 9.3 88.6 7-29.7 13.6-44.3 16.5-75.8zM517.8 535.1c-24.9 72.7-53.1 126.6-76.7 170.7 6.4-1.1 15.1-2.8 25-4.6 18.9-3.5 42.2-7.9 63.4-10.9l27.6-4.3c15-2.4 29.1-4.6 42.1-6.1-17.2-22.8-31.8-47.6-56.2-91.7-10.8-19.9-18.1-36.3-25.2-53.1zM264.2 836.1c-0.6 6.2 2.1 11.3 7.7 14.3 2.4 1 5.2 1.6 8.1 1.6 19.6 0 47.1-27.7 81.7-82.4-68.8 22.5-95.7 48.7-97.5 66.5zM649.1 710.2c34.6 31.8 61.8 39.5 86.9 40.5 25.1 1 25-12.5 22.3-16.2-3.3-4.7-33.4-23.7-109.2-24.3z" p-id="12593"></path><path d="M640 42.7H170.7c-23.6 0-42.7 19.1-42.7 42.7v853.3c0 23.6 19.1 42.7 42.7 42.7h682.7c23.6 0 42.7-19.1 42.7-42.7v-640C796 198.7 740 142.6 640 42.7z m143 716.4c-5.6 9.8-18.7 19.9-45.4 17.2-36.3-3.7-73.1-21.5-112-65.7-19.4 3.5-48.7 6.5-83.5 11.4-48.3 7.1-91.4 15.5-128 25-87.7 183.9-157.5 139.1-171.5 117.9-10.5-16-31.4-100.6 157.9-144.4 28.6-64 80.5-172 98.4-231.3-18.5-45.4-27.3-89.4-25.5-123 0.8-16.5 4.8-60.8 42.6-60.8 47.1 0 59.7 69.1 10.1 195.5 9.6 21.5 19.4 47.1 37.7 80.2 5.2 9.4 9.4 17.9 13.5 26.1 11.4 22.8 21.3 42.4 46 71.6 87.6-5.7 145.6 23.1 157.6 40.5 8.4 12.4 9.2 27.2 2.1 39.8zM640 298.7V128l170.7 170.7H640z" p-id="12594"></path></svg>',
          //   onClick: (event, editor) => {
          //     let title = ''
          //     if (editor.getOutline() && editor.getOutline()[0].text) {
          //       title = editor.getOutline()[0].text
          //     } else {
          //       title = ''
          //     }
          //     Export(item.id, 'pdf', title)
          //   },
          //   tip:t('sampleTemplate.longText.pdfout') as string
          // },
          // {
          //   icon: '<svg t="1736413129072" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14508" width="200" height="200"><path d="M725.333333 810.666667h128V213.333333h-128V128h170.666667a42.666667 42.666667 0 0 1 42.666667 42.666667v682.666666a42.666667 42.666667 0 0 1-42.666667 42.666667h-170.666667v-85.333333zM121.984 122.752l536.32-76.586667a21.333333 21.333333 0 0 1 24.362667 21.077334v889.514666a21.333333 21.333333 0 0 1-24.32 21.077334L121.941333 901.248a42.666667 42.666667 0 0 1-36.650666-42.24V164.992a42.666667 42.666667 0 0 1 36.650666-42.24zM170.666667 202.026667v619.946666l426.666666 60.970667V141.056L170.666667 202.026667zM469.333333 341.333333h85.333334v341.333334h-85.333334l-85.333333-85.333334-85.333333 85.333334H213.333333V341.333333h85.333334l0.426666 213.333334L384 469.333333l85.333333 84.864V341.333333z" fill="#000000" p-id="14509"></path></svg>',
          //   onClick: (event, editor) => {
          //     let title = ''
          //     if (editor.getOutline() && editor.getOutline()[0].text) {
          //       title = editor.getOutline()[0].text
          //     } else {
          //       title = ''
          //     }

          //     Export(item.id, 'docx', title)
          //   },
          //   tip:t('sampleTemplate.longText.wordout') as string
          // }
        ],
        content: cleanedItem.join('\n'),
        backend: {
          endpoint: 'http://aieditor-pro-api.jpress.cn'
        },
        onDestroy: editor => {
          if (editor) {
            editor.clear()
          }
        }
      })
      editorRef.current = Editor
      return () => {
        Editor.destroy()
        editorRef.current = null
      }
    }
    isfuse=false;
    cancleAbstract();
    setCurcontent('');
    setcurarr([-1,-1]);
    titlestring='';
  }, [isOpen, AiEditorRef])

  useEffect(() => {
    let previousContent = editorRef.current?.getMarkdown() || ''
    const intervalId = setInterval(() => {
      if (editorRef.current) {
        const currentContent = editorRef.current.getMarkdown()
        if (currentContent !== previousContent) {
          updateOutLine(editorRef.current)
          previousContent = currentContent
        }
      }
    }, 5000)
    return () => {
      clearInterval(intervalId)
    }
  }, [editorRef])

  useEffect(() => {
    if (editorRef.current) {
      const sections = content.replace(t('sampleTemplate.BasicModal.generatingLongtxt'), '').split('\n')
      editorRef.current.setMarkdownContent(sections.join('\n'))
    }
    setParentItem(item);
  }, [item])

  useEffect(() => {
    if (workflowProcess?.status === 'running') {
      setIsSpinning(true)
    }
    if (workflowProcess?.status === 'succeeded') {
      setIsSpinning(false)
    }
  }, [workflowProcess?.status])

  const insertPicture = () => {
    setisImageModalLoading(true)
    setisImageModalOpen(true)
  }
  const handleImageModalOk = () => {
    if (SelectedImage) {
      const imgs = `![](${SelectedImage})`
      editorRef.current?.insertMarkdown(imgs)
      setisImageModalOpen(false)
    } else {
      message.error('请先选择图片')
    }
  }
  const handleImageModalCancel = () => {
    setisImageModalOpen(false)
    setisImageModalLoading(true)
  }

  const handleImageafterOpenChange = () => {
    setImageCollection([])
    const dat = {
      request_type: t('sampleTemplate.interface.picture')
    }
    const data = {
      conversation_id: '',
      inputs: {
        sample: JSON.stringify(dat)
      },
      parent_message_id: null,
      query: isTitle || beforeTextRef.current,
      response_mode: 'streaming'
    }
    fetchWorkflowsRun(data).then((res: any) => {
      if (res) {
        setisImageModalLoading(false)
        setImageCollection(JSON.parse(res.replace(/'/g, '"')))
      }
    })
  }



  const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    ...restProps
  }) => {
    const [editing, setEditing] = useState(false)
    const inputRef = useRef<InputRef>(null)
    const form = useContext(EditableContext)!

    useEffect(() => {
      if (editing) {
        inputRef.current?.focus()
      }
    }, [editing])

    const toggleEdit = () => {
      setEditing(!editing)
      form.setFieldsValue({ [dataIndex]: record[dataIndex] })
    }

    const save = async () => {
      try {
        const values = await form.validateFields()

        toggleEdit()
        handleSave({ ...record, ...values })
      } catch (errInfo) {
        //console.log('Save failed:', errInfo)
      }
    }

    let childNode = children

    if (editable) {
      childNode = editing ? (
        <Form.Item
          style={{ margin: 0 }}
          name={dataIndex}
          rules={[{ required: true, message: `${title}${t('sampleTemplate.longText.isEempty')}` }]}
        >
          <Input ref={inputRef} size="small" onPressEnter={save} onBlur={save} />
        </Form.Item>
      ) : (
        <div
          className="editable-cell-value-wrap"
          style={{ paddingInlineEnd: 24 }}
          onClick={toggleEdit}
        >
          {children}
        </div>
      )
    }

    return <td {...restProps}>{childNode}</td>
  }

  const defaultColumns: (ColumnTypes[number] & { editable?: boolean; dataIndex: string })[] = [
    {
      title:t('sampleTemplate.longText.remark'),
      dataIndex: 'label',
      width: '40%',
      align: 'center',
      editable: true
    },
    {
      title:t('sampleTemplate.longText.getvalue'),
      dataIndex: 'value',
      width: '40%',
      align: 'center',
      editable: true
    },
    {
      title:t('sampleTemplate.longText.action'),
      width: '20%',
      align: 'center',
      dataIndex: 'operation',
      render: (_, record) =>
        dataSource.length >= 1 ? (
          <Popconfirm title={t('sampleTemplate.longText.delline')} onConfirm={() => handleDelete(record.key)}>
            <a>
              <DeleteOutlined />
            </a>
          </Popconfirm>
        ) : null
    }
  ]

  const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
    const [form] = Form.useForm()
    return (
      <Form form={form} component={false}>
        <EditableContext.Provider value={form}>
          <tr {...props} />
        </EditableContext.Provider>
      </Form>
    )
  }

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell
    }
  }

  const headers = () => {
    const sharedToken = globalThis.location.pathname.split('/').slice(-1)[0]
    const accessToken = localStorage.getItem('token') || JSON.stringify({ [sharedToken]: '' })
    let accessTokenJson = { [sharedToken]: '' }
    try {
      accessTokenJson = JSON.parse(accessToken)
    } catch (e) {
      console.error('Token parsing error:', e)
    }
    return {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessTokenJson[sharedToken]}`
    }
  }

  const handleDelete = (key: React.Key) => {
    const newData = dataSource.filter(item => item.key !== key)
    setDataSource(newData)
  }

  const columns = defaultColumns.map(col => {
    if (!col.editable) {
      return col
    }
    return {
      ...col,
      onCell: (record: DataType) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        handleSave
      })
    }
  })

  const handleAdd = () => {
    const newData: DataType = {
      key: count,
      label: `${t('sampleTemplate.longText.remark')} ${count}`,
      value: Number(Math.floor(Math.random() * 100).toString()).toFixed(0)
    }
    setDataSource([...dataSource, newData])
    setCount(count + 1)
  }

  const handleSave = (row: DataType) => {
    const newData = [...dataSource]
    const index = newData.findIndex(item => row.key === item.key)
    const item = newData[index]
    newData.splice(index, 1, {
      ...item,
      ...row
    })
    setDataSource(newData)
  }

  const handleEchart = () => {
    if (chartName.trim() === '') {
      message.error(t('sampleTemplate.longText.Placeholder1'))
      return
    }
    if (chartType === undefined) {
      message.error(t('sampleTemplate.longText.Placeholder2'))
      return
    }
    const labels = dataSource.map(item => item.label).join(';')
    const values = dataSource.map(item => item.value).join(';')
    const dat = {
      request_type: t('sampleTemplate.interface.chart'),
      chart_data: {
        x_axis: labels,
        data: values
      },
      chart_type: chartType
    }
    const data = {
      conversation_id: '',
      inputs: {
        sample: JSON.stringify(dat)
      },
      parent_message_id: null,
      query: chartName,
      response_mode: 'streaming'
    }
    fetchWorkflowsRun(data)
      .then((res: any) => {
        editorRef.current?.insertMarkdown(res)
        setChartName('')
        setChartType(undefined)
        setDataSource([
          {
            key: '0',
            label: t('sampleTemplate.longText.remark1'),
            value: '100'
          },
          {
            key: '1',
            label: t('sampleTemplate.longText.remark2'),
            value: '56'
          }
        ])
      })
      .catch(err => {
        //console.log(err)
      })
  }

  const handleEchartClear = () => {
    setChartName('')
    setChartType(undefined)
    setDataSource([
      {
        key: '0',
        label: t('sampleTemplate.longText.remark1'),
        value: '100'
      },
      {
        key: '1',
        label:t('sampleTemplate.longText.remark2'),
        value: '56'
      }
    ])
  }

  const fileTypeToAvatarMap: Record<string, string> = {
    '.docx': '/assets/image/word.png',
    '.doc': '/assets/image/word.png',
    '.pdf': '/assets/image/pdf.png',
    '.txt': '/assets/image/txt.png',
    '.xls': '/assets/image/excel.png',
    '.xlsx': '/assets/image/excel.png'
  }

  const getAvatarForFileType = (fileName: string): string => {
    const extension = fileName.slice(fileName.lastIndexOf('.')).toLowerCase()
    return fileTypeToAvatarMap[extension] || '/assets/image/default.png' // 默认图标
  }

  const afterOpenChange = async (bool: boolean) => {
    if (bool) {
      const res = (await fetchConversationsVariables(
        currentConversationId,
        'outline_reference'
      )) as Record<string, ConversationVariable>

      if (res && res.data) {
        setLongModalDatas(res.data)
      }
      if (item.content) {
        const sections = content.replace(t('sampleTemplate.BasicModal.generatingLongtxt'), '').split('\n')
        let found = false
        for (const line of sections) {
          if (line.startsWith('# ')) {
            const title = line.substring(1).trim()
            if (title.includes('《') && title.includes('》')) {
              beforeTextRef.current = title
              found = true
              break
            }
          }
        }
      }
    }
  }

  useEffect(() => {
    if (isOpen) {
      if(chatList.length==0){
        setTimeout(() => {
          if(chatList.length==0){
            handleUpdateChatList(chatList2);
          }
        },500);
      }else if(chatList.length<chatList2.length){
          handleUpdateChatList(chatList2);
      }
      getHistory()
      setHisIds([0])
      let nowtimes=Date.now();
      idtimesvalue=nowtimes;
    }
  }, [!isSpinning, isOpen])


  // 处理历史版本
  const arabicToChinese = (num:any)=>{
    const chineseNumbers = t('sampleTemplate.longText.chineseNumbers').split(',');
    const units = ['',...t('sampleTemplate.longText.units').split(',')];
    num = String(num);
    let result = '';
    const len = num.length;
    for (let i = 0; i < len; i++) {
        const digit = parseInt(num[i]);
        const unitIndex = len - i - 1;

        if (digit === 0) {
            if (unitIndex % 4 === 0) {
                // 处理万、亿等单位
                result += units[unitIndex];
            }
            if (i < len - 1 && parseInt(num[i + 1]) !== 0 && !result.endsWith('零')) {
                result += chineseNumbers[0];
            }
        } else {
            result += chineseNumbers[digit] + units[unitIndex];
        }
    }

    // 处理一些特殊情况，如 一十 简化为 十
    result = result.replace(/^一十/, '十');

    // 去除末尾多余的零
    result = result.replace(/零+$/, '');

    return result;
}

  const getHistory = async () => {
    const res:any = await GetHistroyList(item.id,false);
    if (res && res.list) {
      if(res.list.length==1 && res.list[0].versions.length==0){}else{
        setHisList(res.list)
      }
    }
  }


//上版本聊天列表获取
// const chatlistFn = async () => {
//   try {
//     const res = (await handleChatList(currentConversationId)) as unknown as { data: any }
//     if (res && res.data) {
//       setIschatList(updateChatList(chatList2, res.data))
//       cclist=updateChatList(chatList2, res.data);
//     }
//   } catch (error) {
//     console.error('获取聊天列表失败:', error)
//   }
// }
// const updateChatList = (chatlist: ChatItem[], ischatlist: IsChatItem[]): ChatItem[] => {
//   return chatlist.map(chatItem => {
//     const correspondingItem = ischatlist.find(item => item.id === chatItem.id)
//     if (correspondingItem && chatItem.content !== correspondingItem.answer) {
//       return {
//         ...chatItem,
//         content: correspondingItem.answer
//       }
//     }
//     return chatItem
//   })
// }
  //hq 判断是否以生成长文
  useEffect(() => {
    if (currentChatInstanceRef.current) {
      currentChatInstanceRef.current.handleStop = handleStop
      currentChatInstanceRef.current.handleRestart = handleRestart
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  useEffect(() => {}, [chatList])
  //hq chat发送信息
  let chatData: ChatData = {}
  const doSend: OnSend = useCallback(
    (message, files, last_answer) => {
      const data: any = {
        query:message,
        files,
        inputs: currentConversationId ? currentConversationItem?.inputs : newConversationInputs,
        conversation_id: currentConversationId,
        parent_message_id: last_answer?.id || getLastAnswer(chatListRef.current)?.id || null
      }
      if (
        Object.keys(chatAllTypeResult.form || {}).length > 0 &&
        data.inputs &&
        'sample' in data.inputs
      ) {
        chatData = {
          write_type: chatAllTypeResult.type?.text || '',
          write_theme: chatAllTypeResult.form?.Theme || '',
          word_count: chatAllTypeResult.form?.Number || '',
          requirement: chatAllTypeResult.form
        }
        if (chatAllTypeResult.type?.text === t('sampleTemplate.IntelligentAgent.CreationTypeTemplate') && chatAllTypeResult.files) {
          const sourceFiles = chatAllTypeResult.files
          const filesLong = {
            url: '',
            id: sourceFiles.id || sourceFiles.file_id,
            transferMethod: 'template_file',
            supportFileType: 'document',
            uploadedId: sourceFiles.id || sourceFiles.file_id
          }
          data.inputs.template_file = filesLong
        }
      }
      if (data.inputs &&
          'sample' in data.inputs && message && message === t('sampleTemplate.Generate.GenerateDocument')) {
          chatData.request_type = t('sampleTemplate.Generate.GenerateDocument') as string
        }
      if (data.inputs &&
        'sample' in data.inputs) {
        data.inputs.sample = JSON.stringify(chatData)
      }
      handleSend(getUrl('chat-messages', isInstalledApp, appId || ''), data, {
        onGetConversationId: conversationId => {
          handleAutoRename(conversationId)
          setCurrentChatConversationId(conversationId)
        },
        onGetSuggestedQuestions: responseItemId =>
          fetchSuggestedQuestions(responseItemId, isInstalledApp, appId),
        onConversationComplete: currentConversationId ? undefined : handleNewConversationCompleted,
        isPublicAPI: !isInstalledApp
      })
    },
    [
      chatListRef,
      appConfig,
      currentConversationId,
      currentConversationItem,
      handleSend,
      newConversationInputs,
      handleNewConversationCompleted,
      isInstalledApp,
      appId,
      chatAllTypeResult,
      ischatList
    ]
  )
  const doRegenerate = useCallback(
    (chatItem: ChatItem) => {
      const index = chatList.findIndex(item => item.id === chatItem.id)
      if (index === -1) return

      const prevMessages = chatList.slice(0, index)
      const question = prevMessages.pop()
      const lastAnswer = getLastAnswer(prevMessages)

      if (!question) return

      handleUpdateChatList(prevMessages)
      doSend(question.content, question.message_files, lastAnswer)
    },
    [chatList, handleUpdateChatList, doSend]
  )

   const chatNode = useMemo(() => {
    if (inputsForms.length > 0) {
      return <>{!currentConversationId && <ConfigPanel />}</>
    }
    return null
  }, [currentConversationId, inputsForms, currentConversationItem, isMobile])

   const insertMarkDown = (item:any)=>{
     if(item && item.data && item.data.content){
        let mark = item.data.content.replace(/正在生成长文\.\.\.\n/g, '').split('\n').join('\n');
        if(isfuse){
          editorRef.current?.focus().insertMarkdown(mark)
        }else{
          message.error("请定位插入位置");
        }
      }
  }


  const items: TabsProps['items'] = [
    {
      key: '1',
      label:t('sampleTemplate.longText.creative'),
      children: (
        parentItem ?
        <div className="rightDiv">
           <Chat
            appData={appData}
            answerIcon={appData?.site.icon_url || ''}
            config={appConfig}
            chatList={chatList.length > 0 ? chatList : ischatList}
            isResponding={isResponding}
            chatFooterClassName='bottom-[36px]'
            onSend={doSend}
            islong={islong}
            inputs={
              currentConversationId ? (currentConversationItem?.inputs as any) : newConversationInputs
            }
            inputsForm={inputsForms}
            onRegenerate={doRegenerate}
            onStopResponding={handleStop}
            chatNode={chatNode}
            allToolIcons={appMeta?.tool_icons || {}}
            onFeedback={handleFeedback}
            suggestedQuestions={suggestedQuestions}
            hideProcessDetail
            noChatInput={!!longTxtForm}
            themeBuilder={themeBuilder}
            chartHasPrev={chartHasPrev}
            currentConversationId={currentConversationId}
            islongModel={true}
            changePre={(e:any) => {
              closemodel()
            }}
            actionfn={(e:any) => {
              insertMarkDown(e)
            }}
            parentItem={parentItem}
          />
        </div>
        :null
      )
    },
    // {
    //   key: '2',
    //   label:t('sampleTemplate.longText.editorial'),
    //   children: (
    //     <div className="p-5">
    //       <div className="mb-3">
    //         <div className="text-base font-bold mb-3">{t('sampleTemplate.interface.chart')}</div>
    //         <Input
    //           placeholder={t('sampleTemplate.longText.Placeholder1') as string}
    //           value={chartName}
    //           onChange={e => setChartName(e.target.value)}
    //         />
    //       </div>
    //       <div>
    //         <div className="text-base font-bold mb-3">{t('sampleTemplate.longText.chatType')}</div>
    //         <Select
    //           value={chartType}
    //           onChange={value => setChartType(value)}
    //           style={{ width: '100%', marginBottom: '10px' }}
    //           placeholder={t('sampleTemplate.longText.Placeholder2')}
    //           options={[
    //             { label:t('sampleTemplate.longText.type1'), value: 'bar' },
    //             { label:t('sampleTemplate.longText.type2'), value: 'pie' },
    //             { label:t('sampleTemplate.longText.type3'), value: 'line' }
    //           ]}
    //         />
    //       </div>
    //       <div>
    //         <div className="text-base font-bold mb-3">{t('sampleTemplate.longText.chartParameters')}</div>
    //         <Button onClick={handleAdd} type="primary" style={{ marginBottom: 10 }}>
    //         {t('sampleTemplate.longText.addline')}
    //         </Button>
    //         <Table<DataType>
    //           components={components}
    //           rowClassName={() => 'editable-row'}
    //           bordered
    //           dataSource={dataSource}
    //           columns={columns as ColumnTypes}
    //           pagination={false}
    //         />
    //       </div>
    //       <div className="flex items-center justify-center mt-3">
    //         <Button onClick={handleEchart} type="primary" style={{ marginRight: 10 }}>
    //         {t('sampleTemplate.longText.generated')}
    //         </Button>
    //         <Button onClick={handleEchartClear} type="primary">
    //         {t('sampleTemplate.longText.emptied')}
    //         </Button>
    //       </div>
    //     </div>
    //   )
    // },
    {
      key: '3',
      label:t('sampleTemplate.longText.source'),
      children: (
        <div className="p-5">
          {
            isGdata &&  <div className='xq-flex-center xq-flex-vcenter'>
            <LoadingOutlined style={{ fontSize:16, fontWeight: 'bold',color:"#3168F5" }}  />
            </div>
          }
          {LongModalDatas.value &&
            LongModalDatas.value.map((item: any, index: number) => (
              <div key={index}>
                {item.reference_content && item.reference_content.length > 0 && (
                  <div>
                    {item.reference_content.map((refItem: any, i: number) => (
                      <div
                        className={`w-full border xqcur border-solid border-gray-300 rounded-2xl p-4 mb-3 bg-white  ${curarr[0] == index && curarr[1] == i  ? 'selectDiv' : ''}`}
                        key={i}
                        onClick={(e) => {
                          selectAbstract(item,index,i)
                        }}
                      >
                        <div className='xq-flex-cbetween'>
                        <div className="flex items-center">
                          <img
                            src={getAvatarForFileType(refItem.title)}
                            alt=""
                            className="w-5 h-5 rounded-full mr-1"
                          />
                          <div className={`text-sm font-bold mb-1 ${style.title_ly}`}>
                            {refItem.title}
                          </div>
                        </div>
                        <div className="xq-flex-end xq-flex-vcenter">
                           <AlignLeftOutlined
                                onClick={(e) => {
                                  e.stopPropagation()
                                  getAbstract(refItem,i)
                                }}
                                style={{
                                  fontSize: '16px',
                                  fontWeight: 'bold',
                                  lineHeight: '1'
                                }}
                                className="group-hover:inline-block mr-2"
                            />
                            <DownloadOutlined
                                onClick={(e) => {
                                  e.stopPropagation()
                                  LookDocumentItemDown(refItem)
                                }}
                                style={{
                                  fontSize: '16px',
                                  fontWeight: 'bold',
                                  lineHeight: '1'
                                }}
                                className="group-hover:inline-block mr-2"
                              />
                            <PaperClipOutlined
                             onClick={(e) => {
                              e.stopPropagation()
                              LookDocumentItem(refItem)
                              }}
                              style={{
                                fontSize: '16px',
                                fontWeight: 'bold',
                                lineHeight: '1'
                              }}
                              className="group-hover:inline-block mr-2"
                            />
                         </div>
                        </div>
                        {
                          abstract[i.toString()] && (
                            <div className="text-xs text-gray-500 zyDiv">
                              摘要:{abstract[i.toString()]}
                            </div>
                          )
                        }
                        <div className="text-xs lnDiv">{refItem.content}</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
        </div>
      )
    }
  ]


  //鉴定是否在滚动
  const setLession = (editor: AiEditor) => {
    return;
  }
 //end

  const debouncedLogTreeData = throttle(
    async data => {
      try {
        if (!isSpinning && data.length > 0) {
          if(isOpen){
            if(timeNum==0){
              timeref=setInterval(() => {
                if(timeNum>15){
                clearInterval(timeref);
                timeNum=0;
                //更新历史记录
                getHistory();
                }else{
                  timeNum++;
                }
            }, 1000);
            }else{
              timeNum=1;
            }
          }
          const res = await fetchChatListUpdate(item.id, data, '1')
          if (res.result !== 'success') {
            message.error(res.message);
          }
        }
      } catch (error) {
        console.error(t('sampleTemplate.BasicModal.modificationFailed')+':', error)
      }
    },
    1000,
    { leading: false, trailing: true }
  )

  const updateOutLine = (editor: AiEditor) => {
    const outlineContainer = document.querySelector(`#id_${item.id}_${idtimesvalue.toString()}`)
    if (outlineContainer) {
      outlineContainer.innerHTML = ''
    }
    const outlines = editor.getOutline()
    outlines.forEach(outline => {
      const outlineElement = createOutlineElement(outline, editor);
      outlineContainer?.appendChild(outlineElement)
    })
  }

  const createOutlineElement = (outline: Outline, editor: AiEditor) => {
    const child = document.createElement('div')
    child.classList.add(`aie-title${outline.level}`)
    child.style.padding = `10px 0`
    child.style.marginLeft = `${14 * (outline.level - 1)}px`
    child.innerHTML = `<a href="#${outline.id}" key=${outline.id}>${outline.text}</a>`
    // 为大纲项添加点击事件
    child.addEventListener('click', e => handleOutlineClick(e, outline, editor,child))

    return child
  }

  const handleOutlineClick = (e: MouseEvent, outline: Outline, editor: AiEditor,cuele:any) => {
    cancleAbstract();
    e.preventDefault()
    e.stopPropagation()


     //目录展示及实时定位
     islisten=true
     if (cuele.parentNode) {
      const siblings = cuele.parentNode.children;
      for (let i = 0; i < siblings.length; i++) {
        if (siblings[i] instanceof HTMLElement && siblings[i]!== cuele) {
          siblings[i].classList.remove('active');
        }
      }
    }
    cuele.classList.add('active');
    //end

    const el =
      typeof window !== 'undefined'
        ? editor.innerEditor.view.dom.querySelector(`#${outline.id}`)
        : null

    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' })
      setTimeout(() => {
        editor.focusPos(outline.pos + outline.size - 1)
          //新增
          setTimeout(() => {
            islisten=false
          },700);

      }, 1000)
    } else {
      console.warn(`Element with id ${outline.id} not found.`)
    }
  }

  const Export = async (messageId: string, type: string, title: string) => {
    message.warning(`${t('sampleTemplate.longText.loadingmsg')}`)
    fetch(`${PUBLIC_API_PREFIX}/download-file-get?message_id=${messageId}&file_type=${type}`, {
      method: 'GET',
      headers: headers()
    })
      .then(response => {
        // 检查响应状态
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        // 转换为 Blob 并下载文件
        response.blob().then(blob => {
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = title+`${t('sampleTemplate.longText.aiGenerated')}`+ '.' + type // 使用提取的文件名
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          URL.revokeObjectURL(url)
        })
      })
      .catch(error => {
        console.error(t('sampleTemplate.longText.loadingerror'), error)
      })
  }

  const isImageListModel = useMemo(() => {
    return (
      <>
        {ImageCollection && ImageCollection.length > 0 ? (
          <div className="Image_model">
            <CheckCard.Group
              onChange={(value: any) => {
                setSelectedImage(value)
              }}
            >
              {ImageCollection.map((item: any, index: number) => (
                <CheckCard key={index} value={item} cover={<img alt="" src={item} />} />
              ))}
            </CheckCard.Group>
          </div>
        ) : (
          <div
            className="Image_model"
            style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
          >
            {!isImageModalLoading && ImageCollection.length === 0 ? (
              <Empty
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'column',
                  width: '600px',
                  height: '500px'
                }}
              />
            ) : (
              <Skeleton.Image style={{ width: '600px', height: '500px' }} active />
            )}
          </div>
        )}
      </>
    )
  }, [ImageCollection])


  //hq 导出判断方法
  const onClickFn: MenuProps['onClick'] = ({ key }) => {
    cancleAbstract();
    let title = ''
    if (editorRef.current?.getOutline() && editorRef.current?.getOutline()[0].text) {
      title = editorRef.current?.getOutline()[0].text
    } else {
      title = ''
    }
    if (key === '1') {
      Export(item.id, 'pdf', title)
      return
    }else if (key === '2') {
      Export(item.id, 'docx', title)
      return
    }
  };

  const selectRecord = (index: number, indexc: number,ids:any) => {
     setisHistory(false);
     getHistoryDetail(ids)
  }
  const selectPref = async(index: number) => {
    let pos = index+1;
    let id=0;
    let endId
    if(pos==HisList.length){
      id=HisList[index].versions[0].id;
      endId=HisList[index].versions[HisList[index].versions.length-1].id;
    }else{
      id=HisList[index].versions[0].id;
      endId=HisList[pos].versions[0].id;
    }
    const res:any = await GetHistroyDetail(id,endId,false);
    if (res && res.data) {
        sethistoryItem(res.data);
        setTimeout(() => {
          setisHistoryOpen(true);
        },500);
    }
 }
  const revertfn = async (id:any) => {
    const res:any = await GetHistroyDetail(id,0,false);
    if (res && res.data) {
        editorRef.current?.setMarkdownContent(res.data.content);
    }
  }

  const getHistoryDetail = async (id:any) => {
    const res:any = await GetHistroyDetail(id,0,false);
    if (res && res.data) {
      sethistoryItem(res.data);
      setTimeout(() => {
        setisHistoryOpen(true);
      },500);
    }
  }

  const showRecord = () => {
    if (!isHistory) {

    }
    setisHistory(!isHistory);
  }

  const replaceTime = (item:any) => {
    return item.replace(/T/g, ' ').replace(/\.[0-9]{3}Z/, '');
  }

  const reshtml=(list:any[],index:any)=>{
    return (
       list.map((childItem: any, indexc: number) => {
            return (
              <div className='chidsmall xq-flex-cbetween'  key={childItem.id || indexc}>
                <div onClick={()=>selectRecord(index,indexc,childItem.id)}>{t('sampleTemplate.longText.savetime')} {replaceTime(childItem.created_at)}</div>
                <div className="xq-flex-end xq-flex-vcenter rollback" onClick={()=>{revertfn(childItem.id)}}>
                  <RollbackOutlined  />{t('sampleTemplate.longText.reduction')}
                </div>
              </div>
          )
        })
    )
  }
  const versioName=(index:any)=>{
    let name = arabicToChinese(index);
    name=t('sampleTemplate.longText.the')+name+t('sampleTemplate.longText.version');
    return name;
  }

  const changetab=(index:any)=>{
      let ids = [...HisIds];
      let pos = ids.indexOf(index);
      if (pos > -1) {
        ids.splice(pos, 1); // 从 pos 位置开始删除一个元素
      }else{
        ids.push(index);
      }
      setHisIds(ids);
  }

  useEffect(() => {
    const handleMouseLeave = (e: MouseEvent) => {
      if (isHistory) {
        setisHistory(false);
      }
    };
    const hisChildElement = document.querySelector('.hischild');
    if (isHistory && hisChildElement) {
      hisChildElement.addEventListener('mouseleave', handleMouseLeave);
    }
    return () => {
      if (hisChildElement) {
        hisChildElement.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, [isHistory]);

  const LookDocumentItem = async (item: any) => {
    const res = await GetreferencefileId(item.doc_id)
    if (res) {
      const data = {
        file_name: item.title,
        file_path: `${TEMPLATE_URL}files/${res}/file-preview-look`,
        file_id: res
      }
      setDrawerData(data)
      setOpen(true)
    }
  }
  const LookDocumentItemDown = async (item: any) => {
    const res = await GetreferencefileId(item.doc_id)
    if (res) {
      let type="docx";
      let ar = item.title.split('.');
      let title = item.title;
      if(ar.length>1){
        type=ar[1];
        title=ar[0];
      }
      let pathurl = `${TEMPLATE_URL}files/${res}/file-preview-look`
      const a = document.createElement('a')
      a.href = pathurl
      a.download = title+ '.' + type // 使用提取的文件名
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
  }
  const getAbstract = async (item: any,index:Number) => {
    if(abstract && abstract[index.toString()]){

    }else{
      setIsGdata(true);
      let linshi={...abstract}
      if(abstractDetail && abstractDetail[item.doc_id]){
        linshi[index.toString()]=abstractDetail[item.doc_id];
        setAbstract(linshi);
      }else{
        const res:any = await GetSummary(item.doc_id,false);
        if (res && res.text) {
        let txtdetail = res.text;
        linshi[index.toString()]=txtdetail;
        setAbstract(linshi);
        let Detail = {...abstractDetail};
        Detail[item.doc_id]=txtdetail;
        setAbstractDetail(Detail);
        }
      }
      setIsGdata(false);
    }
  }
  const removeHashes = (str: string): string => {
    return str.replace(/#/g, '');
  };
  const countHashes = (str: string): number => {
    const hashMatches = str.match(/#/g);
    return hashMatches ? hashMatches.length : 0;
  };
  const hasMarkdownTable = (text:any) => {
    const regex = /^\s*\|(.*?)\|$/;  // 匹配 |...| 结构
    const match = text.match(regex);
    if (match){
      return true;
    }else{
      return false;
    }
  }
  const cancleAbstract=()=>{
    let content=curcontent?curcontent:curcontentdetail;
    if(content){
        editorRef.current?.setMarkdownContent(content);
        setTimeout(() => {
          titlestring="";
          curcontentdetail="";
          setCurcontent('');
          setcurarr([-1,-1]);
        },500);
    }
  }
  const selectAbstract=(item:any,index:any,i:any)=>{
      let tel = item.outline_content;
      let arr=[index,i];
      if(curarr[0]==index && curarr[1]==i){
        cancleAbstract();
        return;
      }else{
        setcurarr(arr);
      }
      if(tel==titlestring){
        return;
      }else{
        let mark = editorRef.current?.getMarkdown();
        let content=curcontent?curcontent:curcontentdetail;
        if(content){
          mark=content
        }else{
          setCurcontent(mark);
          curcontentdetail=mark;
        }
        titlestring=tel;
        if(mark){
          let markarr = mark.replace(new RegExp(`${t('sampleTemplate.longText.longtxt') as string}\.\.\.\n`, 'g'), '').split('\n');
          let poslevel=-1;
          let havetel=false;
          markarr.forEach((e:any,index:any)=>{
            let textstringh=removeHashes(e);
            let num =countHashes(e);
            let istable=hasMarkdownTable(e);
            if(num>0 && e.includes(tel) && !havetel){
              havetel=true;
              poslevel=num;
              let strj="";
              for(let i=0;i<num;i++){
                strj+="#";
              }
              e=strj+`  <mark data-color="#f7cde1" style="background-color:#f7cde1; color:inherit">`+textstringh+`</mark>`;
              markarr[index]=e;
            }else if(poslevel==-1){

            }else if(poslevel!=-1 && havetel && (num==0 || num>poslevel) && !istable){
              let strj="";
              for(let i=0;i<num;i++){
                strj+="#";
              }
              if(!istable){
                  if(num==0){
                    e=` <mark data-color="#f7cde1" style="background-color:#f7cde1; color:inherit">`+e+`</mark> `;
                  }else{
                    e=strj+`  <mark data-color="#f7cde1" style="background-color:#f7cde1; color:inherit">`+textstringh+`</mark>`;
                  }
                  markarr[index]=e;
              }
            }else if(poslevel!=-1 && havetel && num==poslevel){
              poslevel=-1;
            }
          })
          editorRef.current?.setMarkdownContent(markarr.join('\n'))
        }
      }
  }
  const closemodel=async()=>{
    if(timeref){
      clearInterval(timeref);
      timeNum=0;
      timeref=null;
    }
    LongChatModelClose();
    setCurcontent('');
    setcurarr([-1,-1]);
    titlestring='';
  }
  const onClose = () => {
    setOpen(false)
  }
  return (
    <>
      <div className="long-modal">
        <TemplateDrawer open={open} drawerData={drawerData} onClose={onClose} placement="left"></TemplateDrawer>
        <Modal
          closable={false}
          footer={null}
          open={isOpen}
          onCancel={closemodel}
          afterClose={closemodel}
          afterOpenChange={afterOpenChange}
          keyboard={false}
          className="long-text-modal"
          style={{ paddingBottom: '0' }}
        >
          <Row className="flex h-full w-full overflow-hidden">
            <Col
              span={4}
              // key={currentConversationId}
              className="w-[20%] h-[calc(100vh-20px)] p-5 aie-directory-content !overflow-y-auto"
              id={'id_' + item.id +'_'+idtimesvalue.toString()}
              style={{ background: '#fafafa' }}
            ></Col>
             <Col span={13} className="h-full border-r">
               <div className='xq-flex-cbetween xqAc'>
                <div></div>
                <div className='xq-flex-end xq-flex-vcenter'>
                {HisList.length>0 &&
                <div className='hisc' onClick={()=>showRecord()}>
                    <ClockCircleOutlined size={32} />
                    {isHistory && (
                      <div className='hischild' onClick={(e)=>{e.stopPropagation()}}>
                      <div className='hischildTel'>{t('sampleTemplate.longText.history')}</div>
                      <div className='hisproce'>
                          <div className='onetel'><div className='acq'>○</div>{t('sampleTemplate.longText.current')}</div>
                          <div className='onesmall'>{replaceTime(HisList[0].latest_update_time)}</div>
                          {HisList.map((item, index) => {
                            return (
                              <div key={index}>
                                <div className='bigTel' onClick={()=>{selectPref(index)}}><div className='acqc'>
                                 {
                                  HisIds.includes(index)?<DownOutlined onClick={(e)=>{e.stopPropagation();changetab(index)}} style={{fontSize:"12px",color:'#666'}} />:<UpOutlined onClick={(e)=>{e.stopPropagation();changetab(index)}} style={{fontSize:"12px",color:'#666'}} />
                                 }
                                 </div>{versioName(HisList.length-index)}</div>
                                {
                                  item.versions && item.versions.length > 0 && HisIds.includes(index) && (
                                  // 根据上下文推测 itemc 可能是 HisList 子项的类型，这里假设为 any，实际使用中需要根据具体类型替换
                                  // 明确指定 indexc 的类型为 number
                                  reshtml(item.versions,index)

                                  )
                                }
                              </div>
                            )
                          })}
                      </div>
                    </div>
                    )}
                    </div>
                 }
                </div>
               </div>
               <div  className="xq-full border-r" ref={AiEditorRef} id={'longWeb_' + item.id}>
                <div className="aie-header" style={{ margin: '0 auto' }}></div>
                <div className="aie-main"></div>
                <div className="aie-footer"></div>
               </div>`
            </Col>
            <Col span={7} className="h-full overflow-y-auto" style={{ background: '#fafafa' }}>
              <Affix offsetTop={0}>
                <Tabs onChange={()=>{cancleAbstract()}} defaultActiveKey="1" tabBarGutter={50} items={items} />
              </Affix>
            </Col>
          </Row>
        </Modal>
      </div>
      <Modal
        title={isAddImage}
        style={{ marginBottom: '0 !important', paddingBottom: 0 }}
        open={isImageModalOpen}
        width={660}
        onOk={handleImageModalOk}
        onCancel={handleImageModalCancel}
        afterOpenChange={open => {
          if (open) {
            handleImageafterOpenChange()
          }
        }}
        keyboard={false}
        closable={false}
        okText={t('sampleTemplate.longText.inserted')}
      >
        {isImageListModel}
      </Modal>
      {
        isHistoryOpen && (
          <div className='webModel'>
           <BasicLongChatView
            isOpen={isHistoryOpen}
            item={historyItem}
            LongChatModelClose={()=>{
              setisHistoryOpen(false)
            }}
          />
         </div>
        )
      }

    </>
  )
}

export default BasicLongChatModel