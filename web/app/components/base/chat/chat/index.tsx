import type {
  FC,
  ReactNode,
} from 'react'
import {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { debounce } from 'lodash-es'
import { useShallow } from 'zustand/react/shallow'
import { useVoiceTts } from '../../voice-service/voice-tts/hooks'
import { useChatWithHistoryContext } from '../chat-with-history/context'
import ChatIntro from './chat-intro'
import Question from './question'
import Answer from './answer'
import ChatInputArea from './chat-input-area'
import TryToAsk from './try-to-ask'
import { ChatContextProvider } from './context'
import type { InputForm, ChatConfig, ChatItem, Feedback, OnRegenerate, OnSend } from '@/types/chat'
import type { Emoji } from '@/types/public/file'
import cn from '@/utils/classnames'
import AgentLogModal from '@/app/components/base/log-modal/agent'
import PromptLogModal from '@/app/components/base/log-modal/prompt'
import Loading from '@/app/components/base/loading'
import { useStore as useAppStore } from '@/app/components/app/store'
import type { AppData } from '@/types/share'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'

export type ChatProps = {
  className?: string
  // 应用数据
  appData?: AppData
  // 对话列表
  chatList: ChatItem[]
  // 正在响应的对话列表
  currentChatList?: ChatItem[]
  // 聊天配置
  config?: ChatConfig
  noStopResponding?: boolean

  // hq 判断是否以生成长文
  islong?: boolean
  islongModel?: boolean

  // 是否存在聊天输入
  noChatInput?: boolean
  inputs?: Record<string, any>
  inputsForm?: InputForm[]
  isPublicAPI?: boolean
  chatContainerClassName?: string
  chatContainerInnerClassName?: string
  chatFooterClassName?: string
  chatFooterInnerClassName?: string
  // 尝试问列表
  suggestedQuestions?: string[]
  showPromptLog?: boolean
  questionIcon?: ReactNode
  answerIcon?: string
  allToolIcons?: Record<string, string | Emoji>
  // 聊天信息前展示节点
  chatNode?: ReactNode
  onFeedback?: (messageId: string, feedback: Feedback) => void
  chatAnswerContainerInner?: string
  hideProcessDetail?: boolean
  hideLogModal?: boolean
  showFeatureBar?: boolean
  showFileUpload?: boolean
  onFeatureBarClick?: (state: boolean) => void
  chartHasPrev?: boolean
  // 是否显示底部提示
  showInputBottomTip?: boolean
  // 是否正在响应
  isResponding?: boolean
  // 是否正在语音通话
  isVoiceCall?: boolean
  // AI思考中
  isThinking?: boolean
  // 语音通话连接中
  isConnecting?: boolean
  // 加载中
  loading?: boolean
  // 发送事件
  onSend?: OnSend
  // 重新生成事件
  onRegenerate?: OnRegenerate
  // 停止响应事件
  onStopResponding?: () => void
  // 编辑标注
  onAnnotationEdited?: (question: string, answer: string, index: number) => void
  // 添加标注
  onAnnotationAdded?: (annotationId: string, authorName: string, question: string, answer: string, index: number) => void
  // 删除标注事件
  onAnnotationRemoved?: (index: number) => void
  // 语音通话事件
  onVoiceCall?: () => void
  // 语音通话取消事件
  onCancelVoiceCall?: () => void
  // 处理新增对话
  handleNewConversation?: () => void
  currentConversationId?: string
  changePre?: any
  actionfn?: any
  parentItem?: any
  // 语音播放
  onPlayVoice?: (content: string, id?: string) => void
}

const Chat: FC<ChatProps> = ({
  className,
  appData,
  config,
  onSend,
  inputs,
  isVoiceCall,
  inputsForm,
  isPublicAPI = false,
  onRegenerate,
  chatList,
  currentChatList = [],
  isResponding,
  isThinking,
  isConnecting,
  islong,
  islongModel = false,
  noStopResponding,
  onStopResponding,
  noChatInput,
  chatContainerClassName,
  chatContainerInnerClassName,
  chatFooterClassName,
  chatFooterInnerClassName,
  suggestedQuestions,
  showPromptLog,
  questionIcon,
  answerIcon,
  onAnnotationAdded,
  onAnnotationEdited,
  onAnnotationRemoved,
  chatNode,
  onFeedback,
  chatAnswerContainerInner,
  hideProcessDetail,
  hideLogModal,
  themeBuilder,
  showFeatureBar,
  showFileUpload,
  chartHasPrev = false,
  showInputBottomTip = true,
  loading,
  onFeatureBarClick,
  handleNewConversation,
  onVoiceCall,
  onCancelVoiceCall,
  changePre,
  actionfn = () => {},
  parentItem = '',
}) => {
  const {
    currentLogItem,
    setCurrentLogItem,
    showPromptLogModal,
    setShowPromptLogModal,
    showAgentLogModal,
    setShowAgentLogModal,
  } = useAppStore(
    useShallow(state => ({
      currentLogItem: state.currentLogItem,
      setCurrentLogItem: state.setCurrentLogItem,
      showPromptLogModal: state.showPromptLogModal,
      setShowPromptLogModal: state.setShowPromptLogModal,
      showAgentLogModal: state.showAgentLogModal,
      setShowAgentLogModal: state.setShowAgentLogModal,
    })),
  )
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  const chatWrapRef = useRef<HTMLDivElement>(null)
  const chatIntroRef = useRef<HTMLDivElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const chatContainerInnerRef = useRef<HTMLDivElement>(null)
  const chatFooterRef = useRef<HTMLDivElement>(null)
  const chatFooterInnerRef = useRef<HTMLDivElement>(null)
  const scrollCheckRef = useRef<boolean>(false)
  const scrollCheckTimeoutRef = useRef<NodeJS.Timeout>()

  const [selectitem, setSelectItem] = useState<any>('')
  const [BasicLongChatModels, setBasicLongChatModels] = useState(false)
  const allChatList = [...chatList, ...currentChatList]

  const {
    setIsBasicLongChatModel,
    handleChangeConversation,
    currentConversationId,
  } = useChatWithHistoryContext()

  const appId = appData?.app_id || appData?.id || ''
  // 是否是长文档应用
  const isLongWeb = Boolean(inputsForm?.find(form => form.type === 'longTxt'))
  const {
    handlePlay,
    handleStop,
  } = useVoiceTts(appId, isPublicAPI, config?.text_to_speech)
  // 聊天对话窗口宽度
  const [width, setWidth] = useState(0)
  // 用户是否滚动
  const userScrolledRef = useRef(false)
  // 是否显示滚动到底部按钮
  const [showScrollButton, setShowScrollButton] = useState(false)
  // 是否拥有尝试去问
  const hasTryToAsk
    = config?.suggested_questions_after_answer?.enabled && !!suggestedQuestions?.length && onSend
  // 对话列表为空、或者只有一条，且为开场白
  const onlyOepnState
    = allChatList.length === 0 || (allChatList.length === 1 && allChatList[0].isOpeningStatement)

  const LongChatModelClose = () => {
    handleChangeConversation(currentConversationId || '', true)
    setIsBasicLongChatModel(false)
    setBasicLongChatModels(false)
    // setTimeout(() => {
    //   // 这里可以添加你想要延迟执行的代码
    //   setIsBasicLongChatModel(true)
    //   setTimeout(() => {
    //     handleChangeConversation(currentConversationId?currentConversationId:'', true)
    //     setIsBasicLongChatModel(false)
    //   }, 500);
    // }, 500);
  }

  // 底部输入框毛玻璃
  const foooterBg = {
    background: 'rgba(240, 245, 255, 0.10)',
    backdropFilter: 'blur(25px)',
    paddingTop: '12px',
    paddingBottom: '12px',
    bottom: '0px',
  }

  // 处理滚动到底部
  const handleScrollToBottom = useCallback(() => {
    if (allChatList.length > 1 && chatContainerRef.current && !userScrolledRef.current)
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
  }, [allChatList.length])
  // 处理屏幕尺寸变化
  const handleWindowResize = useCallback(() => {
    if (chatContainerRef.current)
      setWidth(document.body.clientWidth - (chatContainerRef.current?.clientWidth + 16) - 8)
    if (chatContainerRef.current && chatFooterRef.current)
      chatFooterRef.current.style.width = `${chatContainerRef.current.clientWidth}px`
    if (chatContainerInnerRef.current && chatFooterInnerRef.current)
      chatFooterInnerRef.current.style.width = `${chatContainerInnerRef.current.clientWidth}px`
    else if (chatIntroRef.current && chatFooterInnerRef.current)
      chatFooterInnerRef.current.style.width = `${chatIntroRef.current.clientWidth}px`
  }, [])
  // 点击滚动到底部按钮
  const clickScrollToBottom = useCallback(() => {
    if (!chatContainerRef.current)
      return
    chatContainerRef.current.scrollTo({
      top: chatContainerRef.current.scrollHeight,
      behavior: 'smooth',
    })
  }, [])

  const hasNewConversationBtn = useMemo(() => {
    return !!handleNewConversation
  }, [handleNewConversation])

  useEffect(() => {
    if (chatContainerRef.current && !scrollCheckRef.current) {
      requestAnimationFrame(() => {
        handleScrollToBottom()
        handleWindowResize()
      })
    }
  })
  useEffect(() => {
    window.addEventListener('resize', debounce(handleWindowResize))
    return () => window.removeEventListener('resize', handleWindowResize)
  }, [handleWindowResize])
  useEffect(() => {
    if (chatFooterRef.current && chatContainerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { blockSize } = entry.borderBoxSize[0]

          // chatContainerRef.current!.style.paddingBottom = `${blockSize}px`
          if (chatWrapRef.current)
            chatWrapRef.current.style.paddingBottom = `${blockSize + 24}px`
          handleScrollToBottom()
        }
      })

      resizeObserver.observe(chatFooterRef.current)

      return () => {
        resizeObserver.disconnect()
      }
    }
  }, [handleScrollToBottom, loading])
  useEffect(() => {
    const chatContainer = chatContainerRef.current
    if (chatContainer) {
      const setUserScrolled = () => {
        if (chatContainer)
          userScrolledRef.current = chatContainer.scrollHeight - chatContainer.scrollTop >= chatContainer.clientHeight + 300
      }
      const handleScroll = debounce(() => {
        if (!isMobile)
          return
        if (scrollCheckTimeoutRef.current)
          clearTimeout(scrollCheckTimeoutRef.current)
        scrollCheckTimeoutRef.current = setTimeout(() => {
          const hasScroll = chatContainer.scrollHeight > chatContainer.clientHeight
          const isNotAtBottom = chatContainer.scrollHeight - chatContainer.scrollTop - chatContainer.clientHeight > 100
          scrollCheckRef.current = hasScroll && isNotAtBottom
          setShowScrollButton(hasScroll && isNotAtBottom)
        }, 100)
      }, 100)
      chatContainer.addEventListener('scroll', setUserScrolled)
      chatContainer.addEventListener('scroll', handleScroll)
      return () => {
        chatContainer.removeEventListener('scroll', setUserScrolled)
        chatContainer.removeEventListener('scroll', handleScroll)
      }
    }
  }, [isMobile, loading])

  useEffect(() => {
  }, [chatList])

  // 给子组件传值
  const childRef = useRef(null)
  const callChildMethod = (item: any) => {
    if (childRef.current)
      childRef.current.someMethod(item)
  }

  const handleLongModalOpen = async (itemchild: any) => {
    setSelectItem(itemchild)
    setIsBasicLongChatModel(true)
    setBasicLongChatModels(true)
  }
  return (
    <ChatContextProvider
      config={config}
      chatList={allChatList}
      isResponding={isResponding}
      isVoiceCall={isVoiceCall}
      isConnecting={isConnecting}
      isThinking={isThinking}
      isLongWeb={isLongWeb}
      noChatInput={noChatInput}
      showInputBottomTip={showInputBottomTip}
      showPromptLog={showPromptLog}
      questionIcon={questionIcon}
      answerIcon={answerIcon}
      onSend={onSend}
      onRegenerate={onRegenerate}
      onStopResponding={onStopResponding}
      onVoiceCall={onVoiceCall}
      onCancelVoiceCall={onCancelVoiceCall}
      onAnnotationAdded={onAnnotationAdded}
      onAnnotationEdited={onAnnotationEdited}
      onAnnotationRemoved={onAnnotationRemoved}
      onFeedback={onFeedback}
      handleNewConversation={handleNewConversation}
      onPlayVoice={handlePlay}
    >

      {loading
        ? <Loading></Loading>
        : (isLongWeb
          ? (
            <div ref={chatWrapRef} className={cn('relative h-full py-5', islongModel && 'longmodeBot', className)}>
              {/* 聊天对话容器 */}
              <div
                ref={chatContainerRef}
                className={cn(
                  'relative h-full overflow-y-auto overflow-x-hidden',
                  chatContainerClassName,
                )}
              >
                <div ref={chatContainerInnerRef} className={cn('w-full', chatContainerInnerClassName)}>
                  {chatNode}
                  {(!onlyOepnState || chartHasPrev || isResponding) && (
                    <div
                      className='w-full'
                    // className={cn('w-full midWen', chatContainerInnerClassName)}
                    >
                      {allChatList.map((item, index) => {
                        if (item.isAnswer) {
                          const isLast = item.id === allChatList[allChatList.length - 1]?.id
                          return (
                            <Answer
                              appData={appData}
                              key={item.id}
                              item={item}
                              onSend={onSend as (message: any) => void}
                              question={allChatList[index - 1]?.content}
                              index={index}
                              config={config}
                              answerIcon={answerIcon}
                              responding={isLast && isResponding}
                              chatAnswerContainerInner={chatAnswerContainerInner}
                              hideProcessDetail={hideProcessDetail}
                              noChatInput={noChatInput}
                              LongWeb={isLongWeb}
                              islongModel={islongModel}
                              onShowModel={(itemchild) => {
                                changePre(itemchild)
                              }}
                              onAc={(e) => {
                                if (e && e.type && e.type == '1')
                                  callChildMethod(e)

                                else if (e && e.type && e.type == '2')
                                  actionfn(e)
                              }}
                              parentItem={parentItem}
                            />
                          )
                        }
                        return (
                          <Question
                            key={item.id}
                            item={item}
                            index={index}
                            questionIcon={questionIcon}
                          />
                        )
                      })}
                    </div>
                  )}
                </div>
              </div>
              {/* 底部输入框 */}
              <div
                className={`flex items-center justify-center w-full bot20 ${
                  (hasTryToAsk || noChatInput || !noStopResponding) && chatFooterClassName
                } ${islongModel ? 'long-sticky z999' : 'absolute z999'}`}
                style={isMobile ? foooterBg : {}}
              >
                {/* <div ref={chatFooterInnerRef} className={cn('relative')}> */}
                <div ref={chatFooterInnerRef} className={cn('relative', chatFooterInnerClassName)}>
                  {hasTryToAsk && <TryToAsk suggestedQuestions={suggestedQuestions} onSend={onSend} />}
                  {noChatInput && (
                    <div className="">
                      <ChatInputArea
                        appId={appId}
                        showFeatureBar={showFeatureBar}
                        showFileUpload={showFileUpload}
                        featureBarDisabled={isResponding}
                        onFeatureBarClick={onFeatureBarClick}
                        visionConfig={config?.file_upload}
                        // speechToTextConfig={config?.speech_to_text}
                        inputs={inputs}
                        inputsForm={inputsForm}
                        isPublicAPI={isPublicAPI}
                        isResponding={isResponding}
                        onStopResponding={onStopResponding}
                        showInputBottomTip={showInputBottomTip}
                        isLongTxt={islong}
                        islongModel={islongModel}
                        onSend={onSend}
                        isVoiceCall={isVoiceCall}
                        onCancelVoiceCall={onCancelVoiceCall}
                        handleNewConversation={handleNewConversation}
                        isThinking={isThinking}
                        isConnecting={isConnecting}
                      />
                    </div>
                  )}
                </div>
              </div>
              {showPromptLogModal && !hideLogModal && (
                <PromptLogModal
                  width={width}
                  currentLogItem={currentLogItem}
                  onCancel={() => {
                    setCurrentLogItem()
                    setShowPromptLogModal(false)
                  }}
                />
              )}
              {showAgentLogModal && !hideLogModal && (
                <AgentLogModal
                  width={width}
                  currentLogItem={currentLogItem}
                  onCancel={() => {
                    setCurrentLogItem()
                    setShowAgentLogModal(false)
                  }}
                />
              )}
            </div>
          )
          : (
            <div ref={chatWrapRef} className={cn('relative h-full py-5', className)}>
              {/* 聊天对话容器 */}
              <div
                ref={chatContainerRef}
                className={cn(
                  'relative h-full overflow-y-auto overflow-x-hidden mb-5',
                  chatContainerClassName,
                )}
              >
                {/* 前缀节点 */}
                <div className={cn('w-full', chatContainerInnerClassName)}>
                  {chatNode}
                </div>
                {/* 对话未开启时展示界面 展示应用信息和开场白预设问题之类的内容 */}
                {onlyOepnState && !chartHasPrev && !isResponding && (
                  <ChatIntro
                    ref={chatIntroRef}
                    appConfig={config}
                    appData={appData}
                    className={chatContainerInnerClassName}
                  />
                )}
                {/* 对话列表 */}
                {(!onlyOepnState || chartHasPrev || isResponding) && (
                  <div
                    ref={chatContainerInnerRef}
                    className={cn('w-full', chatContainerInnerClassName)}
                  >
                    {chatList.map((item, index) => {
                      if (item.isAnswer) {
                        const isLast = item.id === chatList[chatList.length - 1]?.id
                        return (
                          <Answer
                            appData={appData}
                            key={item.id}
                            item={item}
                            question={chatList[index - 1]?.content}
                            index={index}
                            config={config}
                            onSend={onSend as (message: any) => void}
                            answerIcon={answerIcon}
                            responding={isLast &&isResponding}
                            chatAnswerContainerInner={chatAnswerContainerInner}
                            hideProcessDetail={hideProcessDetail}
                            noChatInput={noChatInput}
                            islongModel={islongModel}
                            onShowModel={(itemchild) => {
                              changePre(itemchild)
                            }}
                            onAc={(e) => {
                              if (e && e.type && e.type == '1')
                                callChildMethod(e)

                              else if (e && e.type && e.type == '2')
                                actionfn(e)
                            }}
                            parentItem={parentItem}
                            onPlayVoice={handlePlay}
                            onCloseVoice={handleStop}
                          />
                        )
                      }
                      return (
                        <Question
                          key={item.id}
                          item={item}
                          index={index}
                          questionIcon={questionIcon}
                        />
                      )
                    })}

                  </div>
                )}
              </div>
              {/* 底部输入框 */}
              <div
                className={`absolute ${
                  (hasTryToAsk || !noChatInput || !noStopResponding) && chatFooterClassName
                }`}
                style={isMobile ? foooterBg : {}}
                ref={chatFooterRef}
              >
                <div ref={chatFooterInnerRef} className={cn('relative', chatFooterInnerClassName)}>
                  {hasTryToAsk && (
                    <TryToAsk
                      questionsClassName={hasNewConversationBtn ? 'ml-[44px]' : ''}
                      suggestedQuestions={suggestedQuestions}
                      onSend={onSend}
                    />
                  )}
                  {!noChatInput && (
                    <ChatInputArea
                      appId={appId}
                      showFeatureBar={showFeatureBar}
                      showFileUpload={showFileUpload}
                      featureBarDisabled={isResponding}
                      onFeatureBarClick={onFeatureBarClick}
                      visionConfig={config?.file_upload}
                      // speechToTextConfig={config?.speech_to_text}
                      inputs={inputs}
                      inputsForm={inputsForm}
                      isPublicAPI={isPublicAPI}
                      voiceInput={config?.text_to_speech?.voice_input}
                      voiceConversation={config?.text_to_speech?.voice_conversation}
                      isResponding={isResponding}
                      onStopResponding={onStopResponding}
                      showInputBottomTip={showInputBottomTip}
                      onSend={onSend}
                      isVoiceCall={isVoiceCall}
                      onCancelVoiceCall={onCancelVoiceCall}
                      handleNewConversation={handleNewConversation}
                      isThinking={isThinking}
                      isConnecting={isConnecting}
                      handleStopVoiceTts={handleStop}
                    />
                  )}
                </div>
              </div>
              {showPromptLogModal && !hideLogModal && (
                <PromptLogModal
                  width={width}
                  currentLogItem={currentLogItem}
                  onCancel={() => {
                    setCurrentLogItem()
                    setShowPromptLogModal(false)
                  }}
                />
              )}
              {showAgentLogModal && !hideLogModal && (
                <AgentLogModal
                  width={width}
                  currentLogItem={currentLogItem}
                  onCancel={() => {
                    setCurrentLogItem()
                    setShowAgentLogModal(false)
                  }}
                />
              )}
            </div>
          ))}
      {/* Fix mixed operators */}
      {(isMobile && showScrollButton)
        ? (
          <div
            className='fixed right-[20px] bottom-[140px] cursor-pointer hover:opacity-80 transition-opacity'
            onClick={clickScrollToBottom}
          >
            <img src="/assets/icons/scroll-bot.png" alt="scroll to bottom" />
          </div>
        )
        : null}
    </ChatContextProvider>
  )
}

export default memo(Chat)
