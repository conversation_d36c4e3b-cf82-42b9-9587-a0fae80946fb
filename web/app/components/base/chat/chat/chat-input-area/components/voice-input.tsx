import { useTranslation } from 'react-i18next'
import SoundWave from './sound-wave/sound-wave'
import Tooltip from '@/app/components/base/tooltip'
import { XCircle } from '@/app/components/base/icons/src/vender/solid/action'
import cn from '@/utils/classnames'
type VoiceConversationProps = {
  text?: string
  onConverted: () => void
  onCancel: () => void
}
const VoiceInput = ({
  text,
  onConverted,
  onCancel,
}: VoiceConversationProps) => {
  const { t } = useTranslation()

  return (
    <div className='flex items-center justify-between w-full'>
      <div
        className='flex items-center relative grow w-full cursor-pointer'
        onClick={onConverted}
      >
        <div className='flex flex-col px-[14px] py-[12px] pr-[64px] text-S3 leading-H3'>
          <div className='flex'>
            <SoundWave className='mr-2'></SoundWave>
            <span className='text-gray-G2'>{t('common.voiceInput.inputConverting')}</span>
            <span className='text-primary-P1'>{t('common.voiceInput.inputConvertingStop')}</span>
          </div>
          <div>{text}</div>
        </div>
      </div>
      <div className={cn('absolute top-0 bottom-0 h-full flex items-center', 'right-3')}>
        <div className='flex justify-center items-center ml-2 cursor-pointer' onClick={onCancel}>
          <Tooltip popupContent={t('common.voiceInput.cancelVoiceInput')}>
            <XCircle className='w-6 h-6 text-gray-G5 hover:text-gray-G4' />
          </Tooltip>
        </div>
      </div>
    </div>
  )
}

export default VoiceInput
