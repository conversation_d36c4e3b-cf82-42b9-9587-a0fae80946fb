import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { New } from '@/app/components/base/icons/src/vender/line/chat'
import Tooltip from '@/app/components/base/tooltip'

type NewChatProp = {
  onClick?: () => void
}

const NewChat: FC<NewChatProp> = ({
  onClick,
}) => {
  const { t } = useTranslation()

  return (
    <div onClick={onClick} className='flex justify-center bg-gray-G6 items-center w-8 h-8 rounded-full border border-gray-G5 text-gray-G2 hover:bg-white hover:text-gray-G1 cursor-pointer'>
      <Tooltip popupContent={t('common.chat.newChat')}>
        <New />
      </Tooltip>
    </div>
  )
}

export default NewChat
