import {
  useCallback,
  useRef,
  useState,
} from 'react'
import type { TextAreaRef } from 'rc-textarea'
import { useTranslation } from 'react-i18next'
import type { InputForm } from '@/types/chat'
import { TransferMethod } from '@/types/model'
import { InputVarType } from '@/app/components/workflow/types'
import { useToastContext } from '@/app/components/base/toast'

export const useTextAreaHeight = () => {
  const wrapperRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<TextAreaRef>(null)
  const textValueRef = useRef<HTMLDivElement>(null)
  const holdSpaceRef = useRef<HTMLDivElement>(null)
  const [isMultipleLine, setIsMultipleLine] = useState(false)

  const handleComputeHeight = useCallback(() => {
    const textareaElement = textareaRef.current?.resizableTextArea.textArea
    if (wrapperRef.current && textareaElement && textValueRef.current && holdSpaceRef.current) {
      const { width: wrapperWidth } = wrapperRef.current.getBoundingClientRect()
      const { height: textareaHeight } = textareaElement.getBoundingClientRect()
      const { width: textValueWidth } = textValueRef.current.getBoundingClientRect()
      const { width: holdSpaceWidth } = holdSpaceRef.current.getBoundingClientRect()

      if (textareaHeight > 32) {
        setIsMultipleLine(true)
      }
      else {
        if (textValueWidth + holdSpaceWidth >= wrapperWidth)
          setIsMultipleLine(true)
        else
          setIsMultipleLine(false)
      }
    }
  }, [])

  const handleTextareaResize = useCallback(() => {
    handleComputeHeight()
  }, [handleComputeHeight])

  return {
    wrapperRef,
    textareaRef,
    textValueRef,
    holdSpaceRef,
    handleTextareaResize,
    isMultipleLine,
  }
}
// 校验输入表单
export const useCheckInputsForms = () => {
  const { t } = useTranslation()
  const { notify } = useToastContext()

  const checkInputsForm = useCallback((inputs: Record<string, any>, inputsForm: InputForm[]) => {
    let hasEmptyInput = ''
    let fileIsUploading = false
    const requiredVars = inputsForm.filter(({ required }) => required)

    if (requiredVars?.length) {
      requiredVars.forEach(({ variable, label, type }) => {
        if (hasEmptyInput)
          return

        if (fileIsUploading)
          return

        if (!inputs[variable])
          hasEmptyInput = label as string

        if ((type === InputVarType.singleFile || type === InputVarType.multiFiles) && inputs[variable]) {
          const files = inputs[variable]
          if (Array.isArray(files))
            fileIsUploading = files.find(item => item.transferMethod === TransferMethod.local_file && !item.uploadedId)
          else
            fileIsUploading = files.transferMethod === TransferMethod.local_file && !files.uploadedId
        }
      })
    }

    if (hasEmptyInput) {
      notify({ type: 'error', message: t('appDebug.errorMessage.valueOfVarRequired', { key: hasEmptyInput }) })
      return false
    }

    if (fileIsUploading) {
      notify({ type: 'info', message: t('appDebug.errorMessage.waitForFileUpload') })
      return
    }

    return true
  }, [notify, t])

  return {
    checkInputsForm,
  }
}
