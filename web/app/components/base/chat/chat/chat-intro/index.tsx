import { forwardRef, useEffect, useState } from 'react'
import type { ChatConfig } from '@/types/chat'
// import style from './styles/index.module.css'
import style from '@/app/components/base/chat/chat-with-history/styles/style.module.css'

import type { AppData } from '@/types/share'
// 公共组件
import cn from '@/utils/classnames'
import { Markdown } from '@/app/components/base/markdown'
import AppAvatar from '@/app/components/app/common/avatar'
import { useChatContext } from '@/app/components/base/chat/chat/context'
export type ChatInfoProp = {
  appData?: AppData
  appConfig?: ChatConfig | undefined
  className?: string
  styles?: React.CSSProperties
}

const ChatIntro = forwardRef<HTMLDivElement, ChatInfoProp>(({
  appData,
  appConfig,
  className,
  styles,
}, ref) => {
  const { onSend, isVoiceCall, isConnecting } = useChatContext()
  // 头像尺寸
  const [avatarSize, setAvatarSize] = useState(68)
  const chatIntroWrapRef = ref as React.RefObject<HTMLDivElement>

  useEffect(() => {
    const handleResizeEditor = () => {
      if (chatIntroWrapRef?.current) {
        const width = chatIntroWrapRef.current.clientWidth
        if (width <= 300)
          setAvatarSize(40)
        else
          setAvatarSize(68)
      }
    }
    handleResizeEditor()
    // 添加事件监听器
    window.addEventListener('resize', handleResizeEditor)
    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', handleResizeEditor)
    }
  }, [])

  return (
    <div ref={chatIntroWrapRef} className={cn(style.chatIntroWrap, className)} style={styles}>
      <div className={style.chatInroAppInfo}>
        <AppAvatar appMode={'chat'} url={appData?.site?.icon_url} size={avatarSize} className='!rounded-md m-auto' />
        <div className={style.chatIntroAppTitle}
          style={{
            color: appConfig?.background_configs?.fontColor === 'white' ? '#ffffff' : '#333333',
          }}
        >{appData?.site.title}</div>
      </div>
      {appConfig?.opening_statement && (
        <div className={cn(style.openingStatement)}>
          <Markdown content={appConfig?.opening_statement} />
        </div>
      )}
      {appConfig?.suggested_questions && !isVoiceCall && !isConnecting && (
        <div className={style.suggestedQuestionWrap}>
          {appConfig.suggested_questions.map((question, index) => (
            <div
              className={cn(appConfig?.background_configs?.fontColor ? style.hasBgSuggestedQuestion : style.suggestedQuestion)}
              onClick={() => onSend?.(question)}
              key={index}
            >
              <span>{question}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  )
})

ChatIntro.displayName = 'ChatIntro'

export default ChatIntro
