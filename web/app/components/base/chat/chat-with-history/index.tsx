import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import {
  memo,
  useEffect,
  useState,
} from 'react'
import { useSearchParams } from 'next/navigation'
import { ChatWithHistoryContext, useChatWithHistoryContext } from './context'
import { useChatWithHistory } from './hooks'
import Sidebar from './sidebar'
import Header from './header'
import ChatWrapper from './chat-wrapper'
import style from './styles/style.module.css'
import type { InstalledApp } from '@/types/app'
import type { AppMarket } from '@/types/app-market'
import { APP_NAME } from '@/config'
import { getMarketAppData } from '@/service/market'
// 公共能力
import Loading from '@/app/components/base/loading'
import AppUnavailable from '@/app/components/base/app-unavailable'
import { useSystemContext } from '@/context/system-context'

type ChatWithHistoryProps = {
  installedAppInfo?: InstalledApp
  className?: string
}
// 内层提供应用的一些基础信息展示
const ChatWithHistory: FC<ChatWithHistoryProps> = () => {
  const {
    appInfoError,
    appData,
    appInfoLoading,
    chatShouldReloadKey,
    themeBuilder,
    isMobile,
    fromMarket,
    appMarketData: marketAppData,
  } = useChatWithHistoryContext()
  const { t } = useTranslation()
  const customConfig = appData?.custom_config
  const site = appData?.site
  useEffect(() => {
    themeBuilder?.buildTheme(site?.chat_color_theme, site?.chat_color_theme_inverted)
    if (site) {
      if (customConfig)
        document.title = `${site.title}`
      else
        document.title = `${site.title} - Powered by ${APP_NAME}`
    }
  }, [site, customConfig, themeBuilder])

  if (appInfoLoading)
    return <Loading type="app" />

  if (appInfoError && appInfoError.status !== 401) {
    return (
      <AppUnavailable />
    )
  }

  return (
    <>
      {/* 应用头部 */}
      <Header></Header>
      <div className={isMobile ? style['mobile-content'] : style.content}>
        {/* 侧边栏 */}
        { !isMobile && <Sidebar /> }
        {/* 对话列表 */}
        <ChatWrapper key={chatShouldReloadKey} />
        {/* 应用描述 */}
        {fromMarket && !isMobile && (
          <div className={style.marketAppInfo}>
            <div className={style.appTitle}>{marketAppData?.name}</div>
            <div>
              <div className={style.appLabel}>{t('common.chat.market.introLabel')}</div>
              <div className={style.appDesc}>{marketAppData?.description || '-'}</div>
            </div>
            <div>
              <div className={style.appLabel}>{t('common.chat.market.usage')}</div>
              <div className={style.appDesc}>{marketAppData?.usages || '-'}</div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

// 中层处理聊天数据
const ChatWithHistoryWrap: FC<ChatWithHistoryWrapProps> = ({ className, fromMarket = false, auth = false }) => {
  const { isMobile } = useSystemContext()
  const searchParams = useSearchParams()
  // 应用市场数据
  const [marketAppData, setMarketAppData] = useState<AppMarket>()

  useEffect(() => {
    if (!fromMarket || !auth)
      return
    const id = searchParams.get('id')
    if (!id)
      return
    if (!marketAppData) {
      // 通过接口获取应用数据
      getMarketAppData(id).then((res) => {
        setMarketAppData(res as AppMarket)
      })
    }
  }, [fromMarket, searchParams, marketAppData, auth])

  const {
    appInfoError,
    appInfoLoading,
    appData,
    appParams,
    appMeta,
    appChatListDataLoading,
    currentConversationId,
    currentConversationItem,
    appPrevChatList,
    pinnedConversationList,
    conversationList,
    showConfigPanelBeforeChat,
    newConversationInputs,
    newConversationInputsRef,
    handleNewConversationInputsChange,
    setChatAllTypeResult,
    inputsForms,
    chatAllTypeResult,
    handleNewConversation,
    handleChangeConversation,
    handlePinConversation,
    isBasicLongChatModel,
    setIsBasicLongChatModel,
    handleUnpinConversation,
    handleDeleteConversation,
    conversationRenaming,
    handleAutoRename,
    handleRenameConversation,
    setCurrentChatConversationId,
    handleNewConversationCompleted,
    chatShouldReloadKey,
    isInstalledApp,
    appId,
    handleFeedback,
    currentChatInstanceRef,
    handleChatList,
    chatReady,
  } = useChatWithHistory()

  return (
    <ChatWithHistoryContext.Provider
      value={{
        appInfoError,
        appInfoLoading,
        appData,
        appParams: appParams as any,
        appMeta,
        appChatListDataLoading,
        currentConversationId,
        currentConversationItem,
        appPrevChatList,
        pinnedConversationList,
        conversationList,
        showConfigPanelBeforeChat,
        newConversationInputs,
        newConversationInputsRef,
        handleNewConversationInputsChange,
        setChatAllTypeResult,
        chatAllTypeResult,
        inputsForms,
        handleNewConversation,
        handleChangeConversation,
        handlePinConversation,
        handleUnpinConversation,
        handleDeleteConversation,
        conversationRenaming,
        handleAutoRename,
        handleRenameConversation,
        setCurrentChatConversationId,
        handleNewConversationCompleted,
        isBasicLongChatModel,
        setIsBasicLongChatModel,
        chatShouldReloadKey,
        isMobile,
        isInstalledApp,
        appId,
        handleFeedback,
        currentChatInstanceRef,
        fromMarket,
        handleChatList,
        appMarketData: marketAppData,
        chatReady,
      }}
    >
      <ChatWithHistory className={className} />
    </ChatWithHistoryContext.Provider>
  )
}
export type ChatWithHistoryWrapProps = {
  className?: string
  fromMarket?: boolean
  auth?: boolean
}

export default memo(ChatWithHistoryWrap)
