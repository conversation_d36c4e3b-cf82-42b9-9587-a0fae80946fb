import { useCallback, useEffect, useMemo, useState } from 'react'
import { t } from 'i18next'
import Chat from '../pref-chat'
import { useChat } from '../pref-chat/hooks'
import { getLastAnswer } from '../utils'
import { useChatWithHistoryContext } from './context'
import ConfigPanel from './config-panel'
import style from './styles/style.module.css'
import type { ChatConfig, ChatItem, OnSend } from '@/types/chat'
import {
  fetchSuggestedQuestions,
  getUrl,
  stopChatMessageResponding,
} from '@/service/share'

// 公共能力
import { getCropperFontCss } from '@/app/components/app/common/bg-cropper/utils'
import CropperWrapper from '@/app/components/base/cropper/wrapper'
import cn from '@/utils/classnames'

type ChatData = {
  write_type?: string
  write_theme?: string
  word_count?: string
  requirement?: string
  request_type?: string
}

const ChatWrapper = () => {
  const {
    appParams,
    appPrevChatList,
    currentConversationId,
    currentConversationItem,
    inputsForms,
    newConversationInputs,
    handleAutoRename,
    setCurrentChatConversationId,
    handleNewConversationCompleted,
    isInstalledApp,
    appId,
    handleFeedback,
    currentChatInstanceRef,
    appData,
    chatAllTypeResult,
    chatReady,
  } = useChatWithHistoryContext()
  // 对话是否之前已有数据
  const longTxtForm = inputsForms.find(form => form.type === 'longTxt')
  // hq 判断是否以生成长文
  const [IsLongTxt, setIsLongTxt] = useState(false)

  const appConfig = useMemo(() => {
    const config = appParams || {}
    return {
      ...config,
      supportFeedback: true,
      opening_statement: currentConversationId
        ? currentConversationItem?.introduction
        : (config as any).opening_statement,
    } as ChatConfig
  }, [appParams, currentConversationId, currentConversationItem])
  // 停止函数
  const stopChat = useCallback((taskId: string) => {
    stopChatMessageResponding('', taskId, isInstalledApp, appId)
  }, [isInstalledApp, appId])

  const {
    chatList,
    chatListRef,
    currentChatList,
    isVoiceConversation,
    isResponding,
    suggestedQuestions,
    handleUpdateChatList,
    handleSend,
    handleStop,
    handleRestart,
    openVoiceCall,
    closeVoiceCall,
    isThinking,
    isConnecting,
  } = useChat(
    appConfig,
    {
      inputs: (currentConversationId
        ? currentConversationItem?.inputs
        : newConversationInputs) as any,
      inputsForm: inputsForms,
    },
    appPrevChatList,
    stopChat,
  )
  let chatData: ChatData = {}

  // hq 判断是否以生成长文
  useEffect(() => {
    if (longTxtForm) {
      if (chatList.length > 3) {
        chatList.forEach((item, index) => {
          if (item.content.includes(t('sampleTemplate.BasicModal.generatingLongtxt')) && !item.workflowProcess) {
            setIsLongTxt(true)
          }
          else if (item.content.includes(t('sampleTemplate.BasicModal.generatingLongtxt')) && item.workflowProcess) {
            if (item.workflowProcess?.status === 'succeeded')
              setIsLongTxt(true)
          }
        })
      }
    }
  }, [chatList])

  // 消息发送
  const doSend: OnSend = useCallback(
    (message, files, last_answer) => {
      const data: any = {
        query: message,
        files,
        inputs: currentConversationId ? currentConversationItem?.inputs : newConversationInputs,
        conversation_id: currentConversationId,
        parent_message_id: last_answer?.id || getLastAnswer(chatListRef.current)?.id || null,
      }
      if (
        Object.keys(chatAllTypeResult.form || {}).length > 0
        && data.inputs
        && 'sample' in data.inputs
      ) {
        chatData = {
          write_type: chatAllTypeResult.type?.text || '',
          write_theme: chatAllTypeResult.form?.Theme || '',
          word_count: chatAllTypeResult.form?.Number || '',
          requirement: chatAllTypeResult.form,
        }
        if (chatAllTypeResult.type?.text === t('sampleTemplate.IntelligentAgent.CreationTypeTemplate') && chatAllTypeResult.files) {
          const sourceFiles = chatAllTypeResult.files
          const filesLong = {
            url: '',
            id: sourceFiles.id || sourceFiles.file_id,
            transferMethod: 'template_file',
            supportFileType: 'document',
            uploadedId: sourceFiles.id || sourceFiles.file_id,
            filename: sourceFiles.file_name,
          }
          data.inputs.template_file = filesLong
        }
        if ((chatAllTypeResult.type?.text === t('sampleTemplate.IntelligentAgent.CreationTypeTemplate') || chatAllTypeResult.type?.text === t('sampleTemplate.IntelligentAgent.CreationTypeTopic')) && chatAllTypeResult.resource && chatAllTypeResult.resource.length > 0) {
          const sFiles = chatAllTypeResult.resource
          const sFileslong = sFiles.map((item: any) => {
            return {
              url: '',
              id: item.id || item.file_id,
              transferMethod: 'local_file',
              supportFileType: 'document',
              uploadedId: item.id || item.file_id,
            }
          })
          data.inputs.resource_files = sFileslong
        }
      }

      if (data.inputs
        && 'sample' in data.inputs && message && message === t('sampleTemplate.Generate.GenerateDocument'))
        chatData.request_type = t('sampleTemplate.Generate.GenerateDocument') as string

      if (data.inputs
        && 'sample' in data.inputs)
        data.inputs.sample = JSON.stringify(chatData)

      handleSend(getUrl('chat-messages', isInstalledApp, appId || ''), data, {
        onGetConversationId: (conversationId) => {
          handleAutoRename(conversationId)
          setCurrentChatConversationId(conversationId)
        },
        onGetSuggestedQuestions: responseItemId =>
          fetchSuggestedQuestions(responseItemId, isInstalledApp, appId),
        onConversationComplete: currentConversationId ? undefined : handleNewConversationCompleted,
        isPublicAPI: !isInstalledApp,
      })
    },
    [
      chatListRef,
      appConfig,
      currentConversationId,
      currentConversationItem,
      handleSend,
      newConversationInputs,
      handleNewConversationCompleted,
      isInstalledApp,
      appId,
      chatAllTypeResult,
    ],
  )
  // ai回答重新生成
  const doRegenerate = useCallback(
    (chatItem: ChatItem) => {
      const index = chatListRef.current.findIndex(item => item.id === chatItem.id)
      if (index === -1)
        return

      const prevMessages = chatListRef.current.slice(0, index)
      const question = prevMessages.pop()
      const lastAnswer = getLastAnswer(prevMessages)

      if (!question)
        return

      handleUpdateChatList(prevMessages)
      doSend(question.content, question.message_files, lastAnswer)
    },
    [chatListRef, handleUpdateChatList, doSend],
  )
  // 语音通话
  const doVocieCall = useCallback(() => {
    openVoiceCall({
      conversation_id: currentConversationId,
      onGetConversationId: (conversationId) => {
        handleAutoRename(conversationId)
        setCurrentChatConversationId(conversationId)
      },
    })
  }, [currentConversationId, handleAutoRename, openVoiceCall, setCurrentChatConversationId])

  const chatNode = useMemo(() => {
    if (inputsForms.length > 0)
      return <>{!currentConversationId && <ConfigPanel />}</>

    return null
  }, [currentConversationId, inputsForms])
  const chartHasPrev = useMemo(() => {
    return !!appPrevChatList.length
  }, [appPrevChatList.length])

  useEffect(() => {
    if (currentChatInstanceRef.current) {
      currentChatInstanceRef.current.handleStop = handleStop
      currentChatInstanceRef.current.handleRestart = handleRestart
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <CropperWrapper
      config={appParams?.background_configs?.enabled ? appParams?.background_configs?.wide : undefined}
      className='w-full h-full bg-chatbot-bg bodyDiv overflow-hidden'
    >
      <Chat
        loading={!chatReady}
        appData={appData}
        answerIcon={appData?.site.icon_url || ''}
        config={appConfig}
        chatList={chatList}
        currentChatList={currentChatList}
        chatContainerInnerClassName={cn(style.chatContainerInnerClassName, getCropperFontCss(appParams?.background_configs))}
        chatFooterClassName='bottom-[12px]'
        chatFooterInnerClassName={style.chatFooterInnerClassName}
        inputs={
          currentConversationId ? (currentConversationItem?.inputs as any) : newConversationInputs
        }
        inputsForm={inputsForms}
        isPublicAPI={true}
        chatNode={chatNode}
        onFeedback={handleFeedback}
        suggestedQuestions={suggestedQuestions}
        hideProcessDetail
        noChatInput={!!longTxtForm}
        chartHasPrev={chartHasPrev}
        // 状态部分
        isVoiceCall = {isVoiceConversation}
        isResponding={isResponding}
        isThinking={isThinking}
        isConnecting={isConnecting}
        // 事件部分
        onSend={doSend}
        onRegenerate={doRegenerate}
        onStopResponding={handleStop}
        onVoiceCall={doVocieCall}
        onCancelVoiceCall={closeVoiceCall}
      />
    </CropperWrapper>
  )
}

export default ChatWrapper
