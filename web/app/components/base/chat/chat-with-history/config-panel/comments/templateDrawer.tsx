'use client'

import React from 'react'
import { Drawer } from 'antd'
import '../styles/temp_model.scss'
import dynamic from 'next/dynamic'
const FileViewer = dynamic(() => import('react-file-viewer'), { ssr: false })

type BasicModalProps = {
  open: boolean
  onClose: () => void
  drawerData: any
  placement?: 'left' | 'right'
}

const templateDrawer: React.FC<BasicModalProps> = ({ open, onClose, drawerData, placement = 'right' }) => {
  if (!drawerData)
    return null

  const getFileViewer = (fileName: any, filePath: any) => {
    const extension = fileName.slice(fileName.lastIndexOf('.')).toLowerCase()

    switch (extension) {
      case '.docx':
        return <FileViewer key={filePath} fileType='docx' filePath={filePath} />
      default:
        return (
          <iframe
            src={`${filePath}?page=hsn#toolbar=0`}
            width={'100%'}
            height={'100%'}
            style={{ border: 'none' }}
            className='custom-scrollbar'
          />
        )
    }
  }

  return (
    <>
      <Drawer
        title={drawerData.file_name}
        onClose={onClose}
        open={open}
        width={'40%'}
        placement={placement}
        destroyOnClose={true}
        className='custom-drawer'
      >
        {drawerData.file_name && getFileViewer(drawerData.file_name, drawerData.file_path)}
      </Drawer>
    </>
  )
}

export default templateDrawer
