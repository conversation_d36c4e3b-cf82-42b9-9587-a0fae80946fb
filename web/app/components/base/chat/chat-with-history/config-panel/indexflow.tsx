import { useState,useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useChatWithHistoryContext } from '../context'
import Form from './form'
import Button from '@/app/components/base/button'
import { Edit02 } from '@/app/components/base/icons/src/vender/line/general'
import { Star06 } from '@/app/components/base/icons/src/vender/solid/shapes'
import LogoSite from '@/app/components/base/logo/logo-site'
import AppAvatar from '@/app/components/app/common/avatar'
import style from '@/app/components/base/chat/chat-with-history/styles/style.module.css'

const ConfigPanel = () => {
  const { t } = useTranslation()
  const { appData, inputsForms, handleStartChat, showConfigPanelBeforeChat, isMobile } =
    useChatWithHistoryContext()
  const longTxtForm = inputsForms.find(form => form.type === 'longTxt')
  const [collapsed, setCollapsed] = useState(true)
  const customConfig = appData?.custom_config
  const site = appData?.site
  return (
    <div className='w-full'>
      {longTxtForm ? (
        <>
          <div
            className={`
            grow  rounded border-gray-G5'
          `}
          >
           <div className={style.logo}>
  <AppAvatar
    appMode={'chat'}
    url={appData?.site?.icon_url}
    size={68}
    className={style.logo_img}
  />
  <div className={style.name}>{t('sampleTemplate.longText.longtitle')}</div>
</div>

            {!showConfigPanelBeforeChat && collapsed && (
              <div className="p-2 rounded-b">
                <div className="flex">
                  <AppAvatar
                    appMode={'chat'}
                    url={appData?.site?.icon_url}
                    size={32}
                    className="mt-4 mr-2 rounded-full"
                  />
                  <div
                    className={`
            grow  bg-white rounded-lg border border-gray-G5'
          `}
                  >
                    <div className="p-4 rounded-b">
                      <Form />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {!collapsed && !showConfigPanelBeforeChat && (
              <div className="p-2 rounded-b">
                <div className="flex">
                  <AppAvatar
                    appMode={'chat'}
                    url={appData?.site?.icon_url}
                    size={32}
                    className="mt-4 mr-2 rounded-full"
                  />
                  <div
                    className={`
            grow  bg-white rounded-lg border border-gray-G5'
          `}
                  >
                    <div className="p-4 rounded-b">
                      <Form />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {showConfigPanelBeforeChat && (
              <div className="p-2 rounded-b">
                <div className="flex">
                  <AppAvatar
                    appMode={'chat'}
                    url={appData?.site?.icon_url}
                    size={32}
                    className="mt-4 mr-2 rounded-full"
                  />
                  <div
                    className={`
            grow  bg-white rounded-lg border border-gray-G5'
          `}
                  >
                    <div className="p-4 rounded-b">
                      <Form />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </>
      ) : (
        <div
          className={`
            grow rounded overflow-y-auto bg-white rounded border border-gray-G5'
          `}
        >
          <div
            className={`
              flex flex-wrap px-6 py-4 rounded-t
              ${isMobile && '!px-4 !py-3'}
            `}
          >
            {/* {
              showConfigPanelBeforeChat && (
                <>
                  <div className='flex items-center h-8 text-2xl font-semibold text-gray-800'>
                    <AppIcon
                      iconType={appData?.site.icon_type}
                      icon={appData?.site.icon}
                      background='transparent'
                      imageUrl={appData?.site.icon_url}
                      size='small'
                      className="mr-2"
                    />
                    {appData?.site.title}
                  </div>
                  {
                    appData?.site.description && (
                      <div className='w-full mt-2 text-sm text-gray-500'>
                        {appData?.site.description}
                      </div>
                    )
                  }
                </>
              )
            } */}
            {/* 编辑变量弹窗折叠 */}
            {!showConfigPanelBeforeChat && collapsed && (
              <>
                <div className="grow py-[3px] title-14-24">{t('share.chat.configStatusDes')}</div>
                <Button
                  variant="secondary-accent"
                  size="small"
                  className="shrink-0"
                  onClick={() => setCollapsed(false)}
                >
                  <Edit02 className="w-3 h-3 mr-1" />
                  {t('common.operation.edit')}
                </Button>
              </>
            )}
            {/* 对话设置标题 */}
            {(showConfigPanelBeforeChat || !collapsed) && (
              <>
                <div className="grow title-14-24">{t('share.chat.privatePromptConfigTitle')}</div>
              </>
            )}
          </div>
          {/* 编辑变量提交表单 */}
          {!collapsed && !showConfigPanelBeforeChat && (
            <div className="p-6 rounded-b">
              <Form />
              <div className={`pl-[136px] mt-4 flex items-center ${isMobile && '!pl-0'}`}>
                <Button
                  variant="primary"
                  className="mr-2"
                  onClick={() => {
                    setCollapsed(true)
                    handleStartChat()
                  }}
                >
                  {t('common.operation.save')}
                </Button>
                <Button onClick={() => setCollapsed(true)}>{t('common.operation.cancel')}</Button>
              </div>
            </div>
          )}
          {/* 开启对话 编辑变量表单 */}
          {showConfigPanelBeforeChat && (
            <div className="p-6 rounded-b">
              <Form />
              {/* <Button
                className={`${inputsForms.length && !isMobile && 'ml-[136px]'} mt-4`}
                variant="primary"
                size="large"
                onClick={handleStartChat}
              >
                <MessageDotsCircle className="w-4 h-4 mr-2 text-white" />
                {t('share.chat.startChat')}
              </Button> */}
            </div>
          )}
        </div>
      )}


    </div>
  )
}

export default ConfigPanel
