import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import useSWR, { mutate } from 'swr'
import { useLocalStorageState } from 'ahooks'
import produce from 'immer'
import { CONVERSATION_ID_INFO } from '../constants'
import { getPrevChatList } from '../utils'
import type {
  ChatConfig,
  Feedback,
} from '@/types/chat'
import { getFormatedDatetime } from '@/utils'
import {
  delConversation,
  fetchAppInfo,
  fetchAppMeta,
  fetchAppParams,
  fetchChatList,
  fetchConversations,
  generationConversationName,
  pinConversation,
  renameConversation,
  unpinConversation,
  updateFeedback,
} from '@/service/share'
import type { ConversationItem } from '@/types/share'
import { useToastContext } from '@/app/components/base/toast'
import { InputVarType } from '@/app/components/workflow/types'
import { TransferMethod } from '@/types/model'
import { useSystemContext } from '@/context/system-context'
import type { Callback } from '@/types/api/common'

// 获取聊天历史信息
export const useChatWithHistory = () => {
  const { t } = useTranslation()
  const { notify } = useToastContext()
  const { systemFeatures } = useSystemContext()
  // 是否启用历史对话和历史对话列表
  const useHistory = true
  // 对话信息
  const [conversationIdInfo, setConversationIdInfo] = useLocalStorageState<Record<string, string>>(
    CONVERSATION_ID_INFO,
    {
      defaultValue: {},
    },
  )
  // 对话前显示配置面板
  const [showConfigPanelBeforeChat, setShowConfigPanelBeforeChat] = useState(true)
  // 显示新对话项
  const showNewConversationItemInListRef = useRef(true)
  // 对话id 更新有两种情况，1.开启新对话时更新为空字符串，2.更新为对话完成后接口返回的ConversationId
  const [newConversationId, setNewConversationId] = useState('')
  // 输入表单
  const newConversationInputsRef = useRef<Record<string, any>>({})
  const currentChatInstanceRef = useRef<{ handleStop: () => void; handleRestart: () => void }>({
    handleStop: () => { },
    handleRestart: () => { },
  })
  // 新的对话输入参数
  const [newConversationInputs, setNewConversationInputs] = useState<Record<string, any>>({})
  const [originConversationList, setOriginConversationList] = useState<ConversationItem[]>([])
  // 对话正在删除
  const [conversationDeleting, setConversationDeleting] = useState(false)
  // 对话正在重命名
  const [conversationRenaming, setConversationRenaming] = useState(false)
  // 是否是基础长文本模型
  const [isBasicLongChatModel, setIsBasicLongChatModel] = useState(false)
  // 当前开启的新聊天对话id，仅用作新对话未结束就刷新页面时，用来更新conversationIdInfo
  const [currentChatConversationId, setCurrentChatConversationId] = useState('')

  // 获取应用信息
  const {
    data: appInfo,
    isLoading: appInfoLoading,
    error: appInfoError,
  } = useSWR('appInfo', fetchAppInfo, { revalidateOnFocus: false })
  // 应用id
  const appId = useMemo(() => appInfo?.app_id, [appInfo])
  // 当前谈话id
  const currentConversationId = useMemo(
    () => conversationIdInfo?.[appId || ''] || '',
    [appId, conversationIdInfo],
  )
  // 当前对话id和新的对话id不同，则更新的ky
  const chatShouldReloadKey = useMemo(() => {
    if (currentConversationId === newConversationId)
      return ''
    if (originConversationList.find(item => item.id === currentConversationId))
      return currentConversationId

    return ''
  }, [currentConversationId, newConversationId, originConversationList])
  // 应用参数
  const { data: appParams } = useSWR(['appParams'], () =>
    fetchAppParams(false, appId),
  { revalidateOnFocus: false },
  )
  // 应用元数据
  const { data: appMeta } = useSWR(['appMeta'], () =>
    fetchAppMeta(false, appId),
  { revalidateOnFocus: false },
  )
  // 查询当前app下被置顶的对话列表
  const { data: appPinnedConversationData, mutate: mutateAppPinnedConversationData } = useSWR(
    false ? ['appConversationData', appId, true] : null,
    () => fetchConversations(false, appId, undefined, true, 100),
    { revalidateOnFocus: false },
  )
  // 查询当前app下所有对话列表
  const {
    data: appConversationData,
    isLoading: appConversationDataLoading,
    mutate: mutateAppConversationData,
  } = useSWR(useHistory ? ['appConversationData', false] : null, () =>
    fetchConversations(false, appId, undefined, false, 100),
  { revalidateOnFocus: false },
  )
  // 根据某次对话id查询对话记录
  const { data: appChatListData, isLoading: appChatListDataLoading } = useSWR(
    useHistory ? chatShouldReloadKey ? ['appChatList', chatShouldReloadKey, false, appId] : null : null,
    () => fetchChatList(chatShouldReloadKey, false, appId),
    { revalidateOnFocus: false },
  )
  // 生成新对话名称。
  const { data: newConversation } = useSWR(
    newConversationId ? [false, appId, newConversationId] : null,
    () => generationConversationName(false, appId, newConversationId),
    { revalidateOnFocus: false },
  )
  // 之前的对话数据
  const appPrevChatList = useMemo(
    () => (
      (currentConversationId && appChatListData?.data.length)
        ? getPrevChatList(appChatListData.data)
        : []
    ),
    [appChatListData, currentConversationId],
  )
  // 固定的对话列表
  const pinnedConversationList = useMemo(() => {
    return appPinnedConversationData?.data || []
  }, [appPinnedConversationData])
  // 输入表单项
  const inputsForms = useMemo(() => {
    return (appParams?.user_input_form || [])
      .filter((item: any) => !item.external_data_tool)
      .map((item: any) => {
        if (item.paragraph) {
          return {
            ...item.paragraph,
            type: 'paragraph',
          }
        }
        if (item.number) {
          return {
            ...item.number,
            type: 'number',
          }
        }
        if (item.select) {
          return {
            ...item.select,
            type: 'select',
          }
        }
        if (item.longTxt) {
          return {
            ...item.longTxt,
            type: 'longTxt',
          }
        }

        if (item['file-list']) {
          return {
            ...item['file-list'],
            type: 'file-list',
          }
        }

        if (item.file) {
          return {
            ...item.file,
            type: 'file',
          }
        }

        return {
          ...item['text-input'],
          type: 'text-input',
        }
      })
  }, [appParams])
  // 对话是否准备完成
  const chatReady = useMemo(() => {
    return !!(appParams && appMeta && !appChatListDataLoading)
  }, [appParams, appMeta, appChatListDataLoading])

  // 变更对话输入参数
  const handleNewConversationInputsChange = useCallback((newInputs: Record<string, any>) => {
    newConversationInputsRef.current = newInputs
    setNewConversationInputs(newInputs)
  }, [])

  useEffect(() => {
    const conversationInputs: Record<string, any> = {}

    inputsForms.forEach((item: any) => {
      conversationInputs[item.variable] = item.default || ''
    })
    handleNewConversationInputsChange(conversationInputs)
  }, [handleNewConversationInputsChange, inputsForms])

  // 接口返回的对话列表数据赋值给originConversationList,如果没有历史对话，新建一个新对话
  useEffect(() => {
    if (appConversationData?.data && !appConversationDataLoading)
      setOriginConversationList(appConversationData?.data)
  }, [appConversationData, appConversationDataLoading])

  // 对话列表顶部插入一条空白新对话
  const conversationList = useMemo(() => {
    const data = originConversationList.slice()
    if (showNewConversationItemInListRef.current && !currentConversationId) {
      data.unshift({
        id: '',
        name: getFormatedDatetime() || t('share.chat.newChatDefaultName'),
        inputs: {},
        introduction: '',
      })
    }
    return data
  }, [originConversationList, currentConversationId, t])

  useEffect(() => {
    if (newConversation) {
      showNewConversationItemInListRef.current = false
      setOriginConversationList(
        produce((draft) => {
          const index = draft.findIndex(item => item.id === newConversation.id)

          if (index > -1)
            draft[index] = newConversation
          else draft.unshift(newConversation)
        }),
      )
    }
  }, [newConversation])

  const currentConversationItem = useMemo(() => {
    let conversationItem = conversationList.find(item => item.id === currentConversationId)

    if (!conversationItem && pinnedConversationList.length)
      conversationItem = pinnedConversationList.find(item => item.id === currentConversationId)

    if (conversationItem?.id && conversationItem?.name === t('common.actionMsg.addnew'))
      fetchChatList(conversationItem.id, false, appId)

    return conversationItem
  }, [conversationList, currentConversationId, pinnedConversationList])
  /* 获取相信的chat信息--end */
  // 当前应用配置
  const currentAppParams = useMemo(() => {
    return {
      ...appParams,
      text_to_speech: {
        voice_input: {
          ...appParams?.text_to_speech.voice_input,
          enabled: !systemFeatures?.enable_private_register && appParams?.text_to_speech?.voice_input?.enabled,
        },
        voice_conversation: {
          ...appParams?.text_to_speech.voice_conversation,
          enabled: !systemFeatures?.enable_private_register && appParams?.text_to_speech?.voice_conversation?.enabled,
        },
      },
    }
  }, [appParams, systemFeatures?.enable_private_register])

  // 确认对话前输入表单
  const checkInputsRequired = useCallback(
    (silent?: boolean) => {
      let hasEmptyInput = ''
      let fileIsUploading = false
      const requiredVars = inputsForms.filter(({ required }) => required)
      if (requiredVars.length) {
        requiredVars.forEach(({ variable, label, type }) => {
          // 存在空的必填项
          if (hasEmptyInput)
            return
          // 文件正在上传
          if (fileIsUploading)
            return
          if (!newConversationInputsRef.current[variable])
            hasEmptyInput = label as string
          if (
            (type === InputVarType.singleFile || type === InputVarType.multiFiles)
            && newConversationInputsRef.current[variable]
          ) {
            const files = newConversationInputsRef.current[variable]
            if (Array.isArray(files)) {
              fileIsUploading = files.find(
                item => item.transferMethod === TransferMethod.local_file && !item.uploadedId,
              )
            }
            else {
              fileIsUploading
                = files.transferMethod === TransferMethod.local_file && !files.uploadedId
            }
          }
        })
      }

      if (hasEmptyInput) {
        if (!silent) {
          notify({
            type: 'error',
            message: t('appDebug.errorMessage.valueOfVarRequired', { key: hasEmptyInput }),
          })
        }
        return false
      }

      if (fileIsUploading) {
        if (!silent)
          notify({ type: 'info', message: t('appDebug.errorMessage.waitForFileUpload') })
        return false
      }

      return true
    },
    [inputsForms, notify, t],
  )

  const handleChatList = useCallback(async (conversationId: string) => {
    return fetchChatList(conversationId, false, appId)
  }, [])

  // 对话信息变更-对话调用事件-[应用id: 对话变更id]
  const handleConversationIdInfoChange = useCallback(
    (changeConversationId: string) => {
      if (appId) {
        setConversationIdInfo({
          ...conversationIdInfo,
          [appId || '']: changeConversationId,
        })
      }
    },
    [appId, conversationIdInfo, setConversationIdInfo],
  )
  // 更新对话列表
  const handleUpdateConversationList = useCallback(() => {
    mutateAppConversationData()
    mutateAppPinnedConversationData()
  }, [mutateAppConversationData, mutateAppPinnedConversationData])
  // 变更当前对话
  const handleChangeConversation = useCallback(
    async (conversationId: string, isBasicLongChatModel?: boolean) => {
      currentChatInstanceRef.current.handleStop()
      setNewConversationId('')
      handleConversationIdInfoChange(conversationId)

      if (isBasicLongChatModel) {
        const conversationItem = conversationList.find(item => item.id === currentConversationId)
        if (conversationItem) {
          mutate(
            ['appChatList', conversationItem.id, false, appId],
            fetchChatList(conversationItem.id, false, appId),
            false,
          )
        }
      }
      if (conversationId === '' && !checkInputsRequired(true))
        setShowConfigPanelBeforeChat(true)
      else setShowConfigPanelBeforeChat(false)
    },
    [handleConversationIdInfoChange, checkInputsRequired, conversationList, currentConversationId, appId],
  )
  // 开启新对话
  const handleNewConversation = useCallback(() => {
    setNewConversationId('')
    setCurrentChatConversationId('')
    handleChangeConversation('')
    currentChatInstanceRef.current.handleRestart()
    showNewConversationItemInListRef.current = true
    handleUpdateConversationList()
  }, [handleChangeConversation, handleUpdateConversationList])

  // 处理新对话未结束就刷新页面时，用来更新conversationIdInfo
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (!currentConversationId && currentChatConversationId)
        handleConversationIdInfoChange(currentChatConversationId)
    }
    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [currentConversationId, currentChatConversationId, handleConversationIdInfoChange])
  // 如果聊天列表没有当前应用对话id，清空当前应用对话id
  useEffect(() => {
    if (appConversationData?.data && !appConversationDataLoading) {
      if (currentConversationId && appConversationData?.data.findIndex(item => item.id === currentConversationId) === -1)
        handleConversationIdInfoChange('')
    }
  }, [appConversationData, appConversationDataLoading])
  // 固定
  const handlePinConversation = useCallback(
    async (conversationId: string) => {
      await pinConversation(false, appId, conversationId)
      notify({ type: 'success', message: t('common.actionMsg.success') })
      handleUpdateConversationList()
    },
    [appId, notify, t, handleUpdateConversationList],
  )
  // 取消固定
  const handleUnpinConversation = useCallback(
    async (conversationId: string) => {
      await unpinConversation(false, appId, conversationId)
      notify({ type: 'success', message: t('common.actionMsg.success') })
      handleUpdateConversationList()
    },
    [false, appId, notify, t, handleUpdateConversationList],
  )
  // 删除
  const handleDeleteConversation = useCallback(
    async (conversationId: string, { onSuccess }: Callback) => {
      if (conversationDeleting)
        return

      try {
        setConversationDeleting(true)
        await delConversation(false, appId, conversationId)
        notify({ type: 'success', message: t('common.actionMsg.success') })
        onSuccess()
      }
      finally {
        setConversationDeleting(false)
      }

      if (conversationId === currentConversationId)
        handleNewConversation()

      handleUpdateConversationList()
    },
    [
      false,
      appId,
      notify,
      t,
      handleUpdateConversationList,
      handleNewConversation,
      currentConversationId,
      conversationDeleting,
    ],
  )
  // 自动命名
  const handleAutoRename = useCallback(
    async (conversationId: string) => {
      const newConversation = await generationConversationName(
        false,
        appId,
        conversationId,
      )
      showNewConversationItemInListRef.current = false
      setOriginConversationList(
        produce((draft) => {
          const index = draft.findIndex(item => item.id === conversationId)

          if (index > -1)
            draft[index] = newConversation
          else draft.unshift(newConversation)
        }),
      )
    },
    [generationConversationName, originConversationList],
  )
  // 重命名
  const handleRenameConversation = useCallback(
    async (conversationId: string, newName: string, { onSuccess }: Callback) => {
      if (conversationRenaming)
        return

      if (!newName.trim()) {
        notify({
          type: 'error',
          message: t('common.chat.conversationNameCanNotEmpty'),
        })
        return
      }

      setConversationRenaming(true)
      try {
        await renameConversation(false, appId, conversationId, newName)

        notify({
          type: 'success',
          message: t('common.actionMsg.modifiedSuccessfully'),
        })
        setOriginConversationList(
          produce((draft) => {
            const index = originConversationList.findIndex(item => item.id === conversationId)
            const item = draft[index]

            draft[index] = {
              ...item,
              name: newName,
            }
          }),
        )
        onSuccess()
      }
      finally {
        setConversationRenaming(false)
      }
    },
    [appId, notify, t, conversationRenaming, originConversationList],
  )
  // 新对话完成事件
  const handleNewConversationCompleted = useCallback(
    (newConversationId: string) => {
      setNewConversationId(newConversationId)
      handleConversationIdInfoChange(newConversationId)
      showNewConversationItemInListRef.current = false
      mutateAppConversationData()
    },
    [mutateAppConversationData, handleConversationIdInfoChange],
  )
  // 回调事件
  const handleFeedback = useCallback(
    async (messageId: string, feedback: Feedback) => {
      await updateFeedback(
        // { url: `/messages/${messageId}/feedbacks`, body: { rating: feedback.rating } },
        { url: `/messages/${messageId}/feedbacks`, body: { ...feedback } },
        false,
        appId,
      )
      notify({ type: 'success', message: t('common.actionMsg.success') })
    },
    [appId, t, notify],
  )

  // 长文档专用
  const [chatAllTypeResults, setChatAllTypeResults] = useState<Record<string, any>>({})
  const chatAllTypeResult = useMemo(() => chatAllTypeResults, [chatAllTypeResults])
  const setChatAllTypeResult = useCallback((newData: Record<string, any>) => {
    setChatAllTypeResults((prev) => {
      if (Object.keys(newData).length === 0)
        return {}

      const mergedData = { ...prev, ...newData }
      if (JSON.stringify(prev) === JSON.stringify(mergedData))
        return prev
      return mergedData
    })
  }, [])
  useEffect(() => {
    const initializeInputs = () => {
      const conversationInputs: Record<string, any> = {}
      if (chatAllTypeResult && typeof chatAllTypeResult === 'object') {
        Object.entries(chatAllTypeResult).forEach(([key, item]: [string, any]) => {
          if (item && typeof item === 'object' && 'default' in item)
            conversationInputs[key] = item.default || ''
        })
        if (Object.keys(conversationInputs).length > 0)
          setChatAllTypeResult({ ...chatAllTypeResult, ...conversationInputs })
      }
    }

    initializeInputs()
  }, [])

  return {
    appInfoError,
    appInfoLoading,
    isInstalledApp: false,
    appId,
    currentConversationId,
    currentConversationItem,
    handleConversationIdInfoChange,
    setCurrentChatConversationId,
    appData: appInfo,
    appParams: currentAppParams || ({} as ChatConfig),
    appMeta,
    isBasicLongChatModel,
    setIsBasicLongChatModel,
    appPinnedConversationData,
    appConversationData,
    appConversationDataLoading,
    appChatListDataLoading,
    appPrevChatList,
    pinnedConversationList,
    conversationList,
    showConfigPanelBeforeChat,
    setShowConfigPanelBeforeChat,
    newConversationInputs,
    newConversationInputsRef,
    handleNewConversationInputsChange,
    inputsForms,
    handleNewConversation,
    handleChangeConversation,
    handlePinConversation,
    handleUnpinConversation,
    conversationDeleting,
    handleDeleteConversation,
    conversationRenaming,
    handleAutoRename,
    handleRenameConversation,
    handleNewConversationCompleted,
    newConversationId,
    chatShouldReloadKey,
    handleFeedback,
    currentChatInstanceRef,
    chatAllTypeResult,
    setChatAllTypeResult,
    handleChatList,
    chatReady,
  }
}
