import { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { produce, setAutoFreeze } from 'immer'
import { uniqBy } from 'lodash-es'
import type { IAgoraRTCClient as Client, IMicrophoneAudioTrack } from 'agora-rtc-sdk-ng/rtc-sdk_en'
import { useParams, usePathname } from 'next/navigation'
import { useStore } from 'zustand'
import { getProcessedInputs, processOpeningStatement } from './utils'
import { useCheckInputsForms } from './components/chat-input-area/hooks'
import type { ChatStaticState } from './store'
import { ChatStaticContext } from './context'
import type { ChatConfig, ChatItem, InputForm, Inputs, ThoughtItem, VoiceChatConfig } from '@/types/chat'
import { TransferMethod } from '@/types/model'
import { useToastContext } from '@/app/components/base/toast'
import type { IOtherOptions } from '@/service/sse-base'
import { ssePost } from '@/service/sse-base'
import type { Annotation } from '@/models/log'
import { NodeRunningStatus, WorkflowRunningStatus } from '@/app/components/workflow/types'
import useTimestamp from '@/hooks/use-timestamp'
import type { FileEntity } from '@/types/file'
import {
  getProcessedFiles,
  getProcessedFilesFromResponse,
} from '@/app/components/base/file-uploader/utils'
import { sendVoiceChatMessage } from '@/service/debug'
import { useOnline } from '@/hooks/use-online'
import { useWorkflowStore } from '@/app/components/workflow/store'
import type { VoiceAnswerSafetyFence, VoiceQuerySafetyFence } from '@/types/api/chat'

type GetAbortController = (abortController: AbortController) => void
type SendCallback = {
  onGetConversationId?: (conversationId: string) => void
  onGetConversationMessages?: (conversationId: string, getAbortController: GetAbortController) => Promise<any>
  onGetSuggestedQuestions?: (responseItemId: string, getAbortController: GetAbortController) => Promise<any>
  onConversationComplete?: (conversationId: string) => void
  isPublicAPI?: boolean
}

export const useChat = (
  config?: ChatConfig,
  formSettings?: {
    inputs: Inputs
    inputsForm: InputForm[]
  },
  prevChatList?: ChatItem[],
  stopChat?: (taskId: string) => void,
  inWorkflow = false,
  runfunc = ssePost as any,
) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()
  const { notify } = useToastContext()
  const workflowStore = useWorkflowStore()
  const { checkInputsForm } = useCheckInputsForms()

  // 当前弹窗id
  const conversationId = useRef('')
  // 是否已经手动停止
  const hasStopResponded = useRef(false)
  // 是否正在响应
  const [isResponding, setIsResponding] = useState(false)
  const isRespondingRef = useRef(false)
  // 是否正在语音通话
  const [isVoiceConversation, setIsVoiceConversation] = useState(false)
  // 语音通话状态
  const [isConnecting, setIsConnecting] = useState(false)
  // ai是否思考中
  const [isThinking, setIsThinking] = useState(false)
  // 对话列表
  const [chatList, setChatList] = useState<ChatItem[]>(prevChatList || [])
  const chatListRef = useRef<ChatItem[]>(prevChatList || [])
  // 当前对话——包含一问一答
  const [currentChatList, setCurrentChatList] = useState<ChatItem[]>([])
  // 语音通话rtc ref
  const voiceRef = useRef<Client | null>(null)
  // 麦克风音频轨道ref
  const microphoneAudioTrackRef = useRef<IMicrophoneAudioTrack | null>(null)
  // const taskIdRef = useRef('')
  // 任务id
  const [taskIdValue, setTaskIdValue] = useState<string>('')
  // 建议问题列表
  const [suggestedQuestions, setSuggestQuestions] = useState<string[]>([])
  const conversationMessagesAbortControllerRef = useRef<AbortController | null>(null)
  const suggestedQuestionsAbortControllerRef = useRef<AbortController | null>(null)
  const ssePostAbortControllerRef = useRef<AbortController | null>(null)
  const params = useParams()
  const pathname = usePathname()
  const { online, startCheck, finishCheck } = useOnline()

  const getIntroduction = useCallback((str: string) => {
    return processOpeningStatement(str, formSettings?.inputs || {}, formSettings?.inputsForm || [])
  }, [formSettings?.inputs, formSettings?.inputsForm])

  // 更新绘画列表
  const handleUpdateChatList = useCallback((newChatList: ChatItem[], reRender = true) => {
    // 如果直接渲染，说明两者相同，反之需要设置一个中间层
    if (reRender) {
      setChatList(newChatList)
      setCurrentChatList([])
    }
    else {
      setCurrentChatList(newChatList.slice(-2))
    }
    chatListRef.current = newChatList
  }, [])
  // 处理正在响应
  const handleResponding = useCallback((isResponding: boolean) => {
    setIsResponding(isResponding)
    isRespondingRef.current = isResponding
  }, [])
  // 处理用户提问
  const handleQuery = useCallback((data: {
    query: string
    files?: FileEntity[]
    [key: string]: any
  }) => {
    setSuggestQuestions([])
    // 这里先设置了一个虚假id的提问进行占位，等接口返回后替换
    const questionId = `question-${Date.now()}`
    const questionItem = {
      id: questionId,
      content: data.query,
      isAnswer: false,
      message_files: data.files,
    }
    // 这里先设置了一个虚假id的回答进行占位，等接口返回后替换
    const placeholderAnswerId = `answer-placeholder-${Date.now()}`
    const placeholderAnswerItem = {
      id: placeholderAnswerId,
      content: '',
      isAnswer: true,
    }
    // 更新对话窗口
    const newList = [...chatListRef.current, questionItem, placeholderAnswerItem]
    handleUpdateChatList(newList, false)
    return {
      placeholderAnswerId,
      questionId,
      questionItem,
    }
  }, [handleUpdateChatList])
  // 处理ai回答
  const handleAiAnswer = useCallback((placeholderAnswerId: string) => {
    const responseItem: ChatItem = {
      id: placeholderAnswerId,
      content: '',
      agent_thoughts: [],
      message_files: [],
      isAnswer: true,
    }
    handleResponding(true)
    hasStopResponded.current = false
    return responseItem
  }, [handleResponding])
  // 更新当前提问以及回答
  const updateCurrentQA = useCallback(({
    responseItem,
    questionId,
    placeholderAnswerId,
    questionItem,
  }: {
    responseItem: ChatItem
    questionId: string
    placeholderAnswerId: string
    questionItem: ChatItem
  }) => {
    const newListWithAnswer = chatListRef.current.filter(item => item.id !== responseItem.id && item.id !== placeholderAnswerId)
    if (!newListWithAnswer.find(item => item.id === questionId))
      newListWithAnswer.push({ ...questionItem })
    newListWithAnswer.push({ ...responseItem })
    handleUpdateChatList(newListWithAnswer, false)
  }, [handleUpdateChatList])
  // 通过api更新当前对话列表
  const updateChatListByApi = useCallback((newResponseItem: any, responseItemId: string) => {
    const newChatList = produce(chatListRef.current, (draft) => {
      const index = draft.findIndex(item => item.id === responseItemId)
      if (index !== -1) {
        const question = draft[index - 1]
        draft[index - 1] = {
          ...question,
        }
        draft[index] = {
          ...draft[index],
          content: newResponseItem.answer,
          log: [
            ...newResponseItem.message,
            ...((newResponseItem.message.length && newResponseItem.message[newResponseItem.message.length - 1].role !== 'assistant')
              ? [
                {
                  role: 'assistant',
                  text: newResponseItem.answer,
                  files: newResponseItem.message_files?.filter((file: any) => file.belongs_to === 'assistant') || [],
                },
              ]
              : []),
          ],
          more: {
            time: formatTime(newResponseItem.created_at, 'hh:mm A'),
            tokens: newResponseItem.answer_tokens + newResponseItem.message_tokens,
            latency: newResponseItem.provider_response_latency.toFixed(2),
          },
          // for agent log
          conversationId: conversationId.current,
          input: {
            inputs: newResponseItem.inputs,
            query: newResponseItem.query,
          },
        }
      }
    })
    handleUpdateChatList(newChatList)
  }, [formatTime, handleUpdateChatList])

  // 发送对话
  const handleSend = useCallback(async (
    url: string,
    data: {
      query: string
      files?: FileEntity[]
      [key: string]: any
    },
    {
      onGetConversationId,
      onGetConversationMessages,
      onGetSuggestedQuestions,
      onConversationComplete,
      isPublicAPI,
    }: SendCallback,
  ) => {
    // 如果正在响应——直接return
    if (isRespondingRef.current) {
      notify({ type: 'info', message: t('appDebug.errorMessage.waitForResponse') })
      return
    }
    const { query, files, inputs, ...restData } = data

    if (!checkInputsForm(inputs, formSettings?.inputsForm || []))
      return
    // 处理提问逻辑
    const queryConfig = handleQuery(data)
    if (!queryConfig)
      return
    const { placeholderAnswerId, questionId, questionItem } = queryConfig
    // 预处理AI回答
    const responseItem = handleAiAnswer(placeholderAnswerId)
    /* 开始发送sso请求 */
    const bodyParams = {
      response_mode: 'streaming',
      conversation_id: conversationId.current,
      files: getProcessedFiles(files || []),
      query,
      inputs: getProcessedInputs(inputs || {}, formSettings?.inputsForm || []),
      ...restData,
    }
    if (bodyParams?.files?.length) {
      bodyParams.files = bodyParams.files.map((item) => {
        if (item.transfer_method === TransferMethod.local_file) {
          return {
            ...item,
            url: '',
          }
        }
        return item
      })
    }
    let isAgentMode = false
    let hasSetResponseId = false
    let isCheckFailed = false
    let hasGetConversationIdFlag = false // 每次对话发送前重置已获取对话id状态

    // 生成对应的hook函数
    const chatHookConfig: IOtherOptions = {
      isPublicAPI,
      /* 数据接收处理 */
      onData: (message: string, isFirstMessage: boolean, { conversationId: newConversationId, messageId, taskId }: any) => {
        if (!isAgentMode) {
          responseItem.content = responseItem.content + message
        }
        else {
          const lastThought = responseItem.agent_thoughts?.[responseItem.agent_thoughts?.length - 1]
          if (lastThought)
            lastThought.thought = lastThought.thought + message // need immer setAutoFreeze
        }
        if (messageId && !hasSetResponseId) {
          responseItem.id = messageId
          hasSetResponseId = true
        }
        if (onGetConversationId && isFirstMessage && newConversationId && !hasGetConversationIdFlag && !data.conversation_id) {
          onGetConversationId(newConversationId)
          hasGetConversationIdFlag = true
        }
        if (isFirstMessage && newConversationId)
          conversationId.current = newConversationId
        setTaskIdValue(taskId)
        if (messageId)
          responseItem.id = messageId
        // 对话中止后查询agent日志需要conversationId
        console.log("responseItem.conversationId",responseItem.conversationId,newConversationId)
        if (!responseItem.conversationId && newConversationId){
          responseItem.conversationId = newConversationId
          conversationId.current = newConversationId
        }        

        updateCurrentQA({
          responseItem,
          questionId,
          placeholderAnswerId,
          questionItem,
        })
      },
      /* 数据接收完成 */
      async onCompleted(hasError?: boolean, errorMessage?: string) {
        handleResponding(false)
        if (hasError) {
          // 存在错误信息时
          if (errorMessage) {
            responseItem.content = errorMessage
            responseItem.isError = true
            const newListWithAnswer = produce(
              chatListRef.current.filter(item => item.id !== responseItem.id && item.id !== placeholderAnswerId),
              (draft) => {
                if (!draft.find(item => item.id === questionId))
                  draft.push({ ...questionItem })

                draft.push({ ...responseItem })
              })
            handleUpdateChatList(newListWithAnswer)
          }
          return
        }
        if (onConversationComplete)
          onConversationComplete(conversationId.current)
        if (conversationId.current && onGetConversationMessages) {
          const { data }: any = await onGetConversationMessages(
            conversationId.current,
            newAbortController => conversationMessagesAbortControllerRef.current = newAbortController,
          )
          const newResponseItem = data.find((item: any) => item.id === responseItem.id)
          if (!newResponseItem)
            return

          updateChatListByApi(newResponseItem, responseItem.id)
        }
        if (
          config?.suggested_questions_after_answer?.enabled
          && !hasStopResponded.current
          && onGetSuggestedQuestions
        ) {
          try {
            const { data }: any = await onGetSuggestedQuestions(
              responseItem.id,
              newAbortController =>
                (suggestedQuestionsAbortControllerRef.current = newAbortController),
            )
            setSuggestQuestions(data)
          }
          catch (e) {
            setSuggestQuestions([])
          }
        }
      },
      onFile(file) {
        const lastThought
          = responseItem.agent_thoughts?.[responseItem.agent_thoughts?.length - 1]
        if (lastThought) {
          responseItem.agent_thoughts![responseItem.agent_thoughts!.length - 1].message_files
            = [...(lastThought as any).message_files, file]
        }

        updateCurrentQA({
          responseItem,
          questionId,
          placeholderAnswerId,
          questionItem,
        })
      },
      onThought(thought) {
        if (isCheckFailed || inWorkflow)
          return
        isAgentMode = true
        const response = responseItem as any
        if (thought.message_id && !hasSetResponseId)
          response.id = thought.message_id
        if (response.agent_thoughts.length === 0) {
          response.agent_thoughts.push(thought)
        }
        else {
          const lastThought = response.agent_thoughts[response.agent_thoughts.length - 1]
          // thought changed but still the same thought, so update.
          if (lastThought.id === thought.id) {
            thought.thought = lastThought.thought
            thought.message_files = lastThought.message_files
            responseItem.agent_thoughts![response.agent_thoughts.length - 1] = thought
          }
          else {
            responseItem.agent_thoughts!.push(thought)
          }
        }
        updateCurrentQA({
          responseItem,
          questionId,
          placeholderAnswerId,
          questionItem,
        })
      },
      onMessageEnd: (messageEnd) => {
        if (messageEnd.metadata?.annotation_reply) {
          responseItem.id = messageEnd.id
          responseItem.annotation = {
            id: messageEnd.metadata.annotation_reply.id,
            authorName: messageEnd.metadata.annotation_reply.account.name,
          }
          const baseState = chatListRef.current.filter(
            item => item.id !== responseItem.id && item.id !== placeholderAnswerId,
          )
          const newListWithAnswer = produce(baseState, (draft) => {
            if (!draft.find(item => item.id === questionId))
              draft.push({ ...questionItem })

            draft.push({
              ...responseItem,
            })
          })

          handleUpdateChatList(newListWithAnswer)
          return
        }
        responseItem.citation = messageEnd.metadata?.retriever_resources || []
        const processedFilesFromResponse = getProcessedFilesFromResponse(messageEnd.files || [])
        responseItem.allFiles = uniqBy(
          [...(responseItem.allFiles || []), ...(processedFilesFromResponse || [])],
          'id',
        )

        const newListWithAnswer = produce(
          chatListRef.current.filter(
            item => item.id !== responseItem.id && item.id !== placeholderAnswerId,
          ),
          (draft) => {
            if (!draft.find(item => item.id === questionId))
              draft.push({ ...questionItem })

            draft.push({ ...responseItem })
          },
        )
        console.log('newListWithAnswer', newListWithAnswer)
        handleUpdateChatList(newListWithAnswer)
      },
      onMessageReplace: (messageReplace) => {
        responseItem.content = messageReplace.answer
      },
      onCheckFailedReplace: () => {
        isCheckFailed = true
        isAgentMode = false
        responseItem.agent_thoughts = []
        responseItem.content = t('common.chat.checkFailText')
      },
      onError(e) {
        handleResponding(false)
        let newChatList
        // 图文审核不通过, 删除问题和回复
        if (e === 'checkcontent_result') {
          newChatList = produce(chatListRef.current, (draft) => {
            draft.splice(draft.findIndex(item => item.id === placeholderAnswerId), 1)
            draft.splice(draft.findIndex(item => item.id === questionId), 1)
          })
        }
        else {
          //  其他错误，删除回复
          newChatList = produce(chatListRef.current, (draft) => {
            draft.splice(draft.findIndex(item => item.id === placeholderAnswerId), 1)
          })
        }
        handleUpdateChatList(newChatList)
      },
      onWorkflowStarted: ({ workflow_run_id, task_id }) => {
        setTaskIdValue(task_id)
        responseItem.workflow_run_id = workflow_run_id
        responseItem.workflowProcess = {
          status: WorkflowRunningStatus.Running,
          tracing: [],
        }
        handleUpdateChatList(produce(chatListRef.current, (draft) => {
          const currentIndex = draft.findIndex(item => item.id === responseItem.id)
          draft[currentIndex] = {
            ...draft[currentIndex],
            ...responseItem,
          }
        }), false)
      },
      onWorkflowFinished: ({ data }) => {
        responseItem.workflowProcess!.status = data.status as WorkflowRunningStatus
        // 修复暂停聊天时，折叠详情内工作项还在转圈的问题
        if (data.status === WorkflowRunningStatus.Stopped) {
          responseItem.workflowProcess!.tracing.forEach((item) => {
            if (item.status === NodeRunningStatus.Running)
              item.status = NodeRunningStatus.Failed
          })
        }
        handleUpdateChatList(produce(chatListRef.current, (draft) => {
          const currentIndex = draft.findIndex(item => item.id === responseItem.id)
          draft[currentIndex] = {
            ...draft[currentIndex],
            ...responseItem,
          }
        }), false)
      },
      onIterationStart: ({ data }) => {
        responseItem.workflowProcess!.tracing!.push({
          ...data,
          status: WorkflowRunningStatus.Running,
        } as any)
        handleUpdateChatList(produce(chatListRef.current, (draft) => {
          const currentIndex = draft.findIndex(item => item.id === responseItem.id)
          draft[currentIndex] = {
            ...draft[currentIndex],
            ...responseItem,
          }
        }), false)
      },
      onIterationNext: ({ data }) => {
        const tracing = responseItem.workflowProcess!.tracing!
        const iterations = tracing.find(item => item.node_id === data.node_id
          && (item.execution_metadata?.parallel_id === data.execution_metadata?.parallel_id || item.parallel_id === data.execution_metadata?.parallel_id))!
        if (iterations.details)
          iterations.details!.push([])

        handleUpdateChatList(produce(chatListRef.current, (draft) => {
          const currentIndex = draft.length - 1
          if (responseItem)
            draft[currentIndex] = responseItem
        }), false)
      },
      onIterationFinish: ({ data }) => {
        const tracing = responseItem.workflowProcess!.tracing!
        const iterationIndex = tracing.findIndex(item => item.node_id === data.node_id
          && (item.execution_metadata?.parallel_id === data.execution_metadata?.parallel_id || item.parallel_id === data.execution_metadata?.parallel_id))!
        tracing[iterationIndex] = {
          ...tracing[iterationIndex],
          ...data,
          status: WorkflowRunningStatus.Succeeded,
        } as any

        handleUpdateChatList(produce(chatListRef.current, (draft) => {
          const currentIndex = draft.findIndex(item => item.id === responseItem.id)
          draft[currentIndex] = {
            ...draft[currentIndex],
            ...responseItem,
          }
        }), false)
      },
      onNodeStarted: ({ data }) => {
        if (data.iteration_id)
          return

        responseItem.workflowProcess!.tracing!.push({
          ...data,
          status: WorkflowRunningStatus.Running,
        } as any)
        handleUpdateChatList(produce(chatListRef.current, (draft) => {
          const currentIndex = draft.findIndex(item => item.id === responseItem.id)
          draft[currentIndex] = {
            ...draft[currentIndex],
            ...responseItem,
          }
        }), false)
      },
      onNodeFinished: ({ data }) => {
        if (data.iteration_id)
          return
        const currentIndex = responseItem.workflowProcess!.tracing!.findIndex(item => item.id === data.id)
        responseItem.workflowProcess!.tracing[currentIndex] = {
          ...(responseItem.workflowProcess!.tracing[currentIndex]?.extras
            ? { extras: responseItem.workflowProcess!.tracing[currentIndex].extras }
            : {}),
          ...data,
        } as any
        handleUpdateChatList(produce(chatListRef.current, (draft) => {
          const currentIndex = draft.findIndex(item => item.id === responseItem.id)
          draft[currentIndex] = {
            ...draft[currentIndex],
            ...responseItem,
          }
        }), false)
      },
      // 停止对话时终止聊天数据流接口
      getAbortController: (newAbortController: AbortController) => ssePostAbortControllerRef.current = newAbortController,
    }

    if (inWorkflow) {
      runfunc(bodyParams, chatHookConfig)
    }
    else {
      ssePost(
        url,
        {
          body: bodyParams,
        },
        chatHookConfig,
      )
    }
    return true
  }, [checkInputsForm, formSettings?.inputsForm, handleQuery, handleAiAnswer, params.token, params.appId, inWorkflow, notify, t, pathname, updateCurrentQA, handleResponding, config?.suggested_questions_after_answer?.enabled, handleUpdateChatList, updateChatListByApi, runfunc])
  // 关闭语音通话
  const closeVoiceCall = useCallback(async () => {
    setIsConnecting(false)
    setIsThinking(false)
    setIsVoiceConversation(false)
    finishCheck()
    handleUpdateChatList(chatListRef.current)

    // 处在工作流状态下，需要重置工作流状态
    if (inWorkflow) {
      const {
        setWorkflowRunningData,
      } = workflowStore.getState()
      setWorkflowRunningData({
        result: {
          id: '',
          status: WorkflowRunningStatus.Stopped,
        },
        tracing: [],
        resultText: '',
      })
    }
    // 断开RTC连接
    try {
      hasStopResponded.current = true
      handleResponding(false)
      if (stopChat && taskIdValue)
        stopChat(taskIdValue)
      if (conversationMessagesAbortControllerRef.current)
        conversationMessagesAbortControllerRef.current.abort()
      if (suggestedQuestionsAbortControllerRef.current)
        suggestedQuestionsAbortControllerRef.current.abort()
      if (!taskIdValue) {
        // 中止对话流式接口
        if (ssePostAbortControllerRef.current)
          ssePostAbortControllerRef.current.abort()
      }
      const client = voiceRef.current
      const microphoneAudioTrack = microphoneAudioTrackRef.current
      if (client)
        await client.leave()

      // 停止麦克风音频轨道
      if (microphoneAudioTrack) {
        microphoneAudioTrack.stop()
        microphoneAudioTrack.close()
      }
    }
    catch (error) {
      notify({ type: 'error', message: t('关闭语音通话失败') })
    }
  }, [finishCheck, handleResponding, handleUpdateChatList, inWorkflow, notify, stopChat, t, taskIdValue, workflowStore])
  // 停止对话
  const handleStop = useCallback((force?: boolean) => {
    hasStopResponded.current = true
    handleResponding(false)
    handleUpdateChatList(chatListRef.current)
    closeVoiceCall()
    if (stopChat && taskIdValue)
      stopChat(taskIdValue)
    if (conversationMessagesAbortControllerRef.current)
      conversationMessagesAbortControllerRef.current.abort()
    if (suggestedQuestionsAbortControllerRef.current)
      suggestedQuestionsAbortControllerRef.current.abort()
    if (!taskIdValue || force) {
      // 中止对话流式接口
      if (ssePostAbortControllerRef.current)
        ssePostAbortControllerRef.current.abort()
    }
  }, [handleResponding, handleUpdateChatList, closeVoiceCall, stopChat, taskIdValue])
  // 重新开始对话
  const handleRestart = useCallback(() => {
    conversationId.current = ''
    setTaskIdValue('')
    handleStop(true)
    closeVoiceCall()
    const newChatList = config?.opening_statement
      ? [
        {
          id: `${Date.now()}`,
          content: config.opening_statement,
          isAnswer: true,
          isOpeningStatement: true,
          suggestedQuestions: config.suggested_questions,
        },
      ]
      : []
    handleUpdateChatList(newChatList)
    setSuggestQuestions([])
  }, [closeVoiceCall, config, handleStop, handleUpdateChatList])
  // 打开语音通话
  const openVoiceCall = useCallback(async ({
    conversation_id,
    onGetConversationId,
    bodyParams,
  }: {
    conversation_id?: string
    onGetConversationId?: (conversationId: string) => void
    bodyParams?: Record<string, any>
  }) => {
    // 如果正在响应——直接return
    if (isRespondingRef.current) {
      notify({ type: 'info', message: t('appDebug.errorMessage.waitForResponse') })
      return
    }
    let queryConfig: Record<string, any> | undefined
    let responseItem: ChatItem | undefined
    let hasSetResponseId = false
    let isAgentMode = false
    let isCheckFailed = false
    const uid = Math.floor(10000000 + Math.random() * 90000000)
    let hasGetConversationIdFlag = false // 每次对话发送前重置已获取对话id状态
    // 设置状态连接中
    setIsConnecting(true)
    // 开始检查网络配置
    startCheck()
    // 在工作流状态，需要设置正在运行
    if (inWorkflow) {
      const {
        setWorkflowRunningData,
      } = workflowStore.getState()
      setWorkflowRunningData({
        result: {
          id: '',
          status: WorkflowRunningStatus.Running,
        },
        tracing: [],
        resultText: '',
      })
    }

    // 语音通道建立
    const onVoiceChannelBuild = async (data: VoiceChatConfig) => {
      const appId = data.app_id
      const channelId = data.channel_id
      const token = data.token || null
      const serverUserId = data.server_user_id
      import('agora-rtc-sdk-ng').then(async (module) => {
        try {
          const AgoraRTC = module.default
          // 建立rtc连接
          const client = AgoraRTC.createClient({ mode: 'rtc', codec: 'vp8' })
          voiceRef.current = client
          // 获取麦克风权限
          await navigator.mediaDevices.getUserMedia({ audio: true })
          // 加入频道
          await client!.join(appId, channelId, token, uid)
          // 创建麦克风音频轨道
          const microphoneAudioTrack = await AgoraRTC.createMicrophoneAudioTrack({
            encoderConfig: 'music_standard',
          })
          microphoneAudioTrackRef.current = microphoneAudioTrack
          // 发布音频轨道
          await client!.publish([microphoneAudioTrack])
          // 查找远程用户次数
          let findUserTime = 0
          const timer = setInterval(async () => {
          // 查找远程用户据
            const user = client!.remoteUsers.find(item => item.uid === serverUserId)
            if (user) {
              clearInterval(timer)
              await client!.subscribe(user, 'audio')
              user.audioTrack?.play()
              setIsVoiceConversation(true)
              setIsConnecting(false)
            }
            else {
              if (findUserTime > 30) {
                clearInterval(timer)
                notify({ type: 'error', message: t('无法加入频道，建议重新尝试') })
                closeVoiceCall()
              }
              findUserTime++
            }
          }, 1000)
        }
        catch (error) {
          if ((error as any).name === 'NotAllowedError')
            notify({ type: 'error', message: t('设备需开启权限后可以使用') })
          else
            notify({ type: 'error', message: t('网络异常，建议重新尝试') })
        }
      })
    }
    // 语音提问
    const onVoiceQuery = async (data: any) => {
      queryConfig = handleQuery({
        query: data.query,
      })
      if (!queryConfig)
        return
      const { placeholderAnswerId } = queryConfig
      responseItem = handleAiAnswer(placeholderAnswerId)
    }
    // 语音回答
    const onVoiceAnswer = async (message: string, isFirstMessage: boolean, { conversationId: newConversationId, messageId, taskId }: any) => {
      const questionId = queryConfig?.questionId
      const placeholderAnswerId = queryConfig?.placeholderAnswerId
      const questionItem = queryConfig?.questionItem

      if (!isAgentMode) {
        responseItem!.content = responseItem!.content + message
      }
      else {
        const lastThought = responseItem!.agent_thoughts?.[responseItem!.agent_thoughts?.length - 1]
        if (lastThought)
          lastThought.thought = lastThought.thought + message // need immer setAutoFreeze
      }
      // 然后的话，主要就是对这里面的内容去进行处理了
      if (messageId && !hasSetResponseId) {
        responseItem!.id = messageId
        hasSetResponseId = true
      }
      if (onGetConversationId && isFirstMessage && newConversationId && !hasGetConversationIdFlag && !conversation_id) {
        onGetConversationId(newConversationId)
        hasGetConversationIdFlag = true
      }
      if (isFirstMessage && newConversationId)
        conversationId.current = newConversationId
      setTaskIdValue(taskId)
      if (messageId)
        responseItem!.id = messageId
      // 对话中止后查询agent日志需要conversationId
      if (!responseItem!.conversationId && newConversationId)
        responseItem!.conversationId = newConversationId
      updateCurrentQA({
        responseItem: responseItem!,
        questionId,
        placeholderAnswerId,
        questionItem,
      })
    }
    // 语音通话提问安全围栏
    const onVoiceQuerySafetyFence = async (data: VoiceQuerySafetyFence) => {
      notify({
        type: 'error',
        message: data.message,
      })
    }
    // 语音通话回答安全围栏
    const onVoiceAnswerSafetyFence = async (data: VoiceAnswerSafetyFence) => {
      const { safety_pass } = data
      if (!safety_pass) {
        isCheckFailed = true
        isAgentMode = false
        responseItem!.agent_thoughts = []
        responseItem!.content = t('common.chat.checkFailText')
        handleUpdateChatList(produce(chatListRef.current, (draft) => {
          const currentIndex = draft.findIndex(item => item.id === responseItem!.id)
          draft[currentIndex] = {
            ...draft[currentIndex],
            ...responseItem,
          }
        }), false)
      }
    }
    // 语音思考
    const onThought = (thought: ThoughtItem) => {
      if (isCheckFailed)
        return
      setIsThinking(true)
      const questionId = queryConfig?.questionId
      const placeholderAnswerId = queryConfig?.placeholderAnswerId
      const questionItem = queryConfig?.questionItem
      isAgentMode = true
      const response = responseItem as any
      if (thought.message_id && !hasSetResponseId)
        response.id = thought.message_id
      if (response.agent_thoughts.length === 0) {
        response.agent_thoughts.push(thought)
      }
      else {
        const lastThought = response.agent_thoughts[response.agent_thoughts.length - 1]
        // thought changed but still the same thought, so update.
        if (lastThought.id === thought.id) {
          thought.thought = lastThought.thought
          thought.message_files = lastThought.message_files
          responseItem!.agent_thoughts![response.agent_thoughts.length - 1] = thought
        }
        else {
          responseItem!.agent_thoughts!.push(thought)
        }
      }
      updateCurrentQA({
        responseItem: responseItem!,
        questionId,
        placeholderAnswerId,
        questionItem,
      })
      setIsThinking(false)
    }
    // 单句语音终止
    const onMessageEnd = (messageEnd: any) => {
      console.log('onMessageEnd', messageEnd)
      const questionId = queryConfig?.questionId
      const placeholderAnswerId = queryConfig?.placeholderAnswerId
      const questionItem = queryConfig?.questionItem
      if (messageEnd.metadata?.annotation_reply) {
        responseItem!.id = messageEnd.id
        responseItem!.annotation = {
          id: messageEnd.metadata.annotation_reply.id,
          authorName: messageEnd.metadata.annotation_reply.account.name,
        }
        const baseState = chatListRef.current.filter(
          item => item.id !== responseItem!.id && item.id !== placeholderAnswerId,
        )
        const newListWithAnswer = produce(baseState, (draft) => {
          if (!draft.find(item => item.id === questionId))
            draft.push({ ...questionItem })

          draft.push({
            ...responseItem!,
          })
        })

        handleUpdateChatList(newListWithAnswer)
        return
      }
      responseItem!.citation = messageEnd.metadata?.retriever_resources || []
      const processedFilesFromResponse = getProcessedFilesFromResponse(messageEnd.files || [])
      responseItem!.allFiles = uniqBy(
        [...(responseItem!.allFiles || []), ...(processedFilesFromResponse || [])],
        'id',
      )
      const newListWithAnswer = produce(
        chatListRef.current.filter(
          item => item.id !== responseItem!.id && item.id !== placeholderAnswerId,
        ),
        (draft) => {
          if (!draft.find(item => item.id === questionId))
            draft.push({ ...questionItem })

          draft.push({ ...responseItem! })
        },
      )
      console.log('newListWithAnswer', newListWithAnswer)
      handleResponding(false)
      handleUpdateChatList(newListWithAnswer)
    }
    // 语音超时
    const onVoiceTimeout = () => {
      closeVoiceCall()
    }
    const onWorkflowStarted = ({ workflow_run_id, task_id }: { workflow_run_id: string; task_id: string }) => {
      setTaskIdValue(task_id)
      responseItem!.workflow_run_id = workflow_run_id!
      responseItem!.workflowProcess = {
        status: WorkflowRunningStatus.Running,
        tracing: [],
      }
      handleUpdateChatList(produce(chatListRef.current, (draft) => {
        const currentIndex = draft.findIndex(item => item.id === responseItem!.id)
        draft[currentIndex] = {
          ...draft[currentIndex],
          ...responseItem,
        }
      }), false)
    }
    const onWorkflowFinished = ({ data }: { data: any }) => {
      responseItem!.workflowProcess!.status = data.status as WorkflowRunningStatus
      // 修复暂停聊天时，折叠详情内工作项还在转圈的问题
      if (data.status === WorkflowRunningStatus.Stopped) {
        responseItem!.workflowProcess!.tracing.forEach((item) => {
          if (item.status === NodeRunningStatus.Running)
            item.status = NodeRunningStatus.Failed
        })
      }
      handleUpdateChatList(produce(chatListRef.current, (draft) => {
        const currentIndex = draft.findIndex(item => item.id === responseItem!.id)
        draft[currentIndex] = {
          ...draft[currentIndex],
          ...responseItem,
        }
      }), false)
    }
    const onIterationStart = ({ data }: { data: any }) => {
      responseItem!.workflowProcess!.tracing!.push({
        ...data,
        status: NodeRunningStatus.Running,
        details: [],
      } as any)
      handleUpdateChatList(produce(chatListRef.current, (draft) => {
        const currentIndex = draft.findIndex(item => item.id === responseItem!.id)
        draft[currentIndex] = {
          ...draft[currentIndex],
          ...responseItem,
        }
      }), false)
    }
    const onIterationNext = ({ data }: { data: any }) => {
      const tracing = responseItem!.workflowProcess!.tracing!
      const iterations = tracing.find(item => item.node_id === data.node_id
        && (item.execution_metadata?.parallel_id === data.execution_metadata?.parallel_id || item.parallel_id === data.execution_metadata?.parallel_id))!
      if (iterations.details)
        iterations.details!.push([])

      handleUpdateChatList(produce(chatListRef.current, (draft) => {
        const currentIndex = draft.length - 1
        draft[currentIndex] = responseItem!
      }), false)
    }
    const onIterationFinish = ({ data }: { data: any }) => {
      const tracing = responseItem!.workflowProcess!.tracing!
      const iterationsIndex = tracing.findIndex(item => item.node_id === data.node_id
        && (item.execution_metadata?.parallel_id === data.execution_metadata?.parallel_id || item.parallel_id === data.execution_metadata?.parallel_id))!
      tracing[iterationsIndex] = {
        ...tracing[iterationsIndex],
        ...data,
        status: NodeRunningStatus.Succeeded,
      } as any
      handleUpdateChatList(produce(chatListRef.current, (draft) => {
        const currentIndex = draft.length - 1
        draft[currentIndex] = responseItem!
      }), false)
    }
    const onNodeStarted = ({ data }: { data: any }) => {
      if (data.iteration_id)
        return

      responseItem!.workflowProcess!.tracing!.push({
        ...data,
        status: NodeRunningStatus.Running,
      } as any)
      handleUpdateChatList(produce(chatListRef.current, (draft) => {
        const currentIndex = draft.findIndex(item => item.id === responseItem!.id)
        draft[currentIndex] = {
          ...draft[currentIndex],
          ...responseItem,
        }
      }), false)
    }
    const onNodeFinished = ({ data }: { data: any }) => {
      if (data.iteration_id)
        return

      const currentIndex = responseItem!.workflowProcess!.tracing!.findIndex((item) => {
        if (!item.execution_metadata?.parallel_id)
          return item.node_id === data.node_id
        return item.node_id === data.node_id && (item.execution_metadata?.parallel_id === data.execution_metadata?.parallel_id || item.parallel_id === data.execution_metadata?.parallel_id)
      })
      responseItem!.workflowProcess!.tracing[currentIndex] = {
        ...(responseItem!.workflowProcess!.tracing[currentIndex]?.extras
          ? { extras: responseItem!.workflowProcess!.tracing[currentIndex].extras }
          : {}),
        ...data,
      } as any
      handleUpdateChatList(produce(chatListRef.current, (draft) => {
        const currentIndex = draft.findIndex(item => item.id === responseItem!.id)
        draft[currentIndex] = {
          ...draft[currentIndex],
          ...responseItem,
        }
      }), false)
    }
    // 判断在是在调试区还是应用广场
    let isPublicAPI = false
    if (Object.keys(params).includes('token'))
      isPublicAPI = true

    let appId = ''
    if (Object.keys(params).includes('appId'))
      appId = params.appId as string

    try {
      // 发送sse请求，获取频道id
      await sendVoiceChatMessage({
        onVoiceChannelBuild,
        onVoiceQuery,
        onVoiceAnswer,
        onThought,
        onMessageEnd,
        getAbortController: (newAbortController: AbortController) => ssePostAbortControllerRef.current = newAbortController,
        onVoiceTimeout,
        onError: () => {
          closeVoiceCall()
          notify({ type: 'error', message: t('网络异常，建议重新尝试') })
        },
        onWorkflowStarted,
        onWorkflowFinished,
        onIterationStart,
        onIterationNext,
        onIterationFinish,
        onNodeStarted,
        onNodeFinished,
        isPublicAPI,
        onVoiceAnswerSafetyFence,
        onVoiceQuerySafetyFence,
      }, appId, {
        model_config: {
          ...bodyParams,
        },
        rtc_user_id: uid,
      })
    }
    catch (error) {
      closeVoiceCall()
      notify({ type: 'error', message: t('网络异常，建议重新尝试') })
    }
  }, [
    closeVoiceCall,
    handleAiAnswer,
    handleQuery,
    handleResponding,
    handleUpdateChatList,
    inWorkflow,
    notify,
    params,
    startCheck,
    t,
    updateCurrentQA,
    workflowStore,
  ])
  // 标注编辑、新增、删除
  const handleAnnotationEdited = useCallback((query: string, answer: string, index: number) => {
    handleUpdateChatList(chatListRef.current.map((item, i) => {
      if (i === index - 1) {
        return {
          ...item,
          content: query,
        }
      }
      if (i === index) {
        return {
          ...item,
          content: answer,
          annotation: {
            ...item.annotation,
            logAnnotation: undefined,
          } as any,
        }
      }
      return item
    }))
  }, [handleUpdateChatList])
  const handleAnnotationAdded = useCallback(
    (annotationId: string, authorName: string, query: string, answer: string, index: number) => {
      handleUpdateChatList(
        chatListRef.current.map((item, i) => {
          if (i === index - 1) {
            return {
              ...item,
              content: query,
            }
          }
          if (i === index) {
            const answerItem = {
              ...item,
              content: item.content,
              annotation: {
                id: annotationId,
                authorName,
                logAnnotation: {
                  content: answer,
                  account: {
                    id: '',
                    name: authorName,
                    email: '',
                  },
                },
              } as Annotation,
            }
            return answerItem
          }
          return item
        }),
      )
    },
    [handleUpdateChatList],
  )
  const handleAnnotationRemoved = useCallback(
    (index: number) => {
      handleUpdateChatList(
        chatListRef.current.map((item, i) => {
          if (i === index) {
            return {
              ...item,
              content: item.content,
              annotation: {
                ...(item.annotation || {}),
                id: '',
              } as Annotation,
            }
          }
          return item
        }),
      )
    },
    [handleUpdateChatList],
  )
  // 处理web页，隐藏自动保存，展示自动刷新
  const CloseVoiceCallWhenPageClose = useCallback(() => {
    if (document.visibilityState === 'hidden' && (isVoiceConversation || isConnecting))
      closeVoiceCall()
  }, [closeVoiceCall, isConnecting, isVoiceConversation])
  useEffect(() => {
    setAutoFreeze(false)
    return () => {
      setAutoFreeze(true)
    }
  }, [])
  useEffect(() => {
    if (prevChatList?.length)
      handleUpdateChatList(prevChatList)
    if (config?.opening_statement) {
      handleUpdateChatList(produce(chatListRef.current, (draft) => {
        const index = draft.findIndex(item => item.isOpeningStatement)

        if (index > -1) {
          draft[index] = {
            ...draft[index],
            content: getIntroduction(config.opening_statement),
            suggestedQuestions: config.suggested_questions,
          }
        }
        else {
          draft.unshift({
            id: `${Date.now()}`,
            content: getIntroduction(config.opening_statement),
            isAnswer: true,
            isOpeningStatement: true,
            suggestedQuestions: config.suggested_questions,
          })
        }
      }))
    }
  }, [prevChatList, config?.opening_statement, getIntroduction, config?.suggested_questions, handleUpdateChatList])
  useEffect(() => {
    if ((isConnecting || isVoiceConversation) && !online) {
      notify({ type: 'error', message: t('网络异常，建议重新尝试') })
      closeVoiceCall()
    }
  }, [closeVoiceCall, isConnecting, isVoiceConversation, notify, online, t])
  useEffect(() => {
    document.addEventListener('visibilitychange', CloseVoiceCallWhenPageClose)
    return () => {
      document.removeEventListener('visibilitychange', CloseVoiceCallWhenPageClose)
    }
  }, [CloseVoiceCallWhenPageClose, closeVoiceCall])
  useEffect(() => {
    return () => {
      closeVoiceCall()
    }
  }, [])

  return {
    chatList,
    setChatList,
    chatListRef,
    currentChatList,
    setCurrentChatList,
    conversationId: conversationId.current,
    isResponding,
    setIsResponding,
    suggestedQuestions,
    isVoiceConversation,
    isConnecting,
    isThinking,
    handleUpdateChatList,
    handleSend,
    handleRestart,
    handleStop,
    handleAnnotationEdited,
    handleAnnotationAdded,
    handleAnnotationRemoved,
    closeVoiceCall,
    openVoiceCall,
  }
}

export function useChatStatic<T>(selector: (state: ChatStaticState) => T): T {
  const store = useContext(ChatStaticContext)
  if (!store)
    throw new Error('Missing FeaturesContext.Provider in the tree')

  return useStore(store, selector)
}
