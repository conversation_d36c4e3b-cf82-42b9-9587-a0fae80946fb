import type {
  FC,
  ReactNode,
} from 'react'
import {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { debounce } from 'lodash-es'
import { useShallow } from 'zustand/react/shallow'
import ChatIntro from './components/chat-intro'
import Question from './components/question'
import ChatList from './components/chat-list'
import Answer from './components/answer'
import ChatInputArea from './components/chat-input-area'
import TryToAsk from './components/try-to-ask'
import { ChatContextProvider } from './context'
import { useChatStatic } from './hooks'
import type { ChatItem, InputForm } from '@/types/chat'
import cn from '@/utils/classnames'
import AgentLogModal from '@/app/components/base/log-modal/agent'
import PromptLogModal from '@/app/components/base/log-modal/prompt'
import Loading from '@/app/components/base/loading'
import { useStore as useAppStore } from '@/app/components/app/store'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'

export type ChatProps = {
  className?: string
  chatContainerClassName?: string
  chatContainerInnerClassName?: string
  chatFooterClassName?: string
  chatFooterInnerClassName?: string
  showFileUpload?: boolean
  chartHasPrev?: boolean
  noStopResponding?: boolean
  // 对话列表
  chatList: ChatItem[]
  // 正在响应的对话列表
  currentChatList?: ChatItem[]
  // 输入值
  inputs?: Record<string, any>
  // 输入表单配置
  inputsForm?: InputForm[]
  // 尝试问列表
  suggestedQuestions?: string[]
  // 聊天信息前展示节点
  chatNode?: ReactNode
  // 是否正在响应
  isResponding?: boolean
  // AI思考中
  isThinking?: boolean
  // 加载中
  loading?: boolean
  // 停止响应事件
  onStopResponding?: () => void
  // 语音通话取消事件
  onCancelVoiceCall?: () => void
  // 处理新增对话
  handleNewConversation?: () => void
}

const Chat: FC<ChatProps> = ({
  className,
  chatContainerClassName,
  chatContainerInnerClassName,
  chatFooterClassName,
  chatFooterInnerClassName,
  inputs,
  inputsForm,
  chatList,
  currentChatList = [],
  isResponding,
  isThinking,
  noStopResponding,
  suggestedQuestions,
  chatNode,
  chartHasPrev = false,
  loading,
  onStopResponding,
  handleNewConversation,
  onCancelVoiceCall,
}) => {
  const {
    currentLogItem,
    setCurrentLogItem,
    showPromptLogModal,
    setShowPromptLogModal,
    showAgentLogModal,
    setShowAgentLogModal,
  } = useAppStore(
    useShallow(state => ({
      currentLogItem: state.currentLogItem,
      setCurrentLogItem: state.setCurrentLogItem,
      showPromptLogModal: state.showPromptLogModal,
      setShowPromptLogModal: state.setShowPromptLogModal,
      showAgentLogModal: state.showAgentLogModal,
      setShowAgentLogModal: state.setShowAgentLogModal,
    })),
  )
  const {
    config,
    appData,
    noChatInput,
    hideLogModal,
    handleStop,
    onSend,
  } = useChatStatic(
    useShallow(state => ({
      config: state.config,
      appData: state.appData,
      noChatInput: state.noChatInput,
      hideLogModal: state.hideLogModal,
      onSend: state.onSend,
      handleStop: state.onCloseVoice,
    })),
  )
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  const chatWrapRef = useRef<HTMLDivElement>(null)
  const chatIntroRef = useRef<HTMLDivElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const chatContainerInnerRef = useRef<HTMLDivElement>(null)
  const chatFooterRef = useRef<HTMLDivElement>(null)
  const chatFooterInnerRef = useRef<HTMLDivElement>(null)
  const scrollCheckRef = useRef<boolean>(false)
  const scrollCheckTimeoutRef = useRef<NodeJS.Timeout>()

  const appId = useMemo(() => (appData?.app_id || appData?.id || ''), [appData?.app_id, appData?.id])
  const visionConfig = useMemo(() => (config?.file_upload), [config])
  const voiceInput = useMemo(() => (config?.text_to_speech?.voice_input), [config])
  const voiceConversation = useMemo(() => (config?.text_to_speech?.voice_conversation), [config])

  // 聊天对话窗口宽度
  const [width, setWidth] = useState(0)
  // 用户是否滚动
  const userScrolledRef = useRef(false)
  // 是否显示滚动到底部按钮
  const [showScrollButton, setShowScrollButton] = useState(false)
  // 是否拥有尝试去问
  const hasTryToAsk
        = config?.suggested_questions_after_answer?.enabled && !!suggestedQuestions?.length && onSend
  // 对话列表为空、或者只有一条，且为开场白
  const onlyOepnState
        = chatList.length === 0 || (chatList.length === 1 && chatList[0].isOpeningStatement)

  // 底部输入框毛玻璃
  const foooterBg = {
    background: 'rgba(240, 245, 255, 0.10)',
    backdropFilter: 'blur(25px)',
    paddingTop: '12px',
    paddingBottom: '12px',
    bottom: '0px',
  }

  // 处理滚动到底部
  const handleScrollToBottom = useCallback(() => {
    if (chatList.length > 1 && chatContainerRef.current && !userScrolledRef.current)
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
  }, [chatList.length])
  // 处理屏幕尺寸变化
  const handleWindowResize = useCallback(() => {
    if (chatContainerRef.current)
      setWidth(document.body.clientWidth - (chatContainerRef.current?.clientWidth + 16) - 8)
    if (chatContainerRef.current && chatFooterRef.current)
      chatFooterRef.current.style.width = isMobile ? '100%' : `${chatContainerRef.current.clientWidth}px`
    if (chatContainerInnerRef.current && chatFooterInnerRef.current)
      chatFooterInnerRef.current.style.width = `${chatContainerInnerRef.current.clientWidth}px`
    else if (chatIntroRef.current && chatFooterInnerRef.current)
      chatFooterInnerRef.current.style.width = `${chatIntroRef.current.clientWidth}px`
  }, [isMobile])
  // 点击滚动到底部按钮
  const clickScrollToBottom = useCallback(() => {
    if (!chatContainerRef.current)
      return
    chatContainerRef.current.scrollTo({
      top: chatContainerRef.current.scrollHeight,
      behavior: 'smooth',
    })
  }, [])

  const hasNewConversationBtn = useMemo(() => {
    return !!handleNewConversation
  }, [handleNewConversation])

  useEffect(() => {
    if (chatContainerRef.current && !scrollCheckRef.current) {
      requestAnimationFrame(() => {
        handleScrollToBottom()
        handleWindowResize()
      })
    }
  })
  useEffect(() => {
    window.addEventListener('resize', debounce(handleWindowResize))
    return () => window.removeEventListener('resize', handleWindowResize)
  }, [handleWindowResize])
  useEffect(() => {
    if (chatFooterRef.current && chatContainerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { blockSize } = entry.borderBoxSize[0]

          // chatContainerRef.current!.style.paddingBottom = `${blockSize}px`
          if (chatWrapRef.current)
            chatWrapRef.current.style.paddingBottom = `${blockSize + 24}px`
          handleScrollToBottom()
        }
      })

      resizeObserver.observe(chatFooterRef.current)

      return () => {
        resizeObserver.disconnect()
      }
    }
  }, [handleScrollToBottom, loading])
  useEffect(() => {
    const chatContainer = chatContainerRef.current
    if (chatContainer) {
      const setUserScrolled = () => {
        if (chatContainer)
          userScrolledRef.current = chatContainer.scrollHeight - chatContainer.scrollTop >= chatContainer.clientHeight + 300
      }
      const handleScroll = debounce(() => {
        if (!isMobile)
          return
        if (scrollCheckTimeoutRef.current)
          clearTimeout(scrollCheckTimeoutRef.current)

        scrollCheckTimeoutRef.current = setTimeout(() => {
          const hasScroll = chatContainer.scrollHeight > chatContainer.clientHeight
          const isNotAtBottom = chatContainer.scrollHeight - chatContainer.scrollTop - chatContainer.clientHeight > 100
          scrollCheckRef.current = hasScroll && isNotAtBottom
          setShowScrollButton(hasScroll && isNotAtBottom)
        }, 100)
      }, 100)
      chatContainer.addEventListener('scroll', setUserScrolled)
      chatContainer.addEventListener('scroll', handleScroll)
      return () => {
        chatContainer.removeEventListener('scroll', setUserScrolled)
        chatContainer.removeEventListener('scroll', handleScroll)
      }
    }
  }, [isMobile, loading])
  // 切换页面关闭语音播放
  useEffect(() => {
    return () => {
      handleStop?.()
    }
  }, [])

  return (
    <ChatContextProvider chatList={chatList}>
      {loading
        ? <Loading></Loading>
        : (
          <div ref={chatWrapRef} className={cn('relative h-full py-5', className)}>
            {/* 聊天对话容器 */}
            <div
              ref={chatContainerRef}
              className={cn(
                'relative h-full overflow-y-auto overflow-x-hidden mb-5',
                chatContainerClassName,
              )}
            >
              {/* 前缀节点 */}
              <div className={cn('w-full', chatContainerInnerClassName)}>
                {chatNode}
              </div>
              {/* 对话未开启时展示界面 展示应用信息和开场白预设问题之类的内容 */}
              {onlyOepnState && !chartHasPrev && !isResponding && (
                <ChatIntro ref={chatIntroRef} className={chatContainerInnerClassName} />
              )}
              {/* 对话列表 */}
              {(!onlyOepnState || chartHasPrev || isResponding) && (
                <div
                  ref={chatContainerInnerRef}
                  className={cn('w-full', chatContainerInnerClassName)}
                >
                  <ChatList chatList={chatList}></ChatList>
                  {/* 当前对话 */}
                  {currentChatList.map((item, index) => {
                    if (item.isAnswer) {
                      return (
                        <Answer
                          key={item.id}
                          item={item}
                          question={chatList[index - 1]?.content}
                          index={index}
                          responding={isResponding}
                        />
                      )
                    }
                    return (
                      <Question key={item.id} item={item}/>
                    )
                  })}

                </div>
              )}
            </div>
            {/* 底部输入框 */}
            <div
              className={`absolute ${
                (hasTryToAsk || !noChatInput || !noStopResponding) && chatFooterClassName
              }`}
              style={isMobile ? foooterBg : {}}
              ref={chatFooterRef}
            >
              <div ref={chatFooterInnerRef} className={cn('relative', chatFooterInnerClassName)}>
                {hasTryToAsk && (
                  <TryToAsk
                    questionsClassName={hasNewConversationBtn ? 'ml-[44px]' : ''}
                    suggestedQuestions={suggestedQuestions}
                  />
                )}
                {!noChatInput && (
                  <ChatInputArea
                    appId={appId}
                    visionConfig={visionConfig}
                    voiceInput={voiceInput}
                    voiceConversation={voiceConversation}
                    isResponding={isResponding}
                    inputs={inputs}
                    inputsForm={inputsForm}
                    handleNewConversation={handleNewConversation}
                    onStopResponding={onStopResponding}
                    onCancelVoiceCall={onCancelVoiceCall}
                  />
                )}
              </div>
            </div>
            {showPromptLogModal && !hideLogModal && (
              <PromptLogModal
                width={width}
                currentLogItem={currentLogItem}
                onCancel={() => {
                  setCurrentLogItem()
                  setShowPromptLogModal(false)
                }}
              />
            )}
            {showAgentLogModal && !hideLogModal && (
              <AgentLogModal
                width={width}
                currentLogItem={currentLogItem}
                onCancel={() => {
                  setCurrentLogItem()
                  setShowAgentLogModal(false)
                }}
              />
            )}
          </div>
        )}
      {/* Fix mixed operators */}
      {(isMobile && showScrollButton)
        ? (
          <div
            className='fixed right-[20px] bottom-[140px] cursor-pointer hover:opacity-80 transition-opacity'
            onClick={clickScrollToBottom}
          >
            <img src="/assets/icons/scroll-bot.png" alt="scroll to bottom" />
          </div>
        )
        : null}
    </ChatContextProvider>
  )
}

export default memo(Chat)
