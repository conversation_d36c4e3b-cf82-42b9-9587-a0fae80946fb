'use client'

import { type ReactNode, createContext as createZustandContext, useRef } from 'react'
import { createContext, useContext, useContextSelector } from 'use-context-selector'
import { isEqual } from 'lodash-es'
import type { ChatStaticState, ChatStaticStore } from './store'
import { CreateChatStaticStore } from './store'
import type { ChatProps } from './index'
import type { ChatItem } from '@/types/chat'

export type ChatContextValue = Pick<
ChatProps,
  | 'config'
  | 'isResponding'
  | 'noChatInput'
  | 'chatList'
  | 'showPromptLog'
  | 'questionIcon'
  | 'answerIcon'
  | 'onSend'
  | 'onRegenerate'
  | 'onAnnotationEdited'
  | 'onAnnotationAdded'
  | 'onAnnotationRemoved'
  | 'onFeedback'
  | 'showInputBottomTip'
  | 'isVoiceCall'
  | 'onVoiceCall'
  | 'onCancelVoiceCall'
  | 'onStopResponding'
  | 'handleNewConversation'
  | 'isConnecting'
  | 'isThinking'
  | 'onPlayVoice'
> & {
  allChatList?: ChatItem[]
}

const ChatContext = createContext<ChatContextValue>({
  chatList: [],
  allChatList: [],
})

type ChatContextProviderProps = {
  children: ReactNode
} & ChatContextValue

export const ChatContextProvider = ({
  children,
  config,
  chatList,
  allChatList,
  showPromptLog,
  questionIcon,
  answerIcon,

  // 决策部分
  isResponding,
  isVoiceCall,
  showInputBottomTip = true,
  isConnecting,
  isThinking,
  noChatInput,
  // 事件部分
  onSend,
  onStopResponding,
  onRegenerate,
  onAnnotationEdited,
  onAnnotationAdded,
  onAnnotationRemoved,
  onFeedback,
  onVoiceCall,
  onCancelVoiceCall,
  handleNewConversation,
  onPlayVoice,
}: ChatContextProviderProps) => {
  return (
    <ChatContext.Provider value={{
      config,
      chatList: chatList || [],
      allChatList: allChatList || [],
      showPromptLog,
      questionIcon,
      answerIcon,
      showInputBottomTip,
      isResponding,
      isVoiceCall,
      isConnecting,
      isThinking,
      noChatInput,
      onSend,
      onRegenerate,
      onAnnotationEdited,
      onAnnotationAdded,
      onAnnotationRemoved,
      onFeedback,
      onVoiceCall,
      onCancelVoiceCall,
      onStopResponding,
      handleNewConversation,
      onPlayVoice,
    }}>
      {children}
    </ChatContext.Provider>
  )
}

export const useChatContext = () => useContext(ChatContext)

// 自定义 Hook，用于选择性订阅
// eslint-disable-next-line @typescript-eslint/comma-dangle
export const useChatContextSelector = <T,>(selector: (state: ChatContextValue) => T): T =>
  useContextSelector(ChatContext, selector)

export default ChatContext

type ChatStaticProviderProps = {
  children: React.ReactNode
} & Partial<ChatStaticState>

export const ChatStaticContext = createZustandContext<ChatStaticStore | null>(null)
export const ChatStaticProvider = ({ children, ...props }: ChatStaticProviderProps) => {
  const storeRef = useRef<ChatStaticStore>()

  if (!storeRef.current) {
    storeRef.current = CreateChatStaticStore(props)
  }
  else {
    if (!isEqual(storeRef.current.getState().config, props.config))
      storeRef.current.getState().setConfig!(props.config!)
    if (!isEqual(storeRef.current.getState().appData, props.appData))
      storeRef.current.getState().setAppData!(props.appData!)
    if (!isEqual(storeRef.current.getState().answerIcon, props.answerIcon))
      storeRef.current.getState().setAnswerIcon!(props.answerIcon!)
    if (!isEqual(storeRef.current.getState().isVoiceCall, props.isVoiceCall))
      storeRef.current.getState().setIsVoiceCall!(props.isVoiceCall!)
    if (!isEqual(storeRef.current.getState().isConnecting, props.isConnecting))
      storeRef.current.getState().setIsConnecting!(props.isConnecting!)
    if (!isEqual(storeRef.current.getState().hasTryToAsk, props.hasTryToAsk))
      storeRef.current.getState().setHasTryToAsk!(props.hasTryToAsk!)
    if (!isEqual(storeRef.current.getState().onSend, props.onSend))
      storeRef.current.getState().setOnSend!(props.onSend!)
    if (!isEqual(storeRef.current.getState().onCloseVoice, props.onCloseVoice))
      storeRef.current.getState().setOnCloseVoice!(props.onCloseVoice!)
    if (!isEqual(storeRef.current.getState().onPlayVoice, props.onPlayVoice))
      storeRef.current.getState().setOnPlayVoice!(props.onPlayVoice!)
    if (!isEqual(storeRef.current.getState().onFeedback, props.onFeedback))
      storeRef.current.getState().setOnFeedback!(props.onFeedback!)
    if (!isEqual(storeRef.current.getState().onVoiceCall, props.onVoiceCall))
      storeRef.current.getState().setOnVoiceCall!(props.onVoiceCall!)
  }

  return (
    <ChatStaticContext.Provider value={storeRef.current}>
      {children}
    </ChatStaticContext.Provider>
  )
}
