import type {
  FC,
  ReactNode,
} from 'react'
import {
  memo,
} from 'react'
import OldChat from '../chat'
import { ChatStaticProvider } from './context'
import NewChat from './new-chat-content'
import type { ChatConfig, ChatItem, Feedback, InputForm, OnRegenerate, OnSend } from '@/types/chat'
import type { AppData } from '@/types/share'
// 公共能力
import { useVoiceTts } from '@/app/components/base/voice-service/voice-tts/hooks'

export type ChatProps = {
  className?: string
  chatContainerClassName?: string
  chatContainerInnerClassName?: string
  chatFooterClassName?: string
  chatFooterInnerClassName?: string
  chatAnswerContainerInner?: string
  hideProcessDetail?: boolean
  hideLogModal?: boolean
  showFileUpload?: boolean
  chartHasPrev?: boolean
  showPromptLog?: boolean
  noStopResponding?: boolean
  // 应用数据
  appData?: AppData
  // 对话列表
  chatList: ChatItem[]
  // 正在响应的对话列表
  currentChatList?: ChatItem[]
  // 聊天配置
  config?: ChatConfig
  // 是否存在聊天输入
  noChatInput?: boolean
  // 输入值
  inputs?: Record<string, any>
  // 输入表单配置
  inputsForm?: InputForm[]
  // 是否为公共API
  isPublicAPI?: boolean
  // 尝试问列表
  suggestedQuestions?: string[]
  // 问题图标
  questionIcon?: ReactNode
  // 回答图标
  answerIcon?: string
  // 聊天信息前展示节点
  chatNode?: ReactNode
  // 是否显示底部提示
  showInputBottomTip?: boolean
  // 是否正在响应
  isResponding?: boolean
  // 是否正在语音通话
  isVoiceCall?: boolean
  // AI思考中
  isThinking?: boolean
  // 语音通话连接中
  isConnecting?: boolean
  // 加载中
  loading?: boolean
  // 发送事件
  onSend?: OnSend
  // 重新生成事件
  onRegenerate?: OnRegenerate
  // 停止响应事件
  onStopResponding?: () => void
  // 编辑标注
  onAnnotationEdited?: (question: string, answer: string, index: number) => void
  // 添加标注
  onAnnotationAdded?: (annotationId: string, authorName: string, question: string, answer: string, index: number) => void
  // 删除标注事件
  onAnnotationRemoved?: (index: number) => void
  // 语音通话事件
  onVoiceCall?: () => void
  // 语音通话取消事件
  onCancelVoiceCall?: () => void
  // 处理新增对话
  handleNewConversation?: () => void
  // 语音播放
  onPlayVoice?: (content: string, id?: string) => void
  // 反馈回调函数
  onFeedback?: (messageId: string, feedback: Feedback) => void
}

const Chat: FC<ChatProps> = ({
  className,
  appData,
  config,
  onSend,
  inputs,
  isVoiceCall,
  inputsForm,
  isPublicAPI = false,
  onRegenerate,
  chatList,
  currentChatList = [],
  isResponding,
  isThinking,
  isConnecting,
  noStopResponding,
  onStopResponding,
  noChatInput,
  chatContainerClassName,
  chatContainerInnerClassName,
  chatFooterClassName,
  chatFooterInnerClassName,
  suggestedQuestions,
  showPromptLog,
  questionIcon,
  answerIcon,
  chatNode,
  onFeedback,
  chatAnswerContainerInner,
  hideProcessDetail,
  hideLogModal,
  chartHasPrev = false,
  showInputBottomTip = true,
  loading,
  handleNewConversation,
  onVoiceCall,
  onCancelVoiceCall,
}) => {
  const appId = appData?.app_id || appData?.id || ''
  // 是否是长文档应用
  const isLongWeb = Boolean(inputsForm?.find(form => form.type === 'longTxt'))
  const {
    handlePlay,
    handleStop,
  } = useVoiceTts(appId, isPublicAPI, config?.text_to_speech)

  return (
    !isLongWeb
      ? <ChatStaticProvider
        chatAnswerContainerInner={chatAnswerContainerInner}
        config={config}
        appData={appData}
        isVoiceCall={isVoiceCall}
        isConnecting={isConnecting}
        noChatInput={noChatInput}
        isPublicAPI={isPublicAPI}
        questionIcon={questionIcon}
        answerIcon={answerIcon}
        hideProcessDetail={hideProcessDetail}
        hideLogModal={hideLogModal}
        showPromptLog={showPromptLog}
        showInputBottomTip={showInputBottomTip}
        onSend={onSend}
        onCloseVoice={handleStop}
        onFeedback={onFeedback}
        onRegenerate={onRegenerate}
        onPlayVoice={handlePlay}
        onVoiceCall={onVoiceCall}
      >
        <NewChat
          className={className}
          chatContainerClassName={chatContainerClassName}
          chatContainerInnerClassName={chatContainerInnerClassName}
          chatFooterClassName={chatFooterClassName}
          chatFooterInnerClassName={chatFooterInnerClassName}
          inputs={inputs}
          inputsForm={inputsForm}
          chatList={chatList}
          currentChatList={currentChatList}
          isResponding={isResponding}
          isThinking={isThinking}
          suggestedQuestions={suggestedQuestions}
          chatNode={chatNode}
          loading={loading}
          onStopResponding={onStopResponding}
          handleNewConversation={handleNewConversation}
          onCancelVoiceCall={onCancelVoiceCall}
          chartHasPrev={chartHasPrev}
          noStopResponding={noStopResponding}
        ></NewChat>
      </ChatStaticProvider>
      : <OldChat
        className={className}
        appData={appData}
        config={config}
        onSend={onSend}
        inputs={inputs}
        isVoiceCall={isVoiceCall}
        inputsForm={inputsForm}
        isPublicAPI={isPublicAPI}
        onRegenerate={onRegenerate}
        chatList={chatList}
        currentChatList={currentChatList}
        isResponding={isResponding}
        isThinking={isThinking}
        isConnecting={isConnecting}
        noStopResponding={noStopResponding}
        onStopResponding={onStopResponding}
        noChatInput={noChatInput}
        chatContainerClassName={chatContainerClassName}
        chatContainerInnerClassName={chatContainerInnerClassName}
        chatFooterClassName={chatFooterClassName}
        chatFooterInnerClassName={chatFooterInnerClassName}
        suggestedQuestions={suggestedQuestions}
        showPromptLog={showPromptLog}
        questionIcon={questionIcon}
        answerIcon={answerIcon}
        chatNode={chatNode}
        onFeedback={onFeedback}
        chatAnswerContainerInner={chatAnswerContainerInner}
        hideProcessDetail={hideProcessDetail}
        hideLogModal={hideLogModal}
        chartHasPrev={chartHasPrev}
        showInputBottomTip={showInputBottomTip}
        loading={loading}
        handleNewConversation={handleNewConversation}
        onVoiceCall={onVoiceCall}
        onCancelVoiceCall={onCancelVoiceCall}
      ></OldChat>
  )
}

export default memo(Chat)
