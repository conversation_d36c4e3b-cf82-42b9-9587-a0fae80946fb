import { createStore } from 'zustand'
import { type ReactNode } from 'react'
import type { ChatConfig, Feedback, OnRegenerate, OnSend } from '@/types/chat'
import type { AppData } from '@/types/share'

export type ChatStaticState = {
  // 聊天配置
  config?: ChatConfig
  // 应用数据
  appData?: AppData
  // 是否显示输入框
  noChatInput?: boolean
  // 是否是公共api
  isPublicAPI?: boolean
  // 问题头像
  questionIcon?: ReactNode
  // 回答头像
  answerIcon?: string
  // 隐藏处理详情
  hideProcessDetail?: boolean
  // 隐藏日志弹窗
  hideLogModal?: boolean
  // 是否显示底部提示
  showInputBottomTip?: boolean
  // 回答样式
  chatAnswerContainerInner?: string
  // 是否显示提示词日志
  showPromptLog?: boolean
  // 是否正在语音通话
  isVoiceCall?: boolean
  // 语音通话是否正在连接
  isConnecting?: boolean
  // 是否用用追问
  hasTryToAsk?: boolean
  // 发送消息动作
  onSend?: OnSend
  // 重新生成
  onRegenerate?: OnRegenerate
  // 关闭语音播放
  onCloseVoice?: () => void
  // 语音播放
  onPlayVoice?: ((content: string, id?: string) => void)
  // 反馈动作
  onFeedback?: ((messageId: string, feedback: Feedback) => void)
  // 语音通话
  onVoiceCall?: (() => void)
}
export type ChatStaticAction = {
  // 设置聊天配置
  setConfig?: (config: ChatConfig) => void
  // 更新应用数据
  setAppData?: (appData: AppData) => void
  // 更新回答头像
  setAnswerIcon?: (answerIcon: string) => void
  // 更新是否正在语音通话
  setIsVoiceCall?: (isVoiceCall: boolean) => void
  // 更新是否正在连接
  setIsConnecting?: (isConnecting: boolean) => void
  // 更新是否启用追问
  setHasTryToAsk?: (hasTryToAsk: boolean) => void
  // 更新发送消息动作
  setOnSend?: (onSend: OnSend) => void
  // 更新关闭语音播放
  setOnCloseVoice?: (onCloseVoice: () => void) => void
  // 更新语音播放
  setOnPlayVoice?: (onPlayVoice: (content: string, id?: string) => void) => void
  // 更新反馈动作
  setOnFeedback?: (onFeedback: (messageId: string, feedback: Feedback) => void) => void
  // 更新语音通话
  setOnVoiceCall?: (onVoiceCall: () => void) => void
}
export type ChatStaticStoreState = ChatStaticState & ChatStaticAction
export type ChatStaticStore = ReturnType<typeof CreateChatStaticStore>

export const CreateChatStaticStore = (initProps?: Partial<ChatStaticStoreState>) => {
  const DEFAULT_PROPS: ChatStaticState = {
    config: undefined,
    appData: undefined,
    noChatInput: false,
    isPublicAPI: false,
    questionIcon: undefined,
    answerIcon: undefined,
    hideProcessDetail: false,
    hideLogModal: false,
    showInputBottomTip: false,
  }
  return createStore<ChatStaticStoreState>()(set => ({
    ...DEFAULT_PROPS,
    ...initProps,
    setConfig: (config: ChatConfig) => {
      set({ config })
    },
    setAppData: (appData: AppData) => {
      set({ appData })
    },
    setAnswerIcon: (answerIcon: string) => {
      set({ answerIcon })
    },
    setIsVoiceCall: (isVoiceCall: boolean) => {
      set({ isVoiceCall })
    },
    setIsConnecting: (isConnecting: boolean) => {
      set({ isConnecting })
    },
    setHasTryToAsk: (hasTryToAsk: boolean) => {
      set({ hasTryToAsk })
    },
    setOnSend: (onSend: OnSend) => {
      set({ onSend })
    },
    setOnCloseVoice: (onCloseVoice: () => void) => {
      set({ onCloseVoice })
    },
    setOnPlayVoice: (onPlayVoice: (content: string, id?: string) => void) => {
      set({ onPlayVoice })
    },
    setOnFeedback: (onFeedback: (messageId: string, feedback: Feedback) => void) => {
      set({ onFeedback })
    },
    setOnVoiceCall: (onVoiceCall: () => void) => {
      set({ onVoiceCall })
    },
  }))
}
