import React, { memo } from 'react'
import Answer from './answer'
import Question from './question'
import type { ChatItem } from '@/types/chat'

const ChatList = ({ chatList }: {
  chatList: ChatItem[]
}) => {
  console.log(222222)
  return (
    chatList.map((item, index) => {
      return (
        <>
          { item.prefix ? item.prefix : <></> }
          {
            item.isAnswer
              ? (
                <Answer
                  key={item.id}
                  item={item}
                  question={chatList[index - 1]?.content}
                  index={index}
                  responding={false}
                />)
              : <Question
                key={item.id}
                item={item}
              />
          }
        </>
      )
    })

  )
}

export default memo(ChatList)
