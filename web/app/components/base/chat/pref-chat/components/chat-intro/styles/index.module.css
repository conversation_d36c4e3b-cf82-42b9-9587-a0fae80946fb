.chatIntroWrap {
  @apply flex flex-col items-center justify-start py-[110px];
  width: 100%;
}
.chatInroAppInfo {
  @apply flex flex-col items-center justify-center gap-3 mb-3 mb-4 w-full;
}
.chatIntroAppTitle {
  @apply text-S6 leading-H6 font-semibold;
  text-align: center;
}
.openingStatement {
  @apply text-gray-G1 break-all;
  width: 100%;
  text-align: left;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
  margin-bottom: 24px;
  background: rgba(255, 255, 255, 0.90);
  padding: 12px 16px;
  border-radius: 4px;
  word-break: break-all;
}
.suggestedQuestionWrap {
  @apply flex flex-col gap-3 w-full;
}
.suggestedQuestion {
  @apply flex justify-center items-center px-4 py-[7px] rounded border text-gray-G6 w-fit max-w-full;
  min-height: 36px;
  font-size: 13px;
  line-height: 22px; /* 169.231% */
  cursor: pointer;
  word-break: break-all;
  border: 1px solid #D9DCE3;

  /* background: rgba(24, 24, 24, 0.25); */
  color:#5C6273
}
.suggestedQuestion:hover {
  color: #181818;
  border: 1px solid #878EA0;
  /* background: rgba(24, 24, 24, 0.30); */
}

.hasBgSuggestedQuestion {
  @apply flex justify-center items-center px-4 py-[7px] rounded border border-gray-G5 w-fit max-w-full text-gray-G2 hover:border-gray-G3 hover:text-gray-G1;
  min-height: 36px;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 169.231% */
  cursor: pointer;
  word-break: break-all;
  /* overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; */
  color: #EDEFF2;
  fill: rgba(24, 24, 24, 0.25);
  stroke-width: 1px;
  stroke: rgba(255, 255, 255, 0.30);
  backdrop-filter: blur(8px);
}