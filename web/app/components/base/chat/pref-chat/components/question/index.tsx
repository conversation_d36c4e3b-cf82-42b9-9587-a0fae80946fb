import type {
  FC,
} from 'react'
import React, {
  memo,
  useEffect,
} from 'react'
import { useChatStatic } from '../../hooks'
import style from './styles/index.module.css'
import type { ChatItem } from '@/types/chat'
// 公共能力
import { User } from '@/app/components/base/icons/src/public/avatar'
import { Markdown } from '@/app/components/base/markdown'
import { FileList } from '@/app/components/base/file-uploader'

type QuestionProps = {
  item: ChatItem
}
const Question: FC<QuestionProps> = ({ item }) => {
  const {
    content,
    message_files,
  } = item
  const questionIcon = useChatStatic(store => store.questionIcon)
  const chatRef = React.useRef<HTMLDivElement>(null)
  // 面板宽度
  const [panelContentWidth, setPanelContentWidth] = React.useState(0)

  useEffect(() => {
    if (chatRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        const width = chatRef.current?.offsetWidth
        setPanelContentWidth(width ? width - 44 - 32 : 0)
      })

      resizeObserver.observe(chatRef.current)

      return () => {
        resizeObserver.disconnect()
      }
    }
  }, [])
  return (
    <div ref={chatRef} className='p-2 flex gap-x w-full'>
      <div className={style.chatIcon}>
        {questionIcon || (
          <div className="w-full h-full rounded-full">
            <User className="w-full h-full" />
          </div>
        )}
      </div>
      <div className='relative group'>
        <div
          className={style.questionPanel}
        >
          {
            !!message_files?.length && (
              <FileList
                files={message_files}
                showDeleteAction={false}
                showDownloadAction={true}
                panelContentWidth={panelContentWidth}
              />
            )
          }
          <Markdown content={content} />
        </div>
      </div>
    </div>
  )
}

export default memo(Question)
