import { memo } from 'react'
import type { FC } from 'react'
import classNames from '@/utils/classnames'
import type { AppIconType } from '@/types/app'

export type AnswerIconProps = {
  iconType?: AppIconType | null
  icon?: string | null
  background?: string | null
  imageUrl?: string | null
}

const AnswerIcon: FC<AnswerIconProps> = ({
  iconType,
  icon,
  background,
  imageUrl,
}) => {
  const wrapperClassName = classNames(
    'flex',
    'items-center',
    'justify-center',
    'w-full',
    'h-full',
    'rounded-full',
    'border-[0.5px]',
    'border-black/5',
    'text-xl',
  )
  const isValidImageIcon = iconType === 'image' && imageUrl
  return <div
    className={wrapperClassName}
    style={{ background: background || '#D5F5F6' }}
  >
    {isValidImageIcon
      ? <img src={imageUrl} className="w-full h-full rounded-full" alt="answer icon" />
      : <img src='/assets/icons/default.svg' className="w-full h-full" alt="app icon"></img>
    }
  </div>
}

export default memo(AnswerIcon)
