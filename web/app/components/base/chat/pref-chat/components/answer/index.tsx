import type { FC } from 'react'
import { memo, useMemo } from 'react'
import dynamic from 'next/dynamic'
import { useTranslation } from 'react-i18next'
import LoadingAnim from '../loading-anim'
import Citation from '../citation'
import styles from '../styles/style.module.css'
import { useChatStatic } from '../../hooks'
import Operation from './operation'
import AgentContent from './agent-content'
import AnswerIcon from './answer-icon'
import More from './more'
import WorkflowProcess from './workflow-process'
import style from './styles/index.module.css'
import type { ChatItem } from '@/types/chat'
// 公共组件
import { FileListInAnswer } from '@/app/components/base/file-uploader'
import Avatar from '@/app/components/base/avatar'
import cn from '@/utils/classnames'

const BasicContent = dynamic(() => import('./basic-content'), { ssr: false })

type AnswerProps = {
  item: ChatItem
  question: string
  index: number
  responding?: boolean
}
const Answer: FC<AnswerProps> = ({
  item,
  question,
  index,
  responding,
}) => {
  console.log('aaaaaaa')
  const answerIcon = useChatStatic(store => store.answerIcon)
  const config = useChatStatic(store => store.config)
  const hideProcessDetail = useChatStatic(store => store.hideProcessDetail)
  const appData = useChatStatic(store => store.appData)
  const chatAnswerContainerInner = useChatStatic(store => store.chatAnswerContainerInner)

  const { t } = useTranslation()
  const {
    content,
    citation,
    agent_thoughts,
    more,
    annotation,
    workflowProcess,
    allFiles,
    message_files,
  } = item

  // 应用头像
  const AppAvatarIcon = useMemo(() => {
    if (!answerIcon)
      return null
    return <Avatar avatar={answerIcon} size={32} className="!rounded-full"></Avatar>
  }, [answerIcon])
  // 对话中止后，agent_thoughts内的回复可能是空的，这时候日志还是显示外层的content回复内容
  const hasAgentThoughts = () => {
    if (!agent_thoughts || agent_thoughts.length === 0)
      return false
    return (
      agent_thoughts.filter((thought) => {
        return thought.thought
      }).length > 0
    )
  }

  return (
    <div className="p-2 flex gap-x mt25">
      <div className={styles.chatIcon}>
        {/* 机器人回复头像 */}
        {AppAvatarIcon || <AnswerIcon />}
      </div>
      <div className="w-0 chat-answer-container group grow">
        <div className={cn('group relative', chatAnswerContainerInner)}>
          <div className={cn(style.answerPanel, `${workflowProcess && 'w-full'}`)}>
            {/** Render the normal steps */}
            {workflowProcess && !hideProcessDetail && (
              <WorkflowProcess
                data={workflowProcess}
                item={item}
                hideProcessDetail={hideProcessDetail}
              />
            )}
            {/** Hide workflow steps by it's settings in siteInfo */}
            {workflowProcess
              && hideProcessDetail
              && appData
              && appData.site.show_workflow_steps && (
              <WorkflowProcess
                data={workflowProcess}
                item={item}
                hideProcessDetail={hideProcessDetail}
              />
            )}
            {/* 加载动画 */}
            {responding && !content && !hasAgentThoughts() && (
              <div className="flex items-center justify-center w-6 h-5">
                <LoadingAnim type="text" />
              </div>
            )}
            {/* 返回信息流不带有agentThoughts数据的话 */}
            {content && !hasAgentThoughts() && <BasicContent item={item} />}
            {/* 返回信息流带有agentThoughts数据的话 */}
            {hasAgentThoughts() && <AgentContent item={item} responding={responding} />}
            {!!allFiles?.length && (
              <FileListInAnswer
                className="my-1"
                files={allFiles}
                showDeleteAction={false}
                showDownloadAction
                canPreview
              />
            )}
            {!!message_files?.length && (
              <FileListInAnswer
                className="my-1"
                files={message_files}
                showDeleteAction={false}
                showDownloadAction
                canPreview
              />
            )}
            {!!citation?.length && !responding && (
              <Citation data={citation} showHitInfo={config?.supportCitationHitInfo} />
            )}
            {/* 操作按钮 */}
            {!responding && (
              <Operation
                item={item}
                question={question}
                index={index}
              />
            )}
          </div>
        </div>
        <More more={more} />
      </div>
    </div>
  )
}

export default memo(Answer)
