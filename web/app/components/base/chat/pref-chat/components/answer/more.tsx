import type { FC } from 'react'
import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import style from './styles/answer.module.css'
import type { ChatItem } from '@/types/chat'
import { formatNumber } from '@/utils/format'
import cn from '@/utils/classnames'

type MoreProps = {
  more: ChatItem['more']
}
const More: FC<MoreProps> = ({
  more,
}) => {
  const { t } = useTranslation()

  return (
    more && <div className={cn(style.answerMore, 'group-hover:opacity-100')}>
      <>
        <div
          className={style.answerMoreItem}
          title={`${t('appLog.detail.timeConsuming')}${more.latency}${t('appLog.detail.second')}`}
        >
          {`${t('appLog.detail.timeConsuming')}${more.latency}${t('appLog.detail.second')}`}
        </div>
        <div
          className={style.answerMoreItem}
          title={`${t('appLog.detail.tokenCost')} ${formatNumber(more.tokens)}`}
        >
          {`${t('appLog.detail.tokenCost')} ${formatNumber(more.tokens)}`}
        </div>
        <div
          className={style.answerMoreItem}
          title={more.time}
        >
          {more.time}
        </div>
      </>
    </div>
  )
}

export default memo(More)
