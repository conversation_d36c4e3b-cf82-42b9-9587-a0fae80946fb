import type { FC } from 'react'
import { memo } from 'react'
import type { ChatItem } from '@/types/chat'
// 公共能力
import cn from '@/utils/classnames'
import { Markdown } from '@/app/components/base/markdown'

type BasicContentProps = {
  item: ChatItem
}

const BasicContent: FC<BasicContentProps> = ({ item }) => {
  const { content } = item

  return (
    <>
      <Markdown className={cn('', item.isError && '!text-[#F04438]')} content={content} />
    </>
  )
}

export default memo(BasicContent)
