import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  RiLoader2Line,
} from '@remixicon/react'
import style from './styles/detail.module.css'
import { toolCallStatus } from '@/types/chat'
import type { ToolInfoInThought } from '@/types/chat'
// 公共能力
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import { ChatDataset, ChatTool, Success, Warn } from '@/app/components/base/icons/src/public/common'
import cn from '@/utils/classnames'

type ToolDetailProps = {
  payload: ToolInfoInThought
}
const ToolDetail = ({
  payload,
}: ToolDetailProps) => {
  const { t } = useTranslation()
  const { name, label, input, isFinished, output } = payload
  // 是否展开
  const [expand, setExpand] = useState(false)
  // 工具状态
  const [toolStatus, setToolStatus] = useState<toolCallStatus>(toolCallStatus.using)

  // 知识库的tool会有个dataset_前缀，用来区分工具和知识库，展示名称时需要过滤前缀
  const isDataset = useMemo(() => {
    return name.startsWith('dataset_')
  }, [name])
  // 工具调用状态图标
  const toolCallStatusIcon = useMemo(() => {
    if (toolStatus === toolCallStatus.used)
      return <Success className='ml-auto'></Success>
    else
      return <Warn className='ml-auto'></Warn>
  }, [toolStatus])

  // 工具名称
  const toolLabel = isDataset ? label.replace('dataset_', '') : label
  // 工具图标
  const toolIcon = isDataset ? <ChatDataset className={style.toolCallIcon}/> : <ChatTool className={style.toolCallIcon}/>

  useEffect(() => {
    if (isFinished)
      setToolStatus(toolCallStatus.used)
    else
      setToolStatus(toolCallStatus.using)
  }, [isFinished])

  return (
    <div
      className={cn(
        'rounded bg-primary-P4 space-y-2 pt-[6px] px-3 pb-[6px]',
        expand && 'pb-3',
      )}
    >
      <div
        className={cn(
          style.toolDetailContent,
        )}
        onClick={() => setExpand(!expand)}
      >
        {/* 工具图标 加载图标 */}
        {isFinished && toolIcon}
        {!isFinished && <RiLoader2Line className={cn(style.toolCallIcon, 'animate-spin shrink-0')} />}
        {/* 调用状态文字：正在调用、完成调用等... */}
        <div className={cn(style.commonFont, style.toolCallStatus)}>{t(`tools.thought.${isDataset ? 'dataset' : 'tool'}.${toolStatus}`)}</div>
        {/* 工具名称 */}
        <div className={cn(style.commonFont, style.toolCallLabel, 'truncate ...')}>{toolLabel}</div>
        {isFinished && (
          <>
            {/* 工具调用状态图标 */}
            {toolCallStatusIcon}
            {/* 展开图标 */}
            <ArrowDown className={cn('w-4 h-4 ml-2', expand && 'rotate-180')}/>
          </>
        )}
      </div>
      {/* 展开态 */}
      {
        expand && (
          <>
            <div className={style.detailItem}>
              <div className='title-12-20'>
                {t('tools.thought.requestTitle')}
              </div>
              <div className={cn(style.detailBody, 'code-xs-regular')}>
                {input}
              </div>
            </div>
            <div className={style.detailItem}>
              <div className='title-12-20'>
                {t('tools.thought.responseTitle')}
              </div>
              <div className={cn(style.detailBody, style.commonFont)}>
                {output}
              </div>
            </div>
          </>
        )
      }
    </div>
  )
}

export default ToolDetail
