'use client'
import type { FC } from 'react'
import React from 'react'
import ToolDetail from './tool-detail'
import type { ThoughtItem, ToolInfoInThought } from '@/types/chat'
import { getLanguage } from '@/i18n/language'
import { useI18N } from '@/context/i18n'

export type IThoughtProps = {
  thought: ThoughtItem
  isFinished: boolean
}

function getValue(value: string, isValueArray: boolean, index: number) {
  if (isValueArray) {
    try {
      return JSON.parse(value)[index]
    }
    catch (e) {
    }
  }
  return value
}

const Thought: FC<IThoughtProps> = ({
  thought,
  isFinished,
}) => {
  const { locale } = useI18N()
  // 流式聊天里面工具展示名称toolName实际取的是thought.tool
  const [toolNames, isValueArray]: [string[], boolean] = (() => {
    try {
      if (Array.isArray(JSON.parse(thought.tool)))
        return [JSON.parse(thought.tool), true]
    }
    catch (e) {
    }
    return [[thought.tool], false]
  })()
  // 这里没看到有工具存在thought.tool_labels?.toolName?.language，最后label都是取toolName
  const toolThoughtList = toolNames.map((toolName, index) => {
    return {
      name: toolName,
      label: thought.tool_labels?.[toolName][getLanguage(locale)] || toolName,
      input: getValue(thought.tool_input, isValueArray, index),
      output: getValue(thought.observation, isValueArray, index),
      isFinished,
    }
  })

  return (
    <div className='my-2 space-y-2'>
      {toolThoughtList.map((item: ToolInfoInThought, index) => (
        <ToolDetail
          key={index}
          payload={item}
        />
      ))}
    </div>
  )
}
export default React.memo(Thought)
