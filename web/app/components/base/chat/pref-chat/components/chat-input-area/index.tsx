import { memo, useEffect, useMemo, useRef, useState } from 'react'
import Textarea from 'rc-textarea'
import { useTranslation } from 'react-i18next'
import { useChatStatic } from '../../hooks'
import NewChat from './components/new-chat'
import { useCheckInputsForms, useTextAreaHeight } from './hooks'
import Operation from './operation'
import style from './styles/style.module.css'
import VoiceInput from './components/voice-input'
import VoiceConversation from './components/voice-conversation'
import type { InputForm, OnSend } from '@/types/chat'
import { TransferMethod } from '@/types/model'

// 公共能力
import { XCircle } from '@/app/components/base/icons/src/vender/solid/action'
import { useVoiceAsr } from '@/app/components/base/voice-service/voice-asr/hooks'
import cn from '@/utils/classnames'
import { FileListInChatInput, FileUploaderInChatInput } from '@/app/components/base/file-uploader'
import { useFile } from '@/app/components/base/file-uploader/hooks'
import { FileContextProvider, useFileStore } from '@/app/components/base/file-uploader/store'
import { useToastContext } from '@/app/components/base/toast'
import type { FileUpload, VoiceConversationStatement, VoiceInputStatement } from '@/app/components/base/features/types'
import { useSystemContext } from '@/context/system-context'
type ChatInputAreaProps = {
  appId?: string
  // 展示特性栏
  showFeatureBar?: boolean
  // 展示文件上传
  showFileUpload?: boolean
  // 特性栏disabled
  featureBarDisabled?: boolean
  // 特性栏点击
  onFeatureBarClick?: (state: boolean) => void
  // 文件上传配置
  visionConfig?: FileUpload
  inputs?: Record<string, any>
  inputsForm?: InputForm[]
  isPublicAPI?: boolean
  voiceInput?: VoiceInputStatement
  voiceConversation?: VoiceConversationStatement
  // 移动端显示底部上传卡片
  handleClickShowFiles?: () => void
  // 显示输入框底部提示
  showInputBottomTip?: boolean
  // 发送函数
  onSend?: OnSend
  isVoiceCall?: boolean
  onVoiceCall?: () => void
  onCancelVoiceCall?: () => void
  // 是否正在响应
  isResponding?: boolean
  // 停止响应
  onStopResponding?: () => void
  // 开启新对话
  handleNewConversation?: () => void
  isThinking?: boolean
  isConnecting?: boolean
  // 停止语音播放
  handleStopVoiceTts?: () => void
}

const ChatInputArea = memo(({
  appId,
  visionConfig,
  voiceInput,
  voiceConversation,
  inputs = {},
  inputsForm = [],
  onStopResponding,
  onCancelVoiceCall = () => {},
  handleNewConversation,
  isResponding,
  isThinking = false,
  onSend: propOnSend,
  onVoiceCall: handleVoiceCall,
  handleStopVoiceTts: propCloseVoiceTts = () => {},
  showInputBottomTip: propShowInputBottomTip,
  isVoiceCall: propIsVoiceCall = false,
  isPublicAPI: propsIsPublicAPI = false,
  isConnecting: propIsConnecting = false,
}: ChatInputAreaProps) => {
  const { t } = useTranslation()
  const filesStore = useFileStore()
  const { checkInputsForm } = useCheckInputsForms()
  const { notify } = useToastContext()
  const { isMobile } = useSystemContext()
  // 语音通话动作
  const onVoiceCall = useChatStatic(s => s.onVoiceCall) || handleVoiceCall
  // 发送函数
  const onSend = useChatStatic(s => s.onSend) || propOnSend
  // 是否语音通话正在连接
  const isConnecting = useChatStatic(s => s.isConnecting) || propIsConnecting
  // 是否是公共api
  const isPublicAPI = useChatStatic(s => s.isPublicAPI) || propsIsPublicAPI
  // 是否正在语音通话
  const isVoiceCall = useChatStatic(s => s.isVoiceCall) || propIsVoiceCall
  // 显示底部提示
  const showInputBottomTip = useChatStatic(s => s.showInputBottomTip) || propShowInputBottomTip
  // 关于语音播放
  const handleStopVoiceTts = useChatStatic(s => s.onCloseVoice) || propCloseVoiceTts

  const {
    wrapperRef,
    textareaRef,
    holdSpaceRef,
    handleTextareaResize,
    isMultipleLine,
  } = useTextAreaHeight()

  // 语音输入
  const {
    totalText,
    isInVoiceAsr,
    handleStartVoiceAsr,
    handleStopVoiceAsr,
  } = useVoiceAsr(appId || '', isPublicAPI)

  const {
    handleDragFileEnter,
    handleDragFileLeave,
    handleDragFileOver,
    handleDropFile,
    handleClipboardPasteFile,
    isDragActive,
  } = useFile(visionConfig!)

  const totalTextRef = useRef(totalText)
  const isInVoiceAsrRef = useRef(isInVoiceAsr)
  // 提问信息
  const [query, setQuery] = useState('')
  // 是否正在组合输入
  const isUseInputMethod = useRef(false)
  // 移动端文件卡片是否显示
  const [showFiles, setShowFiles] = useState(false)
  // 更新组件的status
  const getVoiceStatus = useMemo(() => {
    if (isThinking)
      return 'thinking'
    if (isConnecting)
      return 'connecting'

    return ''
  }, [isThinking, isConnecting])

  // 语音输入转换为文字
  const convertVoiceInput = () => {
    setQuery(totalTextRef.current)
    handleTextareaResize()
    handleStopVoiceAsr()
  }
  // 取消语音输入
  const cancelVoiceInput = () => {
    setQuery('')
    handleTextareaResize()
    handleStopVoiceAsr()
  }
  // 处理网络异常
  const handleNetworkError = () => {
    if (isInVoiceAsrRef.current) {
      convertVoiceInput()
      notify({
        type: 'error',
        message: t('common.voiceInput.error.asrNoInternet'),
      })
    }
  }
  // 发送提问
  const handleSend = () => {
    if (onSend) {
      const { files, setFiles } = filesStore.getState()
      if (
        files.find(item => item.transferMethod === TransferMethod.local_file && !item.uploadedId)
      ) {
        notify({ type: 'info', message: t('appDebug.errorMessage.waitForFileUpload') })
        return
      }
      if ((!query || !query.trim()) && !files.length) {
        notify({ type: 'info', message: t('appDebug.errorMessage.queryIsRequired') })
        return
      }
      if (checkInputsForm(inputs, inputsForm)) {
        onSend(query, files)
        setQuery('')
        setFiles([])
      }
    }
  }
  // enter按键按下处理
  const handleKeyUp = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.code === 'Enter') {
      e.preventDefault()
      // prevent send message when using input method enter
      if (!e.shiftKey && !isUseInputMethod.current)
        handleSend()
    }
  }
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 是否正在组合输入
    isUseInputMethod.current = e.nativeEvent.isComposing
    if (e.code === 'Enter' && !e.shiftKey) {
      setQuery(query.replace(/\n$/, ''))
      e.preventDefault()
    }
  }
  // 点击显示文件上传卡片
  const handleClickShowFiles = () => {
    setShowFiles(!showFiles)
  }
  // 语音输入过程中网络中断
  useEffect(() => {
    window.addEventListener('offline', handleNetworkError)
    return () => {
      window.removeEventListener('offline', handleNetworkError)
    }
  }, [])
  useEffect(() => {
    totalTextRef.current = totalText
    isInVoiceAsrRef.current = isInVoiceAsr
  }, [isInVoiceAsr, totalText])

  // 操作栏
  const operation = (
    <Operation
      ref={holdSpaceRef}
      query={query}
      onSend={handleSend}
      fileConfig={visionConfig}
      onStartVoiceInput={() => {
        handleStopVoiceTts()
        handleStartVoiceAsr()
      }}
      voiceInput={voiceInput}
      voiceConversation={voiceConversation}
      handleClickShowFiles={handleClickShowFiles}
      isResponding={isResponding}
      onStopResponding={onStopResponding}
      onVoiceCall={onVoiceCall}
    />
  )
  return (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="flex justify-end items-center gap-3 w-full">
        {/* 新增对话 */}
        {!isVoiceCall && handleNewConversation && <NewChat onClick={handleNewConversation} />}
        {/* 输入框 */}
        <div
          className={cn(
            (!isVoiceCall && !isThinking && !isConnecting) ? style.chatInput : style.chatVoiceInput,
            'rounded relative',
            !handleNewConversation && '!w-full',
            isDragActive
                && 'border border-dashed border-components-option-card-option-selected-border',
          )}
        >
          <div className='flex flex-col items-start max-h-[150px] bg-white rounded overflow-y-auto'>
            {/* 上传文件列表 */}
            <FileListInChatInput fileConfig={visionConfig!} />
            {/* 普通文本框输入 */}
            {!isInVoiceAsr && !isVoiceCall && !isThinking && !isConnecting
              && <div
                ref={wrapperRef}
                className='flex items-center justify-between w-full'
              >
                <div className='flex items-center relative grow w-full'>
                  <Textarea
                    ref={textareaRef}
                    className={cn(
                      'block w-full h-[48px] px-4 pr-[118px] py-[12px] text-[14px] leading-H3 max-h-none text-gray-G1 outline-none appearance-none resize-none',
                    )}
                    placeholder={t('common.chat.inputPlaceholder') || ''}
                    autoSize={{ minRows: 1 }}
                    onResize={handleTextareaResize}
                    value={query}
                    onChange={(e) => {
                      setQuery(e.target.value)
                      handleTextareaResize()
                    }}
                    onKeyUp={handleKeyUp}
                    onKeyDown={handleKeyDown}
                    onPaste={handleClipboardPasteFile}
                    onDragEnter={handleDragFileEnter}
                    onDragLeave={handleDragFileLeave}
                    onDragOver={handleDragFileOver}
                    onDrop={handleDropFile}
                  />
                </div>
                <div className={cn('absolute top-0 bottom-0 h-full flex items-center', 'right-3')}>
                  {/* 删除按钮 */}
                  {
                    query
                      ? (
                        <div className='flex justify-center items-center ml-2 w-8 h-8 cursor-pointer' onClick={() => setQuery('')}>
                          <XCircle className='w-4 h-4 text-gray-G5 hover:text-gray-G4' />
                        </div>
                      )
                      : null
                  }
                  {/* 发送/中止按钮 */}
                  {
                    !isMultipleLine && operation
                  }
                  {
                    isMultipleLine && (
                      <div className='px-[9px]'>{operation}</div>
                    )
                  }
                </div>
              </div>
            }
            {/* 正在语音输入 */}
            {isInVoiceAsr
              && <VoiceInput
                text={totalText}
                onConverted={() => {
                  convertVoiceInput()
                }}
                onCancel={cancelVoiceInput}
              />
            }
            {/* 正在语音通话 */}
            {(isVoiceCall || isThinking || isConnecting) && (
              <VoiceConversation
                voiceStatus={getVoiceStatus}
                onStop={onCancelVoiceCall!}
              />
            )}
          </div>
        </div>
      </div>
      {visionConfig?.enabled && isMobile && showFiles && (
        <>
          <FileUploaderInChatInput fileConfig={visionConfig!} />
        </>
      )}
      {/* 底部提示 */}
      {showInputBottomTip && (
        <div className="text-[12px] leading-H1 text-gray-G4 text-center mt-2 pb-3">
          {t('common.chat.tip')}
        </div>
      )}
    </div>)
})
ChatInputArea.displayName = 'ChatInputArea'

const ChatInputAreaWrapper = (props: ChatInputAreaProps) => {
  return (
    <FileContextProvider>
      <ChatInputArea {...props} />
    </FileContextProvider>
  )
}

export default ChatInputAreaWrapper
