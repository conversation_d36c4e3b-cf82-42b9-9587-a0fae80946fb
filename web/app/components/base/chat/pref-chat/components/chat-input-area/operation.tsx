import {
  forwardRef,
  memo,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import Image from 'next/image'
import { Divider } from 'antd'
import style from './styles/style.module.css'
import type { OnVoiceCall } from '@/types/chat'

// 公共组件
import TextButton from '@/app/components/base/button/text-button'
import { Call, Voice } from '@/app/components/base/icons/src/vender/line/chat'
import Tooltip from '@/app/components/base/tooltip'
import { FileUploaderInChatInput } from '@/app/components/base/file-uploader'
import type { FileUpload, VoiceConversationStatement, VoiceInputStatement } from '@/app/components/base/features/types'
import cn from '@/utils/classnames'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'
import { SendMessage } from '@/app/components/base/icons/src/public/chat'

type OperationProps = {
  fileConfig?: FileUpload
  query: string
  // speechToTextConfig?: EnableType
  onStartVoiceInput?: () => void
  onSend?: () => void
  handleClickShowFiles?: () => void
  voiceInput?: VoiceInputStatement
  voiceConversation?: VoiceConversationStatement
  isResponding?: boolean
  onStopResponding?: () => void
  onVoiceCall?: OnVoiceCall
}
const Operation = forwardRef<HTMLDivElement, OperationProps>(({
  fileConfig,
  query,
  // speechToTextConfig,
  onStartVoiceInput,
  onSend,
  handleClickShowFiles,
  voiceInput,
  voiceConversation,
  isResponding,
  onStopResponding,
  onVoiceCall,
}, ref) => {
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  const { t } = useTranslation()
  // 停止按钮是否正在hover
  const [isHovered, setIsHovered] = useState(false)

  // 鼠标移入停止按钮
  const handleMouseEnter = () => {
    setIsHovered(true)
  }
  // 鼠标移出停止按钮
  const handleMouseLeave = () => {
    setIsHovered(false)
  }

  // 发送按钮
  const sendBtn = (
    <div
      className='group flex items-center justify-center w-8 h-full cursor-pointer'
      onClick={onSend}
    >
      <SendMessage></SendMessage>
    </div>
  )
  // 停止响应按钮
  const stopRespondingBtn = (
    <div
      className='group flex items-center justify-center w-8 h-full cursor-pointer'
      onClick={onStopResponding}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <Tooltip popupContent={t('common.chat.stopChat')}>
        <Image src={isHovered ? '/assets/icons/app/stopRespondingActive.svg' : '/assets/icons/app/stopResponding.svg'} width={32} height={32} alt='stopRespondingBtn' />
      </Tooltip>
    </div>
  )
  return (
    <div
      className={cn(
        'shrink-0 flex items-center justify-end pl-1 gap-1',
      )}
      ref={ref}
    >
      <div className='flex items-center space-x-1'>
        {/* 语音输入 */}
        {voiceInput?.enabled && (
          <Tooltip popupContent={t('common.voiceInput.voiceInput')}>
            <TextButton onClick={onStartVoiceInput}>
              <Voice className={style.actionButton} />
            </TextButton>
          </Tooltip>
        )}
        {/* 语音对话 */}
        {voiceConversation?.enabled && (
          <Tooltip popupContent={t('common.voiceInput.voiceConversation')}>
            <TextButton onClick={onVoiceCall}>
              <Call className={style.actionButton} />
            </TextButton>
          </Tooltip>
        )}
        {/* 文件上传 */}
        {!isMobile && fileConfig?.enabled && (
          <>
            <FileUploaderInChatInput fileConfig={fileConfig} />
            <Divider className='w-px h-[18px] m-0 first:hidden' type={'vertical'}/>
          </>
        )}
        {isMobile && fileConfig?.enabled && (
          <>
            <img onClick={handleClickShowFiles} className='w-[28px] h-[28px]' src="/assets/icons/chat/add.png" alt="" />
            <Divider className='w-px h-[18px] m-0' type={'vertical'}/>
          </>
        )}
        {/* 语音转文本 */}
        {/* {
            speechToTextConfig?.enabled && (
              <ActionButton
                size='l'
                onClick={onShowVoiceInput}
              >
                <RiMicLine className='w-5 h-5' />
              </ActionButton>
            )
          } */}
      </div>
      {/* 发送按钮 */}
      {!isResponding && sendBtn}
      {/* 停止响应 */}
      {isResponding && stopRespondingBtn}
    </div>
  )
})
Operation.displayName = 'Operation'

export default memo(Operation)
