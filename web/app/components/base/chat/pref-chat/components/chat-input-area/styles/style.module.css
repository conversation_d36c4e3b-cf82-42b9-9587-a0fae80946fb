.chatInput {
  width: calc(100% - 44px);
  box-shadow: 0px 4px 10px rgba(222, 226, 234, 0.40);
  background: var(--color-gray-G4);
  padding: 1px;
}
.chatInput:hover, .chatInput.active {
  box-shadow: 0px 4px 16px rgba(222, 226, 234, 0.80);
  background: linear-gradient(90deg, #499DFB -29.55%, #1F3DF6 75.22%);
  padding: 1px;
}

.chatVoiceInput {
  width: calc(100% - 44px);
  box-shadow: 0px 4px 10px rgba(222, 226, 234, 0.40);
  background: var(--color-gray-G4);
  padding: 1px;
}
.chatVoiceInput:hover, .chatVoiceInput.active {
  padding: 1px;
}

.actionButton {
  @apply w-8 h-8 text-gray-G1 hover:bg-gray-G7 rounded;
}