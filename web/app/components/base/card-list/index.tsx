import { type ReactNode, forwardRef, useImperativeHandle, useMemo, useRef } from 'react'
import { debounce } from 'lodash-es'
import type { ScrollbarRef } from '../scrollbar'
import Scrollbar from '../scrollbar'
import style from './index.module.css'
import cn from '@/utils/classnames'

export type CardListProps = {
  children: ReactNode[]
  className?: string
  loading?: boolean
  loadFunc?: () => void
  type?: 'normal' | 'scroll'
  layout?: 'col' | 'line' | 'grid'
  wrapperClassName?: string
  emptyText?: string | ReactNode | null
  emptyWrapClassName?: string
}

const CardList = forwardRef(({ children, className, wrapperClassName, emptyText, emptyWrapClassName, loading, type = 'normal', loadFunc, layout = 'col' }: CardListProps, ref) => {
  const cardListRef = useRef<ScrollbarRef>(null)

  const layoutClass = useMemo(() => {
    return children.length ? (layout === 'col' ? style['col-card-list'] : layout === 'grid' ? style['grid-card-list'] : style['line-card-list']) : 'w-full'
  }, [layout, children])

  useImperativeHandle(ref, () => ({
    scrollTop() {
      cardListRef.current?.scrollTop()
    },
  }))

  const onScrollY = debounce(async () => {
    if (!loading && type === 'scroll') {
      const container = cardListRef.current?.getContainer()
      if (container) {
        const { scrollTop, clientHeight, scrollHeight } = container
        if (scrollHeight - 100 <= scrollTop + clientHeight)
          await loadFunc!()
      }
    }
  }, 200)

  return (
    <Scrollbar
      loading={loading}
      ref={cardListRef}
      onScrollY={onScrollY}
      wrapperClassName={wrapperClassName}
      className={cn(layoutClass, className)}
      emptyText={emptyText}
      emptyWrapClassName={emptyWrapClassName}
    >
      {children}
    </Scrollbar>
  )
})

CardList.displayName = 'CardList'

export default CardList
