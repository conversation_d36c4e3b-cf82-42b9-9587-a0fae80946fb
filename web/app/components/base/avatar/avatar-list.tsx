import React from 'react'
import Avatar from '.'
import cn from '@/utils/classnames'

type AvatarListProps = {
  // 头像列表
  list: string[]
  // 数量
  number: string
  // 尺寸
  size: number
  // 容器class
  containerClass?: string
  // 文本class
  textClass?: string
}

const AvatarList = ({ list, number, size, containerClass, textClass }: AvatarListProps) => {
  const getAvatarStyle = (index: number) => {
    return {
      left: `${-6 * index}px`,
      zIndex: `${index + 100}`,
    }
  }

  return (
    <div className='flex'>
      {list.map((url, index) => {
        return <Avatar
          className={cn('relative', containerClass)}
          style={getAvatarStyle(index)}
          key={index}
          name={url}
          size={size}
          avatar={url}
        ></Avatar>
      })}
      <Avatar
        textClassName={textClass}
        className={cn('relative !border-none', containerClass)}
        style={getAvatarStyle(list.length)}
        size={size}
        showFullName={true}
        name={String(number)}
      ></Avatar>
    </div>
  )
}

export default React.memo(AvatarList)
