'use client'
import type { CSSProperties } from 'react'
import React, { useEffect, useState } from 'react'
import type { GetProp, UploadProps } from 'antd'
import { useTranslation } from 'react-i18next'
import { Upload } from 'antd'
import type { RcFile } from 'antd/es/upload'
import { useLocalFileUploader } from '../image-uploader/hooks'
import s from './styles/index.module.css'
import cn from '@/utils/classnames'
import type { ImageFile } from '@/types/public/file'

// 公共组件
import { Upload01 } from '@/app/components/base/icons/src/vender/line/action'
import Toast from '@/app/components/base/toast'
import Loading from '@/app/components/base/loading'

type AvatarProps = {
  name?: string
  avatar?: string
  size?: number
  className?: string
  imgClassName?: string
  style?: CSSProperties
  textClassName?: string
  showUpload?: Boolean
  showFullName?: Boolean
  onChange?: (value: string) => void
  version?: 'lastest' | 'old'
  uploadTrigger?: React.ReactNode
}
type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0]

const Avatar = ({
  name,
  avatar,
  size = 30,
  className,
  imgClassName,
  textClassName,
  showUpload = false,
  uploadTrigger,
  showFullName = false,
  onChange,
  style: containerStyle,
  version = 'lastest',

}: AvatarProps) => {
  const { t } = useTranslation()
  const sizeStyle = { width: `${size}px`, height: `${size}px` }
  const style = { fontSize: `${size}px`, lineHeight: `${size}px` }

  // 图标url
  const [avatarUrl, setAvatarUrl] = useState('')
  // 是否正在加载
  const [loading, setLoading] = useState(true)
  const { handleLocalFileUpload } = useLocalFileUploader({
    limit: 1,
    disabled: false,
    onUpload: (imageFile: ImageFile) => {
      if (imageFile.fileId) {
        setAvatarUrl(imageFile.base64Url!)
        onChange?.(version === 'lastest' ? imageFile.fileId : imageFile.base64Url!)
        setLoading(false)
      }
      // 上传失败
      if (imageFile.progress === -1)
        setLoading(false)
    },
    url: '/images/upload',
  })

  useEffect(() => {
    if (avatar) {
      const img = new Image()
      img.src = avatar
      img.onload = () => {
        setLoading(false)
        setAvatarUrl(avatar)
      }
    }
  }, [avatar])

  if (avatar) {
    return (
      <div className={cn('group', s['avatar-container'], className)} style={{ ...sizeStyle, ...containerStyle }}>
        { !loading
          ? <img
            className={cn(s['avatar-content'], imgClassName)}
            alt={name || ''}
            src={avatarUrl}
          />
          : <Loading type='area'></Loading>
        }
        {/* 上传功能 */}
        { showUpload && <Upload
          className={cn(s['avatar-upload'], 'hidden group-hover:flex')}
          showUploadList={false}
          beforeUpload={(file: FileType) => {
            const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
            if (!isJpgOrPng) {
              Toast.notify({
                type: 'error',
                message: t('common.component.avatar.typeTip'),
              })
            }

            const isLt1M = file.size / 1024 / 1024 < 1
            if (!isLt1M) {
              Toast.notify({
                type: 'error',
                message: t('common.component.avatar.sizeTip'),
              })
            }
            return isJpgOrPng && isLt1M
          }}
          customRequest={({ file }) => {
            if (file) {
              setLoading(true)
              handleLocalFileUpload(file as RcFile)
            }
          }}
        >
          <div className={(s['avatar-upload-trigger'])} style={{ width: `${size}px`, height: `${size}px` }}>
            { uploadTrigger || <Upload01 className='w-1/2 h-1/2 text-white !opacity-100'></Upload01>}
          </div>
        </Upload>
        }
      </div>
    )
  }

  return (
    <div
      className={cn(s['avatar-text-container'], className)}
      style={{ ...sizeStyle, ...containerStyle }}
    >
      <div
        className={cn(textClassName, 'text-center text-white scale-[0.4]')}
        style={{ ...style, ...sizeStyle }}
      >
        {name && (showFullName ? name : name[0].toLocaleUpperCase())}
      </div>
    </div>
  )
}

export default React.memo(Avatar)
