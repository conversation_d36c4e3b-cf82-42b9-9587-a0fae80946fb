'use client'
import type { FC } from 'react'
import Editor, { loader } from '@monaco-editor/react'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import Base from '../base'
import cn from '@/utils/classnames'
import { CodeLanguage } from '@/app/components/workflow/nodes/code/types'

import './style.css'

loader.config({ paths: { vs: '/vs' } })

// 行高
const CODE_EDITOR_LINE_HEIGHT = 18
// 支持的语言
const languageMap = {
  [CodeLanguage.javascript]: 'javascript',
  [CodeLanguage.python3]: 'python',
  [CodeLanguage.json]: 'json',
}
// 默认主题
const DEFAULT_THEME = {
  base: 'vs',
  inherit: true,
  rules: [],
  colors: {
    'editor.background': '#00000000', // #00000000 transparent. But it will has a blue border
    'editorLineNumber.foreground': '#5C6273',
    'editorLineNumber.activeForeground': '#3168F5',
  },
}

export type Props = {
  customEditorWrapClassName?: string

  value?: string | object
  placeholder?: JSX.Element | string
  title?: JSX.Element
  language: CodeLanguage
  headerRight?: JSX.Element
  readOnly?: boolean
  isJSONStringifyBeauty?: boolean
  height?: number
  isInNode?: boolean
  noWrapper?: boolean
  isExpand?: boolean
  showFileList?: boolean
  onMount?: (editor: any, monaco: any) => void
  onChange?: (value: string) => void
}

const CodeEditor: FC<Props> = ({
  value = '',
  placeholder = '',
  onChange = () => { },
  title = '',
  headerRight,
  language,
  readOnly,
  isJSONStringifyBeauty,
  height,
  isInNode,
  onMount,
  noWrapper,
  isExpand,
  customEditorWrapClassName,
}) => {
  // 是否聚焦
  const [isFocus, setIsFocus] = React.useState(false)
  // 是否加载完成
  const [isMounted, setIsMounted] = React.useState(false)
  // 编辑器内容高度
  const [editorContentHeight, setEditorContentHeight] = useState(80)
  const valueRef = useRef(value)
  const editorRef = useRef<any>(null)

  const minHeight = height || 200

  useEffect(() => {
    valueRef.current = value
  }, [value])

  // 主题
  const theme = useMemo(() => {
    if (noWrapper)
      return 'default-theme'

    return isFocus ? 'focus-theme' : 'blur-theme'
  }, [isFocus, noWrapper])
  // 输出值
  const outPutValue = useMemo(() => {
    if (!isJSONStringifyBeauty)
      return value as string
    try {
      return JSON.stringify(value as object, null, 2)
    }
    catch (e) {
      return value as string
    }
  }, [isJSONStringifyBeauty, value])

  // 调整编辑器高度
  const resizeEditorToContent = () => {
    if (editorRef.current) {
      const contentHeight = editorRef.current.getContentHeight() // Math.max(, minHeight)
      setEditorContentHeight(contentHeight)
    }
  }
  // 编辑器内容变化
  const handleEditorChange = (value: string | undefined) => {
    onChange(value || '')
    setTimeout(() => {
      resizeEditorToContent()
    }, 10)
  }
  // 编辑器初始完成
  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor
    resizeEditorToContent()

    editor.onDidFocusEditorText(() => {
      setIsFocus(true)
    })
    editor.onDidBlurEditorText(() => {
      setIsFocus(false)
    })

    monaco.editor.defineTheme('default-theme', DEFAULT_THEME)

    monaco.editor.defineTheme('blur-theme', {
      base: 'vs',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#00000000',
      },
    })

    monaco.editor.defineTheme('focus-theme', {
      base: 'vs',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#00000000',
      },
    })

    monaco.editor.setTheme('default-theme') // Fix: sometimes not load the default theme

    onMount?.(editor, monaco)
    setIsMounted(true)
  }

  const main = (
    <>
      {/* https://www.npmjs.com/package/@monaco-editor/react */}
      <Editor
        // className='min-h-[100%]' // h-full
        // language={language === CodeLanguage.javascript ? 'javascript' : 'python'}
        language={languageMap[language] || 'javascript'}
        theme={isMounted ? theme : 'default-theme'} // sometimes not load the default theme
        value={outPutValue}
        onChange={handleEditorChange}
        // https://microsoft.github.io/monaco-editor/typedoc/interfaces/editor.IEditorOptions.html
        options={{
          readOnly,
          domReadOnly: true,
          quickSuggestions: false,
          minimap: { enabled: false },
          lineNumbersMinChars: 1, // would change line num width
          wordWrap: 'on', // auto line wrap
          // lineNumbers: (num) => {
          //   return <div>{num}</div>
          // }
          // hide ambiguousCharacters warning
          unicodeHighlight: {
            ambiguousCharacters: false,
          },
        }}
        onMount={handleEditorDidMount}
      />
      {!outPutValue && !isFocus && <div className='pointer-events-none absolute left-[36px] top-0 leading-H3 text-S3 font-normal text-gray-300'>{placeholder}</div>}
    </>
  )

  return (
    <div className={cn(isExpand && 'h-full')}>
      {noWrapper
        ? <div className='relative no-wrapper' style={{
          height: isExpand ? '100%' : (editorContentHeight) / 2 + CODE_EDITOR_LINE_HEIGHT, // In IDE, the last line can always be in lop line. So there is some blank space in the bottom.
          minHeight: CODE_EDITOR_LINE_HEIGHT,
        }}>
          {main}
        </div>
        : (
          <Base
            className='relative'
            title={title}
            value={outPutValue}
            headerRight={headerRight}
            isFocus={isFocus && !readOnly}
            minHeight={minHeight}
            isInNode={isInNode}
            customEditorWrapClassName={customEditorWrapClassName}
          >
            {main}
          </Base>
        )}
    </div>
  )
}
export default React.memo(CodeEditor)
