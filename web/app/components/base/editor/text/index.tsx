'use client'
import type { FC } from 'react'
import React, { useCallback } from 'react'
import { useBoolean } from 'ahooks'
import { Input as AInput } from 'antd'
import Base from '../base'
const { TextArea } = AInput

type Props = {
  value: string
  onChange: (value: string) => void
  title: JSX.Element | string
  headerRight?: JSX.Element
  minHeight?: number
  onBlur?: () => void
  placeholder?: string
  readonly?: boolean
  isInNode?: boolean
}

const TextEditor: FC<Props> = ({
  value,
  onChange,
  title,
  headerRight,
  minHeight,
  onBlur,
  placeholder,
  readonly,
  isInNode,
}) => {
  // 是否聚焦
  const [isFocus, {
    setTrue: setIsFocus,
    setFalse: setIsNotFocus,
  }] = useBoolean(false)

  // 失去焦点时回调
  const handleBlur = useCallback(() => {
    setIsNotFocus()
    onBlur?.()
  }, [setIsNotFocus, onBlur])

  return (
    <Base
      title={title}
      value={value}
      headerRight={headerRight}
      isFocus={isFocus}
      minHeight={minHeight}
      isInNode={isInNode}
    >
      <TextArea
        value={value}
        onChange={e => onChange(e.target.value)}
        onFocus={setIsFocus}
        onBlur={handleBlur}
        className='w-full !h-full !px-0 !py-0 resize-none bg-transparent border-none'
        placeholder={placeholder}
        readOnly={readonly}
      />
    </Base>
  )
}
export default React.memo(TextEditor)
