'use client'
import type { FC } from 'react'
import React, { useRef, useState } from 'react'
import {
  RiCollapseDiagonalLine,
} from '@remixicon/react'
import Wrap from './wrap'
import cn from '@/utils/classnames'
import { Expand } from '@/app/components/base/icons/src/vender/line/editor'
import useToggleExpend from '@/app/components/workflow/nodes/_base/hooks/use-toggle-expend'
import ResizableBoxInEditor from '@/app/components/base/resizable-box/in-editor'
import CopyBtn from '@/app/components/base/button/copy-button'
import TextButton from '@/app/components/base/button/text-button'

type Props = {
  className?: string
  title: JSX.Element | string
  headerRight?: JSX.Element
  children: JSX.Element
  minHeight?: number
  value: string
  isFocus: boolean
  isInNode?: boolean
  customEditorWrapClassName?: string
}

const Base: FC<Props> = ({
  className,
  customEditorWrapClassName,
  title,
  headerRight,
  children,
  minHeight = 120,
  value,
  isFocus,
  isInNode,
}) => {
  const ref = useRef<HTMLDivElement>(null)
  const {
    wrapClassName,
    wrapStyle,
    isExpand,
    setIsExpand,
    editorExpandHeight,
  } = useToggleExpend({ ref, hasFooter: false, isInNode })
  // 最小高度
  const editorContentMinHeight = minHeight - 28

  // 编辑器内容高度
  const [editorContentHeight, setEditorContentHeight] = useState(editorContentMinHeight)

  return (
    <Wrap className={cn('border border-gray-G6', wrapClassName, customEditorWrapClassName)} style={wrapStyle} isInNode={isInNode} isExpand={isExpand}>
      <div ref={ref} className={cn(className, isExpand && 'h-full', 'rounded', isFocus ? '' : 'overflow-hidden', 'bg-white py-2 px-3')}>
        {/* 头部 */}
        <div className='flex justify-between items-center mb-1'>
          <div className='text-S1 leading-H1 text-gray-G1'>{title}</div>
          <div className='flex items-center gap-2' onClick={(e) => {
            e.nativeEvent.stopImmediatePropagation()
            e.stopPropagation()
          }}>
            {headerRight}
            {/* 拷贝按钮 */}
            <CopyBtn value={value}></CopyBtn>
            {/* 展开按钮  */}
            <TextButton variant='text' onClick={() => setIsExpand(!isExpand)}>
              {isExpand ? <RiCollapseDiagonalLine className='w-4 h-4' /> : <Expand className='w-4 h-4'/>}
            </TextButton>
          </div>
        </div>
        {/* 内部容器 */}
        <ResizableBoxInEditor
          className='pt-[6px] pb-2'
          height={isExpand ? editorExpandHeight : editorContentHeight}
          minHeight={editorContentMinHeight}
          onHeightChange={setEditorContentHeight}
          hideResize={isExpand}
        >
          {children}
        </ResizableBoxInEditor>
      </div>
    </Wrap>
  )
}
export default React.memo(Base)
