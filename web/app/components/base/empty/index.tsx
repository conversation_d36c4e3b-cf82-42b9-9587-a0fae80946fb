import React, { type ReactNode } from 'react'
import s from './styles/index.module.css'
import cn from '@/utils/classnames'

type EmptyProps = {
  text?: string | ReactNode | null
  icon?: string
  wrapClassName?: string
  className?: string
  showLogo?: boolean
}

const Empty = ({ text, className: textClassName, showLogo = true, icon, wrapClassName }: EmptyProps) => {
  return (
    <div className={cn(s.empty, wrapClassName)}>
      {showLogo && <div className={`${s['empty-logo']}`} style={{ backgroundImage: `url(${icon || '/assets/empty.png'})` }}></div>}
      <div className={cn(s['empty-text'], textClassName)}>{text}</div>
    </div>
  )
}

export default React.memo(Empty)
