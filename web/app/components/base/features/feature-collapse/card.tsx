'use client'
import type { FC, ReactNode } from 'react'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider, Switch } from 'antd'
import style from './style.module.css'
// 公共组件
import cn from '@/utils/classnames'
import TextButton from '@/app/components/base/button/text-button'
import { AddCircle } from '@/app/components/base/icons/src/vender/line/action'

export type IFeatureCollapseCardProps = {
  // 样式明
  className?: string
  // 标题
  title: ReactNode
  // 提示词
  tooltip?: ReactNode
  // 描述
  desc?: ReactNode
  // 是否展示开关按钮
  useSwitch?: boolean
  // 开关按钮是否禁用
  disabled?: boolean
  // 新增按钮是否禁用
  addDisabled?: boolean
  // 开关按钮值
  value?: boolean
  // 新增函数
  onAdd?: () => void
  // 变更函数
  onChange?: (state: any) => void
  // 子节点
  children?: ReactNode
  headerRight?: ReactNode
  // 右侧提示描述
  rightTooltipDesc?: ReactNode

}

const FeatureCollapseCard: FC<IFeatureCollapseCardProps> = ({
  className,
  title,
  desc,
  children,
  headerRight,
  useSwitch = true,
  disabled,
  addDisabled,
  value,
  onAdd,
  onChange,
  rightTooltipDesc,
}) => {
  const { t } = useTranslation()
  // 是否展开
  const [expand, setExpand] = useState(false)

  const validChildren = React.Children.toArray(children).filter(children => children)

  // 内容默认展开
  useEffect(() => {
    if (validChildren.length > 0)
      setExpand(true)
  }, [children])

  // 自动展开与收缩
  const handleChange = (checked: boolean) => {
    if (checked && !expand && validChildren.length > 0)
      setExpand(true)

    else if (!checked && expand)
      setExpand(false)

    onChange && onChange(checked)
  }

  return (
    <div className={cn(style.cardWrap, className)}>
      {/* Header */}
      <div className='flex justify-between items-center'>
        <div className='title-14-24 truncate"'>{title}</div>
        <div className='flex gap-3 items-center'>
          {headerRight && (
            <div>{headerRight}</div>
          )}
          {onAdd && (
            <TextButton disabled={addDisabled} variant='hover' onClick={onAdd}>
              <AddCircle className='w-4 h-4' />
              <div>{t('common.operation.add')}</div>
            </TextButton>
          )}
          {(headerRight || onAdd) && useSwitch
              && <Divider type='vertical' className='!mx-0'></Divider>}
          {/* 右侧自定义描述 */}
          {
            rightTooltipDesc && (
              <div className='flex h-full items-center text-xs text-[#878ea0]'>
                {rightTooltipDesc}
              </div>
            )
          }
          {useSwitch && (
            <Switch size='small' disabled={disabled} onChange={handleChange} value={value} />
          )}
        </div>
      </div>
      {desc && (<div className={style.desc}>{desc}</div>)}
      {/* Body */}
      {expand && validChildren.length > 0 && (
        <div className='pt-2'>
          {validChildren}
        </div>
      )}
    </div>
  )
}
export default React.memo(FeatureCollapseCard)
