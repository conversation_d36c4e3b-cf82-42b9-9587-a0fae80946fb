import cn from '@/utils/classnames'

type Props = {
  className?: string
  label?: string
  children?: React.ReactNode
}
const FeatureCollapseItem = ({
  className,
  label,
  children,
}: Props) => {
  return (
    <div className={cn('flex items-center space-x-2', className)}>
      {label && <div className='desc-12 shrink-0'>{label}</div>}
      {children}
    </div>
  )
}

export default FeatureCollapseItem
