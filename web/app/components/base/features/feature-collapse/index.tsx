'use client'
import type { FC, ReactNode } from 'react'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider, Switch } from 'antd'
import style from './style.module.css'
// 公共组件
import cn from '@/utils/classnames'
import type { FeatureEnum } from '@/app/components/base/features/types'
import Tooltip from '@/app/components/base/tooltip'
import { ArrowLeft, ArrowRight } from '@/app/components/base/icons/src/vender/line/arrows'
import TextButton from '@/app/components/base/button/text-button'
import { Add, AddCircle } from '@/app/components/base/icons/src/vender/line/action'

export type IFeaturePanelProps = {
  // 是否为工作流模式
  inWorkflow?: boolean
  // 样式明
  className?: string
  // 标题
  title: ReactNode
  // 提示词
  tooltip?: ReactNode
  // 描述
  desc?: ReactNode
  // 是否显示展开
  showExpand?: boolean
  // 展开位置
  expondPosition?: 'left' | 'right'
  // 是否展示开关按钮
  useSwitch?: boolean
  // 开关按钮是否禁用
  disabled?: boolean
  // 新增按钮是否禁用
  addDisabled?: boolean
  // 开关按钮值
  value?: boolean
  // 新增函数
  onAdd?: () => void
  // 变更函数
  onChange?: (state: any) => void
  // 子节点
  children?: ReactNode
  headerRight?: ReactNode
  type?: FeatureEnum
  // 右侧提示描述
  rightTooltipDesc?: ReactNode

}

const FeatureCollapse: FC<IFeaturePanelProps> = ({
  className,
  title,
  inWorkflow,
  tooltip,
  desc,
  children,
  headerRight,
  showExpand = true,
  expondPosition = 'left',
  useSwitch = true,
  type,
  disabled,
  addDisabled,
  value,
  onAdd,
  onChange,
  rightTooltipDesc,
}) => {
  const { t } = useTranslation()
  // 是否展开
  const [expand, setExpand] = useState(false)

  const validChildren = React.Children.toArray(children).filter(children => children)

  // 内容默认展开
  useEffect(() => {
    if (validChildren.length > 0)
      setExpand(true)
  }, [children])

  // 自动展开与收缩
  const handleChange = (checked: boolean) => {
    if (checked && !expand && validChildren.length > 0)
      setExpand(true)

    else if (!checked && expand)
      setExpand(false)

    onChange && onChange(checked)
  }
  // feature在聊天助手折叠样式
  if (!inWorkflow) {
    return (
      <div>
        {/* 头部 */}
        <div className='flex justify-between items-center gap-2'>
          {/* 左侧部分 */}
          <div className={cn(validChildren.length > 0 ? 'cursor-pointer' : '', 'flex gap-1 items-center overflow-hidden')} onClick={() => setExpand(v => validChildren.length > 0 && !v)}>
            {/* 展开按钮 */}
            {expondPosition === 'left' && showExpand && (
              <ArrowRight
                className={cn(validChildren.length > 0 ? 'cursor-pointer' : 'cursor-not-allowed', expand && 'rotate-90')}
              />
            )}
            {/* 标题 */}
            <div className='title-14-24 grow truncate'>{title}</div>
            {/* 提示图标 */}
            {tooltip && (
              <Tooltip popupContent={tooltip}></Tooltip>
            )}
          </div>
          {/* 右侧部分 */}
          <div className='flex gap-2 items-center'>
            {/* 右侧自定义描述 */}
            {
              rightTooltipDesc && (
                <div className='flex h-full items-center text-xs text-[#878ea0]'>
                  {rightTooltipDesc}
                </div>
              )
            }
            {/* 右侧自定义操作按钮 */}
            {headerRight && <div className='flex h-full items-center'>{headerRight}</div>}
            {/* 右侧添加按钮 */}
            {!type && onAdd && (
              <TextButton disabled={addDisabled} variant='primary' onClick={onAdd}><Add/></TextButton>
            )}
            {/* 右侧switch开关按钮 */}
            {useSwitch && (
              <Switch size='small' disabled={disabled} onChange={handleChange} defaultValue={value} />
            )}
            {/* 展开按钮 */}
            {expondPosition === 'right' && showExpand && (
              <ArrowLeft
                className={cn(validChildren.length > 0 ? 'cursor-pointer' : 'cursor-not-allowed', expand && '-rotate-90')}
              />
            )}
          </div>
        </div>

        {/* 展开内容 */}
        {expand && validChildren.length > 0 && (
          <div className='pt-2'>
            {validChildren}
          </div>
        )}
      </div>
    )
  }
  // feature在工作流弹窗内卡片样式
  else {
    return (
      <div className={cn(style.cardWrap, className)}>
        {/* Header */}
        <div className='flex justify-between items-center'>
          <div className='flex items-center w-0 grow mr-6 gap-1'>
            <div className='title-14-24 truncate'>{title}</div>
            {/* 提示图标 */}
            {tooltip && (
              <Tooltip popupContent={tooltip}></Tooltip>
            )}
          </div>
          <div className='flex gap-3 items-center'>
            {headerRight && (
              <div>{headerRight}</div>
            )}
            {onAdd && (
              <TextButton disabled={addDisabled} variant='hover' onClick={onAdd}>
                <AddCircle className='w-4 h-4' />
                <div>{t('common.operation.add')}</div>
              </TextButton>
            )}
            {(headerRight || onAdd) && useSwitch
              && <Divider type='vertical' className='!mx-0'></Divider>}
            {/* 右侧自定义描述 */}
            {
              rightTooltipDesc && (
                <div className='flex h-full items-center text-xs text-[#878ea0]'>
                  {rightTooltipDesc}
                </div>
              )
            }
            {useSwitch && (
              <Switch size='small' disabled={disabled} onChange={handleChange} value={value} />
            )}
          </div>
        </div>
        {desc && (<div className={style.desc}>{desc}</div>)}
        {/* Body */}
        {expand && validChildren.length > 0 && (
          <div className='pt-2'>
            {validChildren}
          </div>
        )}
      </div>
    )
  }
}
export default React.memo(FeatureCollapse)
