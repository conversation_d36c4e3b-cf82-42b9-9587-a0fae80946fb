'use client'
import type { FC, ReactNode } from 'react'
import React, { useState } from 'react'
import { Switch } from 'antd'
// 公共组件
import cn from '@/utils/classnames'
import type { FeatureEnum } from '@/app/components/base/features/types'
import Tooltip from '@/app/components/base/tooltip'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import TextButton from '@/app/components/base/button/text-button'
import { Add } from '@/app/components/base/icons/src/vender/line/action'

export type IFeatureCollapseInWorkflowProps = {
  // 是否为工作流模式
  inWorkflow?: boolean
  // 样式明
  className?: string
  // 标题
  title: ReactNode
  // 提示词
  tooltip?: ReactNode
  // 描述
  desc?: ReactNode
  // 是否显示展开
  showExpand?: boolean
  // 展开位置
  expondPosition?: 'left' | 'right'
  // 是否展示开关按钮
  useSwitch?: boolean
  // 开关按钮是否禁用
  disabled?: boolean
  // 新增按钮是否禁用
  addDisabled?: boolean
  // 开关按钮值
  value?: boolean
  // 新增函数
  onAdd?: () => void
  // 变更函数
  onChange?: (state: any) => void
  // 子节点
  children?: ReactNode
  // 右侧自定义
  headerRight?: ReactNode
  // 左侧自定义
  leftRight?: ReactNode
  type?: FeatureEnum
  // 右侧提示描述
  rightTooltipDesc?: ReactNode
  childrenClassName?: string
}

const FeatureCollapseInWorkflow: FC<IFeatureCollapseInWorkflowProps> = ({
  title,
  children,
  tooltip,
  headerRight,
  leftRight,
  showExpand = false,
  useSwitch = false,
  type,
  disabled,
  addDisabled,
  value,
  onAdd,
  onChange,
  rightTooltipDesc,
  className,
  childrenClassName,
}) => {
  // 是否展开
  const [expand, setExpand] = useState(false)

  const validChildren = React.Children.toArray(children).filter(children => children)

  // 自动展开与收缩
  const handleChange = (checked: boolean) => {
    if (checked && !expand)
      setExpand(true)

    else if (!checked && expand)
      setExpand(false)

    onChange && onChange(checked)
  }
  const toggleExpand = () => {
    if (validChildren.length > 0)
      setExpand(!expand)
  }

  // feature在聊天助手折叠样式
  return (
    <div className={className}>
      {/* 头部 */}
      <div className={cn(showExpand && 'cursor-pointer', 'flex justify-between items-center gap-2')} onClick={() => showExpand && toggleExpand()}>
        {/* 左侧部分 */}
        <div className={cn('flex gap-1 items-center overflow-hidden')}>
          {/* 标题 */}
          <div className='title-14-24 grow truncate'>{title}</div>
          {/* 提示图标 */}
          {tooltip && (
            <Tooltip popupContent={tooltip}></Tooltip>
          )}
          {/* 左侧自定义 */}
          { leftRight }
        </div>
        {/* 右侧部分 */}
        <div className='flex gap-2 items-center'>
          {/* 右侧自定义描述 */}
          {
            rightTooltipDesc && (
              <div className='flex h-full items-center text-xs text-[#878ea0]'>
                {rightTooltipDesc}
              </div>
            )
          }
          {/* 右侧自定义操作按钮 */}
          {headerRight && <div className='flex h-full items-center'>{headerRight}</div>}
          {/* 右侧添加按钮 */}
          {!type && onAdd && (
            <TextButton disabled={addDisabled} variant='primary' onClick={onAdd}><Add/></TextButton>
          )}
          {/* 右侧switch开关按钮 */}
          {useSwitch && (
            <Switch size='small' disabled={disabled} onChange={handleChange} value={value} />
          )}
          {/* 展开按钮 */}
          {showExpand && (
            <ArrowDown
              className={cn(validChildren.length > 0 ? 'cursor-pointer' : 'cursor-not-allowed', expand && '-rotate-180')}
            />
          )}
        </div>
      </div>

      {/* 展开内容 */}
      {((!showExpand || expand) && validChildren.length > 0) && (
        <div className={cn(childrenClassName, 'pt-2')}>
          {validChildren}
        </div>
      )}
    </div>
  )
}

export default React.memo(FeatureCollapseInWorkflow)

FeatureCollapseInWorkflow.displayName = 'FeatureCollapseInWorkflow'
