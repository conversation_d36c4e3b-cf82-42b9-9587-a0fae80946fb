'use client'
import type { FC, ReactNode } from 'react'
import React, { useState } from 'react'
import style from '@/app/components/app/configuration/styles/style.module.scss'
import cn from '@/utils/classnames'

export type IFeaturePanelProps = {
  className?: string
  title: ReactNode
  children?: ReactNode
}

const FeatureCollapseGroup: FC<IFeaturePanelProps> = ({
  className,
  title,
  children,
}) => {
  const [childrenExpand, setChildrenExpand] = useState(false)

  return (
    <div className={cn(className, style.flexColGap8)}>
      {/* label */}
      <div className='text-S3 leading-H3 text-gray-G3'>
        {title}
      </div>
      {/* Body */}
      {children && (
        <div className={style.flexColGap8}>
          {children}
        </div>
      )}
    </div>
  )
}
export default React.memo(FeatureCollapseGroup)
