// ===================== 文件说明 =====================
// 本文件定义了 features（特性）相关的 TypeScript 类型，包括语音、检索、文件上传、标注、背景图等功能模块的配置结构。
// 这些类型用于描述各类功能开关、参数、资源引用等，支撑应用的动态特性管理。
// ====================================================
import type { Resolution, TransferMethod, TtsAutoPlay } from '@/types/model'
import type { CropperConfig } from '@/app/components/base/cropper/type'

// ===================== 通用开关类型 =====================
// 表示功能是否启用的通用类型
export type EnabledOrDisabled = {
  enabled?: boolean // 是否启用该功能
}

// ===================== 具体特性类型定义 =====================
// “更多类似”功能开关
export type MoreLikeThis = EnabledOrDisabled

// 开场白配置，包含开场白文本和推荐问题
export type OpeningStatement = EnabledOrDisabled & {
  opening_statement?: string // 开场白内容
  suggested_questions?: string[] // 推荐问题列表
}

// 答案后推荐问题
export type SuggestedQuestionsAfterAnswer = EnabledOrDisabled

// ===================== 语音相关类型 =====================
// 语音默认值配置
export type VoliceDefaulValue = {
  languageDefaulVal?: string // 默认语言
  timbreDefaulValue?: string // 默认音色
}
// 语音输入配置
export type voiceInputConfig = EnabledOrDisabled & {
  language?: string // 语音输入语言
  timbre?: string // 语音输入音色
  auto_play?: boolean // 是否自动播放
} & VoliceDefaulValue
// 语音对话配置
export type voiceConversationConfig = EnabledOrDisabled & {
  language?: string // 语音对话语言
  timbre?: string // 语音对话音色
} & VoliceDefaulValue
// 文本转语音配置
export type TextToSpeech = EnabledOrDisabled & {
  language?: string // TTS 语言
  voice?: string // TTS 音色
  autoPlay?: TtsAutoPlay // 是否自动播放
  voice_input?: voiceInputConfig // 嵌套语音输入配置
  voice_conversation?: voiceConversationConfig // 嵌套语音对话配置
}
// 语音转文本配置
export type SpeechToText = EnabledOrDisabled

// ===================== 检索资源类型 =====================
// 检索资源配置，包含检索模型、权重、重排序等
export type RetrieverResource = EnabledOrDisabled

// ===================== 敏感词规避类型 =====================
// 敏感词规避配置
export type SensitiveWordAvoidance = EnabledOrDisabled & {
  type?: string // 规避类型
  config?: any // 规避参数
}

// ===================== 文件上传类型 =====================
// 文件上传配置，支持图片、类型限制、方式限制等
export type FileUpload = {
  image?: EnabledOrDisabled & {
    detail?: Resolution // 图片分辨率
    number_limits?: number // 图片数量限制
    transfer_methods?: TransferMethod[] // 传输方式
  }
  allowed_file_types?: string[] // 允许的文件类型
  allowed_file_extensions?: string[] // 允许的文件扩展名
  allowed_file_upload_methods?: TransferMethod[] // 允许的上传方式
  number_limits?: number // 文件数量限制
} & EnabledOrDisabled

// ===================== 标注回复类型 =====================
// 标注回复配置
export type AnnotationReplyConfig = {
  enabled: boolean // 是否启用
  id?: string // 标注 ID
  score_threshold?: number // 分数阈值
  embedding_model?: {
    embedding_provider_name: string // 嵌入提供方
    embedding_model_name: string // 嵌入模型名
  }
}

// ===================== 聊天背景图类型 =====================
// 聊天背景图配置
export type AppChatBgConfig = {
  url?: string // 背景图 URL
  fontColor?: 'white' | 'black' // 字体颜色
  wide?: CropperConfig // 宽屏裁剪配置
  narrow?: CropperConfig // 窄屏裁剪配置
  enabled: boolean // 是否启用
}

// ===================== 语音输入/对话声明类型 =====================
// 语音输入声明
export type VoiceInputStatement = EnabledOrDisabled & {
  voiceInput_language?: string // 语音输入语言
  voiceInput_timbre?: string // 语音输入音色
  voiceInput_autoPlay?: boolean // 是否自动播放
} & VoliceDefaulValue
// 语音对话声明
export type VoiceConversationStatement = EnabledOrDisabled & {
  voiceConversation_language?: string // 语音对话语言
  voiceConversation_timbre?: string // 语音对话音色
} & VoliceDefaulValue

// ===================== 特性枚举 =====================
// 所有特性类型的枚举，便于统一管理
export enum FeatureEnum {
  moreLikeThis = 'moreLikeThis', // 更多类似
  opening = 'opening', // 开场白
  suggested = 'suggested', // 推荐问题
  text2speech = 'text2speech', // 文本转语音
  speech2text = 'speech2text', // 语音转文本
  citation = 'citation', // 引用
  moderation = 'moderation', // 敏感词规避
  file = 'file', // 文件上传
  annotationReply = 'annotationReply', // 标注回复
  voiceInput = 'voiceInput', // 语音输入
  voiceConversation = 'voiceConversation', // 语音对话
  chatBg = 'chatBackground', // 聊天背景
}

// ===================== 特性主类型 =====================
// 所有特性配置的主结构体，key 为特性枚举，value 为对应配置
export type Features = {
  [FeatureEnum.moreLikeThis]?: MoreLikeThis // 更多类似
  [FeatureEnum.opening]?: OpeningStatement // 开场白
  [FeatureEnum.voiceInput]?: VoiceInputStatement // 语音输入
  [FeatureEnum.voiceConversation]?: VoiceConversationStatement // 语音对话
  [FeatureEnum.suggested]?: SuggestedQuestionsAfterAnswer // 推荐问题
  [FeatureEnum.text2speech]?: TextToSpeech // 文本转语音
  [FeatureEnum.speech2text]?: SpeechToText // 语音转文本
  [FeatureEnum.citation]?: RetrieverResource // 引用/检索
  [FeatureEnum.moderation]?: SensitiveWordAvoidance // 敏感词规避
  [FeatureEnum.file]?: FileUpload // 文件上传
  [FeatureEnum.annotationReply]?: AnnotationReplyConfig // 标注回复
  [FeatureEnum.chatBg]?: AppChatBgConfig // 聊天背景
}

// ===================== 特性变更回调类型 =====================
// 特性变更回调函数类型
export type OnFeaturesChange = (features?: Features) => void
