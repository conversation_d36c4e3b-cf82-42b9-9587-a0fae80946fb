import { Fragment, useCallback } from 'react'
import type { ReactNode } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import style from './styles/style.module.css'
import cn from '@/utils/classnames'
import { Close } from '@/app/components/base/icons/src/vender/line/general'

type DialogProps = {
  className?: string
  title?: string
  children: ReactNode
  show: boolean
  onClose?: () => void
}

const DialogWrapper = ({
  className,
  title,
  children,
  show,
  onClose,
}: DialogProps) => {
  const close = useCallback(() => onClose?.(), [onClose])
  return (
    <Transition appear show={show} as={Fragment}>
      <Dialog as="div" className="relative z-40" onClose={close}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className={style.mask} />
        </Transition.Child>

        <div className="fixed inset-0">
          <div className={cn('flex flex-col items-end justify-center min-h-full')}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className={cn(style.panelWrap, className)}>
                <div className={style.header}>
                  <div>{title}</div>
                  <Close onClick={onClose} className='w-4 h-4 text-gray-G2 cursor-pointer' />
                </div>
                <div className={style.scrollContent}>
                  {/* <Scrollbar style={{maxHeight:'calc(100vh - 50px)'}}> */}
                  <div className={style.content}>
                    {children}
                  </div>
                  {/* </Scrollbar> */}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition >
  )
}

export default DialogWrapper
