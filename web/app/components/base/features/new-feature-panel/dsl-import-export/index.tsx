import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider } from 'antd'
import UpdateDslModal from './update-dsl-modal'
import DSLExportConfirmModal from './dsl-export-confirm-modal'
import type { EnvironmentVariable } from '@/app/components/workflow/types'
import { exportAppConfig } from '@/service/apps'
import { useStore as useAppStore } from '@/app/components/app/store'
import { fetchWorkflowDraft } from '@/service/workflow'
// 公共组件
import { Download, Upload01 } from '@/app/components/base/icons/src/vender/line/action'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import TextButton from '@/app/components/base/button/text-button'
import Toast from '@/app/components/base/toast'

const DslImportExport = () => {
  const { t } = useTranslation()
  const appDetail = useAppStore(s => s.appDetail)
  // 显示导入dsl弹窗
  const [showImportDSLModal, setShowImportDSLModal] = useState(false)
  // 环境变量列表
  const [secretEnvList, setSecretEnvList] = useState<EnvironmentVariable[]>([])

  // 导出应用配置
  const onExport = async (include = false) => {
    if (!appDetail?.id)
      return
    try {
      const { data } = await exportAppConfig({
        appID: appDetail.id,
        include,
        type: appDetail.mode === 'workflow' ? 'workflow_tool' : '',
      })
      const a = document.createElement('a')
      const file = new Blob([data], { type: 'application/yaml' })
      a.href = URL.createObjectURL(file)
      a.download = `${appDetail.name}.yml`
      a.click()
    }
    catch (e) {
      Toast.notify({ type: 'error', message: t('workflow.dsl.exportFailed') })
    }
  }
  // 导出确认
  const exportCheck = async () => {
    if (!appDetail?.id)
      return
    if (appDetail.mode !== 'workflow' && appDetail.mode !== 'advanced-chat') {
      onExport()
      return
    }
    try {
      const workflowDraft = await fetchWorkflowDraft(`/apps/${appDetail.id}/workflows/draft`)
      const list = (workflowDraft.environment_variables || []).filter(env => env.value_type === 'secret')
      if (list.length === 0) {
        onExport()
        return
      }
      setSecretEnvList(list)
    }
    catch (e) {
      Toast.notify({ type: 'error', message: t('workflow.dsl.exportFailed') })
    }
  }
  return (
    <>
      <FeatureCollapse
        inWorkflow
        title={t('workflow.dsl.title')}
        useSwitch={false}
        headerRight={(
          <div className='flex gap-2 items-center'>
            <TextButton variant={'hover'} onClick={() => setShowImportDSLModal(true)}>
              <Download className='w-4 h-4' />
              <div>{t('workflow.dsl.import')}</div>
            </TextButton>
            <Divider type='vertical' className='!mx-0'></Divider>
            <TextButton variant={'hover'} onClick={exportCheck}>
              <Upload01 className='w-4 h-4' />
              <div>{t('workflow.dsl.export')}</div>
            </TextButton>
          </div>
        )}
      >

      </FeatureCollapse>
      {/* 导入dsl弹窗 */}
      {showImportDSLModal && (
        <UpdateDslModal
          onCancel={() => setShowImportDSLModal(false)}
          onImport={(() => {})}
        />
      )}
      {/* 导出确认弹窗 */}
      {secretEnvList.length > 0 && (
        <DSLExportConfirmModal
          envList={secretEnvList}
          onConfirm={onExport}
          onClose={() => setSecretEnvList([])}
        />
      )}
    </>
  )
}

export default DslImportExport
