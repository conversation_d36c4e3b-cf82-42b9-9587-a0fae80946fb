import React, { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import SettingModal from '@/app/components/base/features/new-feature-panel/file-upload/setting-modal'
import { useFeatures, useFeaturesStore } from '@/app/components/base/features/hooks'
import type { OnFeaturesChange } from '@/app/components/base/features/types'
import { FeatureEnum } from '@/app/components/base/features/types'
import { Setting } from '@/app/components/base/icons/src/vender/line/action'
import TextButton from '@/app/components/base/button/text-button'

type Props = {
  disabled: boolean
  onChange?: OnFeaturesChange
}

const FileUpload = ({
  disabled,
  onChange,
}: Props) => {
  const { t } = useTranslation()
  const file = useFeatures(s => s.features.file)
  const inWorkflow = useFeatures(s => s.inWorkflow)
  const featuresStore = useFeaturesStore()
  // 文件配置弹窗是否打开
  const [modalOpen, setModalOpen] = useState(false)

  // 变更上传文件配置
  const handleChange = useCallback((type: FeatureEnum, enabled: boolean) => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()

    const newFeatures = produce(features, (draft) => {
      draft[type] = {
        ...draft[type],
        enabled,
      }
    })
    setFeatures(newFeatures)
    if (onChange)
      onChange()
  }, [featuresStore, onChange])

  return (
    <FeatureCollapse
      inWorkflow={inWorkflow}
      title={t('appDebug.feature.fileUpload.title')}
      tooltip={t('appDebug.feature.fileUpload.description')}
      value={file?.enabled}
      onChange={state => handleChange(FeatureEnum.file, state)}
      disabled={disabled}
      headerRight={(
        <div className='flex gap-4'>
          <SettingModal
            open={modalOpen && !disabled}
            onOpen={(v) => {
              setModalOpen(v)
            }}
            onChange={onChange}
          >
            <TextButton variant={'text'} type={'text'} disabled={!file?.enabled || disabled}>
              <Setting/>
              {t('common.operation.settings')}
            </TextButton>
          </SettingModal>
        </div>
      )}
    >
    </FeatureCollapse>
  )
}

export default FileUpload
