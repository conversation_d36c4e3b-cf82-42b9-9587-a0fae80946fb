import React, { useCallback, useMemo, useState } from 'react'
import produce from 'immer'
import { useTranslation } from 'react-i18next'
import FileUploadSetting from '@/app/components/workflow/common/file-upload-setting'
import Button from '@/app/components/base/button'
import { useFeatures, useFeaturesStore } from '@/app/components/base/features/hooks'
import type { OnFeaturesChange } from '@/app/components/base/features/types'
import type { UploadFileSetting } from '@/app/components/workflow/types'
import { SupportUploadFileTypes } from '@/app/components/workflow/types'
import { FILE_EXTS } from '@/app/components/base/editor/prompt/constants'
import { Close } from '@/app/components/base/icons/src/vender/line/general'

type SettingContentProps = {
  imageUpload?: boolean
  onClose: () => void
  onChange?: OnFeaturesChange
}
const SettingContent = ({
  imageUpload,
  onClose,
  onChange,
}: SettingContentProps) => {
  const { t } = useTranslation()
  const featuresStore = useFeaturesStore()
  const file = useFeatures(state => state.features.file)
  const fileSettingPayload = useMemo(() => {
    return {
      allowed_file_upload_methods: file?.allowed_file_upload_methods || ['local_file', 'remote_url'],
      allowed_file_types: file?.allowed_file_types || [SupportUploadFileTypes.image],
      allowed_file_extensions: file?.allowed_file_extensions || FILE_EXTS[SupportUploadFileTypes.image],
      max_length: file?.number_limits || 3,
    } as UploadFileSetting
  }, [file])
  const [tempPayload, setTempPayload] = useState<UploadFileSetting>(fileSettingPayload)

  const handleChange = useCallback(() => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()

    const newFeatures = produce(features, (draft) => {
      draft.file = {
        ...draft.file,
        allowed_file_upload_methods: tempPayload.allowed_file_upload_methods,
        number_limits: tempPayload.max_length,
        allowed_file_types: tempPayload.allowed_file_types,
        allowed_file_extensions: tempPayload.allowed_file_extensions,
      }
    })

    setFeatures(newFeatures)
    if (onChange)
      onChange()
  }, [featuresStore, onChange, tempPayload])

  return (
    <>
      <div className='mb-4 flex items-center justify-between'>
        <div className='title-16-26'>{!imageUpload ? t('appDebug.feature.fileUpload.modalTitle') : t('appDebug.feature.imageUpload.modalTitle')}</div>
        <Close onClick={onClose} className='w-4 h-4 cursor-pointer' />
      </div>
      <FileUploadSetting
        isMultiple
        inFeaturePanel
        hideSupportFileType={imageUpload}
        payload={tempPayload}
        onChange={(p: UploadFileSetting) => setTempPayload(p)}
      />
      <div className='flex items-center justify-end gap-4'>
        <Button
          variant={'secondary-accent'}
          onClick={onClose}
        >
          {t('common.operation.cancel')}
        </Button>
        <Button
          variant='primary'
          onClick={handleChange}
          disabled={tempPayload.allowed_file_types.length === 0}
        >
          {t('common.operation.save')}
        </Button>
      </div>
    </>
  )
}

export default React.memo(SettingContent)
