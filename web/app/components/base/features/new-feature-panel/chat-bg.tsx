import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import Confirm from '../../confirm'
import AppBgCropper from '@/app/components/app/common/bg-cropper'
// 公共组件
import Avatar from '@/app/components/base/avatar'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import TextButton from '@/app/components/base/button/text-button'
import { Delete, Edit } from '@/app/components/base/icons/src/vender/line/general'
import { useFeatures, useFeaturesStore } from '@/app/components/base/features/hooks'
import type { AppChatBgConfig, OnFeaturesChange } from '@/app/components/base/features/types'
import { FeatureEnum } from '@/app/components/base/features/types'
import cn from '@/utils/classnames'

type Props = {
  disabled?: boolean
  onChange?: OnFeaturesChange
}

const ChatBg = ({
  disabled,
  onChange,
}: Props) => {
  const { t } = useTranslation()
  const featuresStore = useFeaturesStore()
  const inWorkflow = useFeatures(s => s.inWorkflow)
  const bgConfig = useFeatures(s => s.features[FeatureEnum.chatBg])

  // 是否显示背景图裁剪弹窗
  const [showBgCropper, setShowBgCropper] = useState(false)
  // 是否显示确认删除弹窗
  const [showConfirmDelete, setShowConfirmDelete] = useState(false)

  // 变更是否可用
  const changeConfigEnable = (enabled: boolean) => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()

    const newFeatures = produce(features, (draft) => {
      draft[FeatureEnum.chatBg] = {
        ...draft[FeatureEnum.chatBg],
        enabled,
      }
    })
    setFeatures(newFeatures)
    onChange && onChange(newFeatures)
  }
  // 变更背景图配置
  const changeChatBgConfig = (config: AppChatBgConfig) => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()

    const newFeatures = produce(features, (draft) => {
      draft[FeatureEnum.chatBg] = {
        ...config,
        enabled: true,
      }
    })
    setFeatures(newFeatures)
    onChange && onChange(newFeatures)
    setShowBgCropper(false)
  }
  // 删除背景配置
  const deleteChatBgConfig = () => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()

    const newFeatures = produce(features, (draft) => {
      draft[FeatureEnum.chatBg] = {
        enabled: false,
        id: '',
      }
    })
    setFeatures(newFeatures)
    setShowConfirmDelete(false)
    onChange && onChange(newFeatures)
  }

  return (
    <FeatureCollapse
      inWorkflow={inWorkflow}
      title={t('appDebug.feature.chatBg.title')}
      tooltip={t('appDebug.feature.chatBg.description')}
      desc={t('appDebug.feature.chatBg.description')}
      useSwitch={inWorkflow}
      disabled={disabled}
      addDisabled={(inWorkflow && (!bgConfig?.enabled || !!bgConfig.url)) || (!inWorkflow && !!bgConfig?.url)}
      value={bgConfig?.enabled}
      onAdd={() => setShowBgCropper(true)}
      onChange={changeConfigEnable}
    >
      {
        bgConfig?.enabled && bgConfig?.url && <>
          <div className='flex items-center justify-between'>
            {/* 背景原图 */}
            <Avatar size={60} className={cn('!rounded', !inWorkflow && '!ml-5')} avatar={bgConfig?.url}></Avatar>
            {/* 操作栏 */}
            <div className='flex items-center gap-4'>
              <TextButton variant='hover' onClick={() => setShowBgCropper(true)}>
                <Edit className='w-4 h-4'></Edit>
              </TextButton>
              <TextButton variant='hover' onClick={() => setShowConfirmDelete(true)}>
                <Delete className='w-4 h-4'></Delete>
              </TextButton>
            </div>
          </div>
        </>
      }
      {/* 背景图裁剪弹窗 */}
      {
        showBgCropper && <AppBgCropper
          height={356}
          onClose={() => setShowBgCropper(false)}
          onSubmit={changeChatBgConfig}
          config={bgConfig}
        ></AppBgCropper>
      }
      {
        showConfirmDelete && <Confirm
          isShow={showConfirmDelete}
          onCancel={() => setShowConfirmDelete(false)}
          title={t('appDebug.feature.chatBg.delete')}
          content={t('appDebug.feature.chatBg.deleteDescription')}
          onConfirm={deleteChatBgConfig}
        ></Confirm>
      }
      {/* 删除确认弹窗 */}
    </FeatureCollapse>
  )
}

export default ChatBg
