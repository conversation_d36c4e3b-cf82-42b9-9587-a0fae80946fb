'use client'
import React, { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import cn from 'classnames'
import ParamConfigContent from './param-config-content'
import TextButton from '@/app/components/base/button/text-button'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import { useFeatures, useFeaturesStore } from '@/app/components/base/features/hooks'
import type { FileUpload, OnFeaturesChange } from '@/app/components/base/features/types'
import { Setting } from '@/app/components/base/icons/src/vender/line/action'
import style from '@/app/components/app/configuration/styles/style.module.scss'

type Props = {
  disabled?: boolean
  onChange?: OnFeaturesChange
}

const ConfigVision = ({
  disabled,
  onChange,
}: Props) => {
  const { t } = useTranslation()
  // const { isShowVisionConfig } = useContext(ConfigContext)
  const file = useFeatures(s => s.features.file)
  const featuresStore = useFeaturesStore()
  const [configOpen, setConfigOpen] = React.useState(false)

  const handleChange = useCallback((data: FileUpload) => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()
    const newFeatures = produce(features, (draft) => {
      draft.file = {
        ...draft.file,
        enabled: data.enabled,
        image: {
          enabled: data.enabled,
          detail: data.image?.detail,
          transfer_methods: data.image?.transfer_methods,
          number_limits: data.image?.number_limits,
        },
      }
    })
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }, [featuresStore, onChange])

  const handleSwitch = useCallback((enabled: boolean) => {
    if (enabled) {
      setConfigOpen(true)
      handleChange({
        ...(file || {}),
        enabled,
      })
    }

    else {
      handleChange({
        ...(file || {}),
        enabled,
      })
    }
  }, [file, setConfigOpen, handleChange])

  // if (!isShowVisionConfig)
  //   return null

  return (
    <>
      <FeatureCollapse
        title={t('appDebug.vision.name')}
        tooltip={t('appDebug.vision.description')}
        value={file?.enabled}
        onChange={handleSwitch}
        showExpand={false}
        headerRight={
          <TextButton variant={'text'} type={'text'} onClick={() => setConfigOpen(true)} disabled={disabled}>
            <div className={cn(style.textBtn)}>
              {/* <ParamConfig /> */}
              <Setting className='w-4 h-4' />
              <div>{t('appDebug.voice.settings')}</div>
            </div>
          </TextButton>
        }
        disabled={disabled}
      >
      </FeatureCollapse>
      {configOpen && (
        <ParamConfigContent onChange={onChange} onClose={() => setConfigOpen(false)}/>
      )}
    </>
  )
}
export default React.memo(ConfigVision)
