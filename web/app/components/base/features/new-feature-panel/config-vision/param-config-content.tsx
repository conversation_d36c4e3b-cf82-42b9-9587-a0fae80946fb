'use client'
import React, { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import { Form } from 'antd'
import { Resolution, TransferMethod } from '@/types/model'
import ParamItem from '@/app/components/datasets/common/param-item'
// 公共能力
import { useFeatures, useFeaturesStore } from '@/app/components/base/features/hooks'
import type { FileUpload, OnFeaturesChange } from '@/app/components/base/features/types'
import Modal from '@/app/components/base/modal'
import Radio from '@/app/components/base/radio'
import Button from '@/app/components/base/button'

type Props = {
  onChange?: OnFeaturesChange
  onClose?: () => void
}

const MIN = 1
const MAX = 6
const ParamConfigContent = ({
  onChange,
  onClose,
}: Props) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const file = useFeatures(s => s.features.file)
  const featuresStore = useFeaturesStore()

  // 分辨率
  const [resolution, setResolution] = useState(file?.image?.detail || Resolution.high)
  // 上传方式
  const [uploadMethod, setUploadMethod] = useState(file?.allowed_file_upload_methods || [])
  // 上传限制
  const [numberLimits, setNumberLimits] = useState(file?.number_limits || 0.5)

  const handleChange = useCallback((data: FileUpload) => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()

    const newFeatures = produce(features, (draft) => {
      draft.file = {
        ...draft.file,
        allowed_file_upload_methods: data.allowed_file_upload_methods,
        number_limits: data.number_limits,
        image: {
          enabled: data.enabled,
          detail: data.image?.detail,
          transfer_methods: data.allowed_file_upload_methods,
          number_limits: data.number_limits,
        },
      }
    })
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }, [featuresStore])

  const handleSubmit = useCallback(() => {
    handleChange({
      ...file,
      allowed_file_upload_methods: uploadMethod,
      number_limits: numberLimits,
      image: {
        detail: resolution,
      },
    })
  }, [file, handleChange, numberLimits, resolution, uploadMethod])

  return (
    <Modal
      isShow
      title={t('appDebug.vision.visionSettings.title')}
      closable
      onClose={onClose}
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={onClose}>{t('common.operation.cancel')}</Button>
          <Button variant='primary' onClick={handleSubmit}>{t('common.operation.confirm')}</Button>
        </>
      }
    >
      <Form form={form} layout='vertical'>
        {/* 分辨率 */}
        <Form.Item
          label={t('appDebug.vision.visionSettings.resolution')}
          tooltip={
            <div className='w-[180px]' >
              {t('appDebug.vision.visionSettings.resolutionTooltip').split('\n').map(item => (
                <div key={item}>{item}</div>
              ))}
            </div>
          }
        >
          <Radio.Group
            className='flex items-center gap-1'
            value={resolution}
            onChange={setResolution}
            size='small'
          >
            <Radio.Button className='grow' value={Resolution.high}>{t('appDebug.vision.visionSettings.high')!}</Radio.Button>
            <Radio.Button className='grow' value={Resolution.low}>{t('appDebug.vision.visionSettings.low')!}</Radio.Button>
          </Radio.Group>
        </Form.Item>
        {/* 上传方式 */}
        <Form.Item label={t('appDebug.vision.visionSettings.uploadMethod')}>
          <div
            className='flex items-center gap-1'
          >
            <Radio.Button
              className='grow'
              size='small'
              checked={!!uploadMethod?.includes(TransferMethod.local_file) && !!uploadMethod?.includes(TransferMethod.remote_url)}
              onChange={() => setUploadMethod([TransferMethod.local_file, TransferMethod.remote_url])}
            >{t('appDebug.vision.visionSettings.both')!}</Radio.Button>
            <Radio.Button
              className='grow'
              size='small'
              checked={!!uploadMethod?.includes(TransferMethod.local_file) && uploadMethod?.length === 1}
              onChange={() => setUploadMethod([TransferMethod.local_file])}
            >{t('appDebug.vision.visionSettings.localUpload')!}</Radio.Button>
            <Radio.Button
              className='grow'
              size='small'
              checked={!!uploadMethod?.includes(TransferMethod.remote_url) && uploadMethod?.length === 1}
              onChange={() => setUploadMethod([TransferMethod.remote_url])}
            >{t('appDebug.vision.visionSettings.url')!}</Radio.Button>
          </div>
        </Form.Item>
        <ParamItem
          id='upload_limit'
          className=''
          name={t('appDebug.vision.visionSettings.uploadLimit')}
          noTooltip
          {...{
            default: 2,
            step: 1,
            min: MIN,
            max: MAX,
          }}
          value={numberLimits}
          enable={true}
          onChange={(_key: string, value: number) => {
            if (!value)
              return
            setNumberLimits(value)
          }}
        />
      </Form>
    </Modal>

  )
}

export default React.memo(ParamConfigContent)
