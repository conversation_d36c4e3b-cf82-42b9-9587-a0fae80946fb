'use client'

import type { ReactNode } from 'react'
import { useTranslation } from 'react-i18next'
import { createRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import type { ReactCropperElement } from './react-cropper'
import './react-cropper/src/index.css'
import ReactCropper from './react-cropper'
import s from './styles/index.module.css'
import type { CropperConfig } from './type'
import type { ImageFile } from '@/types/public/file'
// 公共能力
import cn from '@/utils/classnames'
import { useLocalFileUploader } from '@/app/components/base/image-uploader/hooks'
import { LoadingWhite } from '@/app/components/base/icons/src/public/common'

type CropperProps = {
  // 高度
  height: number
  // 宽度
  width: number
  // 子节点
  children: ReactNode
  // 背景图配置
  config: CropperConfig
  // 样式
  className?: string
  // 浮层样式
  floatingClassName?: string
}

const Cropper = forwardRef(({
  config,
  width,
  height,
  className,
  floatingClassName,
  children,
}: CropperProps, ref) => {
  const { t } = useTranslation()
  const { url, position, fill, zoom } = config

  // 裁剪器ref节点
  const cropperRef = createRef<ReactCropperElement>()
  // 裁剪器状态
  const [loading, setLoading] = useState(false)
  // 填充色
  const [fillColor, setFillColor] = useState(fill)
  // 模糊宽度
  const [vagueWidth, setVagueWidth] = useState(0)
  // 是否开启模糊
  const [startVague, setStartVague] = useState(true)
  // 左侧偏移
  const [leftVagueOffset, setLeftVagueOffset] = useState(0)
  // 右侧偏移
  const [rightVagueOffset, setRightVagueOffset] = useState(0)

  const { handleLocalFileUpload } = useLocalFileUploader({
    limit: 10,
    disabled: false,
    onUpload: (imageFile: ImageFile) => {},
    url: '/images/upload',
  })

  // 计算填充色
  const calculateFillColor = () => {
    // @ts-expect-error 类型正确
    const cavasDom = cropperRef.current?.cropper.getCroppedCanvas()
    if (!cavasDom)
      return
    // @ts-expect-error 类型正确
    const imageData = cropperRef.current?.cropper.getCroppedCanvas().getContext('2d')?.getImageData(0, 0, cavasDom.width, cavasDom.height).data
    if (!imageData)
      return

    const aMap: Record<number, number> = {}
    const bMap: Record<number, number> = {}
    const cMap: Record<number, number> = {}
    let totalCount = cavasDom.width * cavasDom.height
    // 计算颜色频率
    for (let i = 0; i < imageData.length; i += 4) {
      if (imageData[i] || imageData[i + 1] || imageData[i + 2]) {
        aMap[imageData[i] as number] = (aMap[imageData[i] as number] || 0) + 1
        bMap[imageData[i + 1]] = (bMap[imageData[i + 1]] || 0) + 1
        cMap[imageData[i + 2]] = (cMap[imageData[i + 2]] || 0) + 1
      }
      else {
        totalCount = totalCount - 1
      }
    }
    // 找到出现频率最高的颜色
    const aTotal = Object.keys(aMap).reduce((total, item) => total + (aMap[item as unknown as number] * (item as unknown as number)), 0)
    const bTotal = Object.keys(bMap).reduce((total, item) => total + (bMap[item as unknown as number] * (item as unknown as number)), 0)
    const cTotal = Object.keys(cMap).reduce((total, item) => total + (cMap[item as unknown as number] * (item as unknown as number)), 0)

    setFillColor(`rgb(${Math.floor(aTotal / totalCount)}, ${Math.floor(bTotal / totalCount)}, ${Math.floor(cTotal / totalCount)})`)
  }
  // 开始裁剪
  const startCropper = () => {
    setStartVague(false)
  }
  // 结束裁剪
  const finishCropper = () => {
    calculateFillColor()
    // @ts-expect-error 类型正确
    const canvasData = cropperRef.current?.cropper.getCanvasData()
    if (canvasData) {
      setVagueWidth(canvasData?.width / 5)
      setLeftVagueOffset(canvasData.left - (canvasData.width / 10))
      setRightVagueOffset(canvasData.left + canvasData.width - (canvasData.width / 10))
      setStartVague(true)
    }
  }
  // 初始化cropper
  const initCropper = () => {
    // @ts-expect-error 类型正确
    cropperRef.current?.cropper.zoomTo(zoom)
    if (position) {
      // @ts-expect-error 类型正确
      cropperRef.current?.cropper.moveTo(position.x, position.y)
      finishCropper()
    }
  }

  // 处理事件监听
  useEffect(() => {
    // 监听裁剪器移动开始
    cropperRef.current?.addEventListener('cropstart', startCropper)
    // 监听裁剪器移动结束
    cropperRef.current?.addEventListener('cropend', finishCropper)
    // 视口变化
    cropperRef.current?.addEventListener('zoom', finishCropper)
    // 初始化处理
    cropperRef.current?.addEventListener('ready', initCropper)

    return () => {
      cropperRef.current?.removeEventListener('cropstart', startCropper)
      cropperRef.current?.removeEventListener('cropend', finishCropper)
      cropperRef.current?.removeEventListener('zoom', finishCropper)
      cropperRef.current?.removeEventListener('ready', initCropper)
    }
  }, [])

  useImperativeHandle(ref, () => ({
    // 获取裁剪数据
    getCropData: async () => {
      setLoading(true)
      // @ts-expect-error 类型正确
      const data = cropperRef.current?.cropper.getCanvasData()
      // @ts-expect-error 类型正确
      const croppedData = cropperRef.current?.cropper.getCroppedCanvas()
      const croppedConfig = {
        fill: fillColor,
        zoom: data!.height / data!.naturalHeight,
        position: {
          x: data?.left,
          y: data?.top,
          leftPercent: data!.left / width,
          rightPercent: (data!.left + data!.width) / width,
        },
        url: '',
      }
      await new Promise((resolve, reject) => {
        // @ts-expect-error 类型正确
        croppedData?.toBlob((blob) => {
          const file = new File([blob], 'screenshot.jpeg', { type: 'image/jpeg' })
          const reader = new FileReader()
          reader.onload = () => {
            // 获取 Base64 地址
            const base64Url = reader.result as string
            // 这里可以根据需求使用 base64Url
            croppedConfig.url = base64Url
            handleLocalFileUpload(file, (imageFile) => {
              // @ts-expect-error 类型正确
              croppedConfig.id = imageFile.fileId
              setLoading(false)
              resolve(true)
            }, (err) => {
              setLoading(false)
              reject(err)
            })
          }
          reader.onerror = () => {
            setLoading(false)
            reject(new Error('Failed to read file as data URL'))
          }
          reader.readAsDataURL(file)
        })
      })
      if (croppedConfig.url)
        return croppedConfig
    },
  }))

  return (
    <div className={cn(s['cropper-container'], className)}>
      <ReactCropper
        ref={cropperRef}
        disabled={loading}
        style={{ height, width, background: fillColor || 'white' }}
        src={url}
        viewMode={3}
        minCropBoxHeight={height}
        minCropBoxWidth={width}
        background={false}
        responsive={false}
        checkOrientation={false}
        guides={false}
        modal={false}
        center={false}
        highlight={false}
        cropBoxMovable={false}
        cropBoxResizable={false}
        dragMode={'move'}
      />
      {/* 模糊层 */}
      {startVague && <div className={s['cropper-vague']}>
        {/* 左侧模糊 */}
        <div
          className={cn(s['cropper-vague-item'])}
          style={{
            background: `linear-gradient(90deg, ${fillColor} 10%, ${fillColor} 28%, transparent 92.4%)`,
            opacity: 1,
            left: leftVagueOffset,
            width: vagueWidth,
          }}
        ></div>
        {/* 右侧模糊 */}
        <div
          className={cn(s['cropper-vague-item'])}
          style={{
            background: `linear-gradient(90deg, transparent 10%, ${fillColor} 72%, ${fillColor} 92%)`,
            opacity: 1,
            left: rightVagueOffset,
            width: vagueWidth,
          }}
        ></div>
      </div>}
      {/* 浮层 */}
      <div className={cn(s['cropper-floating'], floatingClassName)}>
        {/* 上传状态 */}
        {loading && <div className={s['cropper-loading']}>
          <LoadingWhite className='animate-spin w-3 h-3'></LoadingWhite>
          <span className='text-S1 leading-H1'>{t('common.status.uploading')}</span>
        </div>}
        { children }
      </div>
    </div>
  )
})

Cropper.displayName = 'Cropper'

export default Cropper
