<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.85938 2C2.85938 0.895431 3.75481 0 4.85938 0H19.9686C20.2234 0 20.4686 0.0972653 20.6541 0.271934L28.8306 7.97049C29.0313 8.15946 29.1451 8.4229 29.1451 8.69856V30C29.1451 31.1046 28.2497 32 27.1451 32H4.85938C3.75481 32 2.85938 31.1046 2.85938 30V2Z" fill="url(#paint0_linear_15739_44567)"/>
<mask id="mask0_15739_44567" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="3" y="0" width="27" height="32">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3 2C3 0.895431 3.89543 0 5 0H20.1092C20.364 0 20.6092 0.0972653 20.7947 0.271934L28.9712 7.97049C29.1719 8.15946 29.2857 8.4229 29.2857 8.69856V30C29.2857 31.1046 28.3903 32 27.2857 32H5C3.89543 32 3 31.1046 3 30V2Z" fill="white"/>
</mask>
<g mask="url(#mask0_15739_44567)">
<ellipse opacity="0.12" cx="21.2618" cy="14.5678" rx="16.254" ry="16.0873" fill="url(#paint1_linear_15739_44567)"/>
</g>
<path d="M7 23.53V16H8.846L11.616 18.824L14.384 16H16.23V23.53H14.384V18.664L11.616 21.488L8.846 18.664V23.53H7ZM19.924 16H22.692V19.764H25L21.308 24L17.616 19.764H19.924V16Z" fill="url(#paint2_linear_15739_44567)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.0156 0H20.326C20.5882 0 20.8399 0.102991 21.0269 0.286789L28.8444 7.96988C29.0357 8.15789 29.1435 8.41487 29.1435 8.68309V9.1294H22.0156C20.9111 9.1294 20.0156 8.23397 20.0156 7.1294V0Z" fill="#FFC1B8"/>
<defs>
<linearGradient id="paint0_linear_15739_44567" x1="-3.58143" y1="12.8856" x2="18.0571" y2="36.3934" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA488"/>
<stop offset="1" stop-color="#FF773C"/>
</linearGradient>
<linearGradient id="paint1_linear_15739_44567" x1="4.07643" y1="10.3843" x2="15.6442" y2="30.395" gradientUnits="userSpaceOnUse">
<stop stop-color="#D97436"/>
<stop offset="1" stop-color="#D8D8D8" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint2_linear_15739_44567" x1="8.69989" y1="16" x2="8.69989" y2="22.489" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.96"/>
<stop offset="1" stop-color="white" stop-opacity="0.8"/>
</linearGradient>
</defs>
</svg>
