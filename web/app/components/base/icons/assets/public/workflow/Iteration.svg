<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 1940685856">
<rect id="Rectangle 1312321387" width="20" height="20" rx="3" fill="#EE704E"/>
<g id="Mask group" filter="url(#filter0_d_7972_22283)">
<mask id="mask0_7972_22283" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="4" width="12" height="12">
<rect id="Rectangle 1312321435" x="4.6" y="4.6" width="10.8" height="10.8" fill="#D9D9D9" stroke="white" stroke-width="1.2"/>
</mask>
<g mask="url(#mask0_7972_22283)">
<g id="Group 1940684761">
<g id="Group 1940684762">
<path id="Rectangle 1312321436" d="M5.22299 8.97057C4.68755 8.67048 4.68755 7.89983 5.22299 7.59974L9.61587 5.1378C9.8545 5.00406 10.1455 5.00406 10.3841 5.1378L14.777 7.59974C15.3125 7.89983 15.3125 8.67048 14.777 8.97057L10.3841 11.4325C10.1455 11.5662 9.8545 11.5662 9.61587 11.4325L5.22299 8.97057Z" stroke="white" stroke-width="1.2" stroke-linecap="round"/>
<path id="Rectangle 1312321437" d="M5.22299 12.3992C4.68755 12.0991 4.68755 11.3284 5.22299 11.0283L9.61587 8.56639C9.8545 8.43265 10.1455 8.43265 10.3841 8.56639L14.777 11.0283C15.3125 11.3284 15.3125 12.0991 14.777 12.3992L10.3841 14.8611C10.1455 14.9948 9.8545 14.9948 9.61587 14.8611L5.22299 12.3992Z" stroke="white" stroke-width="1.2" stroke-linecap="round"/>
</g>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_7972_22283" x="2.21875" y="4.4375" width="15.5625" height="15.1239" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.933333 0 0 0 0 0.439216 0 0 0 0 0.305882 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7972_22283"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7972_22283" result="shape"/>
</filter>
</defs>
</svg>
