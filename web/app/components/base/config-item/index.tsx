'use client'
import type { FC } from 'react'
import React from 'react'
import { Input } from 'antd'
import { useTranslation } from 'react-i18next'
import { ReactSortable } from 'react-sortablejs'

import TextButton from '@/app/components/base/button/text-button'
import { Add, Draggable } from '@/app/components/base/icons/src/vender/line/action'
import { Delete } from '@/app/components/base/icons/src/vender/line/general'

export type Options = string[]
export type IConfigSelectProps = {
  options: Options
  onChange: (options: Options) => void
}

const ConfigSelect: FC<IConfigSelectProps> = ({
  options,
  onChange,
}) => {
  const { t } = useTranslation()

  const optionList = options.map((content, index) => {
    return ({
      id: index,
      name: content,
    })
  })

  return (
    <div>
      {options.length > 0 && (
        <div className='mb-1'>
          <ReactSortable
            className="space-y-1"
            list={optionList}
            setList={list => onChange(list.map(item => item.name))}
            handle='.handle'
            ghostClass="opacity-50"
            animation={150}
          >
            {options.map((o, index) => (
              <div className={'flex gap-1 items-center'} key={index}>
                <TextButton className='handle w-6 h-6 !cursor-grab' variant='hover'>
                  <Draggable className='w-4 h-4' />
                </TextButton>
                <Input
                  key={index}
                  type="input"
                  value={o || ''}
                  onChange={(e) => {
                    const value = e.target.value
                    onChange(options.map((item, i) => {
                      if (index === i)
                        return value

                      return item
                    }))
                  }}
                  suffix={(
                    <TextButton variant='text' onClick={() => {
                      onChange(options.filter((_, i) => index !== i))
                    }}>
                      <Delete className='w-4 h-4' />
                    </TextButton>
                  )}
                />
              </div>
            ))}
          </ReactSortable>
        </div>
      )}

      <div
        onClick={() => { onChange([...options, '']) }}
        className='flex items-center h-9 px-3 gap-2 rounded cursor-pointer text-gray-G1 border border-gray-G5'>
        <Add className='w-4 h-4'></Add>
        <div className='text-gray-G1'>{t('appDebug.variableConfig.addOption')}</div>
      </div>
    </div>
  )
}

export default React.memo(ConfigSelect)
