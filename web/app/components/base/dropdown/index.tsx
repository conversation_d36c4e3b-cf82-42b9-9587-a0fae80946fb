import type { FC } from 'react'
import { useMemo, useState } from 'react'
import { Dropdown as ADropdown } from 'antd'
import type { MenuItemType } from 'antd/es/menu/interface'
import {
  RiMoreFill,
} from '@remixicon/react'

export type Item = {
  value: string
  text: string | JSX.Element
}
type DropdownProps = {
  items: Item[]
  secondItems?: Item[]
  onSelect: (item: Item) => void
  renderTrigger?: (open: boolean) => React.ReactNode
  children?: React.ReactNode
  placement?: 'bottom' | 'topLeft' | 'topCenter' | 'topRight' | 'bottomLeft' | 'bottomCenter' | 'bottomRight' | 'top' | undefined
}
const Dropdown: FC<DropdownProps> = ({
  items,
  onSelect,
  secondItems,
  renderTrigger,
  children,
  placement = 'bottom',
}) => {
  // 下拉菜单是否展开
  const [open, setOpen] = useState(false)
  // 菜单信息
  const menuItems: MenuItemType[] = useMemo(() => {
    return [...items, ...(secondItems || [])].map((item) => {
      return {
        label: item.text,
        tittle: item.text,
        key: item.value,
      }
    })
  }, [items, secondItems])

  const handleSelect = (key: string) => {
    setOpen(false)
    const info = menuItems.find(item => item?.key === key)
    onSelect({
      text: info?.label as string,
      value: info?.key as string,
    })
  }

  return (
    <ADropdown
      open={open}
      trigger={['click']}
      placement={placement}
      onOpenChange={setOpen}
      menu={{
        items: menuItems,
        onClick: ({ key }) => { handleSelect(key) },
      }}
    >
      { renderTrigger
        ? renderTrigger(open)
        : (children || (
          <RiMoreFill className='w-4 h-4 text-gray-G2 cursor-pointer shrink-0' />
        ))}
    </ADropdown>
  )
}

export default Dropdown
