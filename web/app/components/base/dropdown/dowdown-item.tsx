'use client'
import itemStyle from './styles/item.module.css'

import cn from '@/utils/classnames'

type DropdownItemProps = {
  title: string | React.ReactNode
  icon?: React.ReactNode
  type?: 'normal' | 'warn' | 'primary' | 'disabled'
  className?: string
  showBg?: boolean
  onClick: () => void
}

const DropdownItem = ({ title, type = 'normal', showBg = false, icon, className, onClick }: DropdownItemProps) => {
  return (
    <div
      className={cn(
        itemStyle['dropdown-item'],
        type === 'warn' && itemStyle['warn-dropdown-item'],
        type === 'primary' && itemStyle['primary-dropdown-item'],
        type === 'disabled' && itemStyle['disabled-dropdown-item'],
        showBg && itemStyle['show-bg'],
        className,
      )}
      onClick={onClick}
    >
      {icon}
      <div className={itemStyle['dropdown-item-name']}>{title}</div>
    </div>
  )
}

export default DropdownItem
