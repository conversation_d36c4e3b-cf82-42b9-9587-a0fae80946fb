import { useCallback, useEffect, useState } from 'react'
import { t } from 'i18next'
import copy from 'copy-to-clipboard'
import { Tooltip } from 'antd'
import { Clipboard } from '../icons/src/vender/line/action'
import cn from '@/utils/classnames'

type CopyBtnProps = {
  value?: string
  className?: string
  wrapClassName?: string
  onCopy?: () => void
}

const CopyBtn = ({
  value,
  className,
  wrapClassName,
  onCopy,
}: CopyBtnProps) => {
  // 当前内容是否被拷贝
  const [isCopied, setIsCopied] = useState(false)

  // 拷贝动作
  const copyHandle = useCallback(() => {
    if (onCopy)
      onCopy()

    else
      copy(value?.trim() as string)

    setIsCopied(true)
  }, [onCopy, value])

  // 清理拷贝状态
  useEffect(() => {
    if (isCopied) {
      const timeout = setTimeout(() => {
        setIsCopied(false)
      }, 1000)

      return () => {
        clearTimeout(timeout)
      }
    }
  }, [isCopied])

  return (
    <Tooltip title={isCopied ? `${t('appApi.copied')}` : `${t('appApi.copy')}`}>
      <div className={cn('w-4 h-4 text-gray-G3 hover:text-gray-G1 cursor-pointer', !isCopied && 'cursor-pointer', wrapClassName)}>
        <Clipboard className={cn('w-4 h-4', className)} onClick={isCopied ? () => { } : copyHandle} />
      </div>
    </Tooltip>
  )
}

export default CopyBtn
