import type { CSSProperties } from 'react'
import React, { useEffect, useState } from 'react'
import { type VariantProps, cva } from 'class-variance-authority'
import { LoadingOutlined } from '@ant-design/icons'
import classNames from '@/utils/classnames'

const buttonVariants = cva(
  'btn disabled:btn-disabled loading:btn-loading',
  {
    variants: {
      variant: {
        'primary': 'btn-primary', // 主要按钮
        'warning': 'btn-warning',
        'secondary': 'btn-secondary', // 次增按钮
        'secondary-accent': 'btn-secondary-accent', // 次要按钮
        'ghost': 'btn-ghost',
        'ghost-accent': 'btn-ghost-accent',
        'tertiary': 'btn-tertiary',
        'info': 'btn-info',
        'gray': 'btn-gray',
      },
      size: {
        small: 'btn-small',
        medium: 'btn-medium',
        large: 'btn-large',
      },
    },
    defaultVariants: {
      variant: 'secondary',
      size: 'large',
    },
  },
)

export type ButtonProps = {
  destructive?: boolean
  loading?: boolean
  styleCss?: CSSProperties
} & React.ButtonHTMLAttributes<HTMLButtonElement> & VariantProps<typeof buttonVariants>

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, destructive, loading, styleCss, children, disabled, onClick, ...props }, ref) => {
    // 是否正在加载
    const [currentLoading, setCurrentLoading] = useState(loading)
    // 点击按钮事件
    const handleClick = async (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
      setCurrentLoading(true)
      try {
        onClick && await onClick(event)
      }
      finally {
        setCurrentLoading(false)
      }
    }

    useEffect(() => {
      setCurrentLoading(loading)
    }, [loading])

    return (
      <button
        type='button'
        className={classNames(
          buttonVariants({ variant, size, className }),
          destructive && 'btn-destructive',
          currentLoading && 'btn-loading',
        )}
        ref={ref}
        style={styleCss}
        onClick={handleClick}
        disabled={disabled}
        {...props}
      >
        {currentLoading && <LoadingOutlined className='mr-1.5'/>}
        {children}
      </button>
    )
  },
)
Button.displayName = 'Button'

export default Button
export { Button, buttonVariants }
