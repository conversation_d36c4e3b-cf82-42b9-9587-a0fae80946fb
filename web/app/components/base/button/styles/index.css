@tailwind components;

@layer components {
  .btn {
    @apply inline-flex justify-center items-center cursor-pointer whitespace-nowrap;
  }
  .btn-disabled {
    @apply cursor-not-allowed;
  }
  /* 尺寸 */
  .btn-small {
    @apply px-4 h-7 text-S1 leading-H1;
    border-radius: 2px;
  }
  .btn-medium {
    @apply px-4 h-8 rounded leading-H3 font-semibold text-S3;
  }
  .btn-large {
    @apply px-4 h-9 rounded leading-H3 font-normal text-S3 min-w-[92px];
  }

  /* 颜色 */
  .btn-primary {
    @apply
    bg-primary-P1
    border-primary-P1
    hover:bg-primary-P2
    hover:border-primary-P2
    text-white;
  }
  .btn-primary.btn-disabled {
    @apply 
    shadow-none
    !border-primary-P3
    !bg-primary-P3;
  }
  .btn-primary.btn-loading {
    @apply bg-primary-P2 cursor-not-allowed;
  }

  .btn-secondary {
    @apply 
    border
    bg-white
    border-primary-P1 
    hover:bg-primary-P4 
    hover:border-primary-P4 
    text-primary-P1
    hover:text-primary-P1;
  }
  .btn-secondary.btn-disabled {
    @apply 
    bg-white 
    border-primary-P4
    text-primary-P4;
  }

  .btn-secondary-accent {
    @apply 
    border
    bg-white 
    border-gray-G4
    hover:border-primary-P1 
    text-gray-G2
    hover:text-primary-P1;
  }
  .btn-secondary-accent.btn-disabled {
    @apply 
    border-gray-G5 
    text-gray-G4;
  }

  .btn-ghost {
    @apply hover:bg-gray-G5 text-gray-G2;
  }
  .btn-ghost.btn-disabled {
    @apply text-gray-G4;
  }

  .btn-warning {
    border-color: rgba(255, 87, 51, 0.50);
    border: 1px solid rgba(255, 87, 51, 0.50);
    color: #FF5733;
  }

  .btn-info {
    @apply bg-white border border-gray-G5 hover:border-primary-P1 text-gray-G2;
  }

  .btn-gray {
    @apply bg-gray-G6 hover:text-gray-G1 text-gray-G2;
  }
  
  .btn-tertiary {
    @apply 
    bg-components-button-tertiary-bg 
    hover:bg-components-button-tertiary-bg-hover 
    text-components-button-tertiary-text;
  }

  .btn-tertiary.btn-disabled {
    @apply 
    bg-components-button-tertiary-bg-disabled 
    text-components-button-tertiary-text-disabled;
  }


  .btn-ghost-accent {
    @apply 
    hover:bg-state-accent-hover
    text-gray-G2;
  }

  .btn-ghost-accent.btn-disabled {
    @apply 
    text-gray-G4;
  }
}