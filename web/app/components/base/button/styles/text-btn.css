@tailwind components;

@layer components {
  .text-btn {
    @apply p-0 !border-0 gap-1;
    height: unset;
  }
  .text-btn-text {
    @apply !text-gray-G2 hover:!text-gray-G1;
  }
  .text-btn-accent {
    @apply !text-gray-G2 hover:!text-primary-P1;
  }
  .text-btn-accent-deep {
    @apply !text-gray-G1 hover:!text-primary-P1;
  }
  .text-btn-accent.text-btn-disabled, .text-btn-accent-deep.text-btn-disabled  {
    @apply !text-gray-G4 hover:!text-gray-G4;
  }
  .text-btn-primary {
    @apply !text-primary-P1 hover:!text-primary-P2;
  }
  .text-btn-primary.text-btn-disabled {
    @apply !text-primary-P3 hover:!text-primary-P3;
  }
  .text-btn-icon {
    @apply flex items-center w-6 h-6 rounded !text-gray-G2;
  }
  .text-btn-icon.text-btn-disabled {
    @apply hover:!bg-transparent !text-gray-G4 !cursor-not-allowed;
  }
  .text-btn-icon-deep {
    @apply flex items-center w-6 h-6 rounded !text-gray-G1;
  }
  .text-btn-icon-deep.text-btn-disabled {
    @apply hover:!bg-transparent !text-gray-G4 !cursor-not-allowed;
  }
  .text-btn-icon:hover, .text-btn-icon-deep:hover {
    background-color: #C8CEDA33 !important;
  }
  .text-btn-hover.text-btn-icon, .text-btn-hover.text-btn-icon-deep  {
    background-color: #C8CEDA33 !important;
  }
  .text-btn-clear {
    color: rgba(0,0,0,0.25) !important
  }
  .text-btn-clear:hover {
    color: rgba(0,0,0,0.45) !important
  }

  .text-btn-small {
    @apply !text-S1 !leading-H1;
  }
  .text-btn-middle {
    @apply !text-S3 !leading-H3;
  }
}