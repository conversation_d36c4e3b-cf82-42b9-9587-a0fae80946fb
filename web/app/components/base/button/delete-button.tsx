'use client'
import type { <PERSON> } from 'react'
import React from 'react'
import TextButton from './text-button'
import Button from '.'
import { Delete } from '@/app/components/base/icons/src/vender/line/general'

type Props = {
  className?: string
  onClick: (e: React.MouseEvent) => void
  variant?: 'primary' | 'text' | 'hover'
  type?: 'button' | 'text'
  text?: string
}

const AddButton: FC<Props> = ({
  className,
  onClick,
  variant = 'hover',
  type = 'text',
  text,
}) => {
  return (
    type === 'text'
      ? <TextButton className={className} variant={variant} onClick={onClick}>
        <Delete className='w-4 h-4' />
      </TextButton>
      : <Button
        variant={'secondary-accent'}
        className={className}
        onClick={onClick}
      >
        <Delete className='w-4 h-4 mr-1' />
        {text}
      </Button>
  )
}
export default React.memo(AddButton)
