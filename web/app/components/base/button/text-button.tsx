import type { ButtonProps } from 'antd'
import { Button } from 'antd'
import type { VariantProps } from 'class-variance-authority'
import { cva } from 'class-variance-authority'
import './styles/text-btn.css'

const textButtonVariants = cva(
  'text-btn disabled:text-btn-disabled',
  {
    variants: {
      variant: {
        'primary': 'text-btn-primary',
        'text': 'text-btn-text',
        'hover': 'text-btn-accent',
        'icon': 'text-btn-icon',
        'icon-deep': 'text-btn-icon-deep',
        'hover-deep': 'text-btn-accent-deep',
        'clear': 'text-btn-clear',
      },
      size: {
        small: 'text-btn-small',
        middle: 'text-btn-middle',
        large: 'text-btn-large',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'small',
    },
  },
)

export type TextButtonType = Omit<ButtonProps, 'variant'> & {
  variant?: 'primary' | 'text' | 'hover' | 'icon' | 'icon-deep' | 'hover-deep' | 'clear'
  hover?: boolean
} & VariantProps<typeof textButtonVariants>

const TextButton = (props: TextButtonType) => {
  return (
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    <Button
      {...props}
      type='link'
      className={
        `${textButtonVariants({ variant: props.variant, size: props.size, className: props.className })} ${props.hover && 'text-btn-hover'})`
      }
    >{props.children}</Button>
  )
}

export default TextButton
