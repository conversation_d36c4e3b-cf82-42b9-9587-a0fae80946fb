'use client'
import type { <PERSON> } from 'react'
import React from 'react'
import type { TextButtonType } from './text-button'
import TextButton from './text-button'
import type { ButtonProps } from '.'
import Button from '.'
import { Add } from '@/app/components/base/icons/src/vender/line/action'

const AddButton: FC<ButtonProps> = React.memo(({ children, ...rest }) => {
  return (
    <Button {...rest}>
      <Add className='w-4 h-4 mr-1' />
      {children}
    </Button>
  )
})
AddButton.displayName = 'AddButton'
const AddTextButton: FC<TextButtonType> = React.memo(({ type = 'text', ...rest }) => {
  return (
    <TextButton type={type} {...rest}>
      <Add className='w-4 h-4' />
    </TextButton>
  )
})
AddTextButton.displayName = 'AddTextButton'
export {
  AddTextButton,
  AddButton,
}
