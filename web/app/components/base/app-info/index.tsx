import React from 'react'
import s from './styles/index.module.css'
import type { MediaType } from '@/types/videos'
import type { AppMode } from '@/types/app'
// 公共组件
import AppAvatar from '@/app/components/app/common/avatar'
import Tooltip from '@/app/components/base/tooltip'
import { InfoCircle } from '@/app/components/base/icons/src/vender/line/tip'

export type IAppBasicProps = {
  name: string
  type: string | React.ReactNode
  url?: string
  tooltip?: string
  mode: AppMode | 'datasets' | MediaType
}

export default function AppBasic({ name, type, url, tooltip, mode }: IAppBasicProps) {
  return (
    <div className={s['app-basic']}>
      <AppAvatar className={s['app-icon']} url={url} appMode={mode} size={36}/>
      <div className={s['app-info']} style={{ width: 'calc(100% - 80px)' }}>
        <div className={s['app-title']}>
          {name}
          { tooltip && <Tooltip
            popupContent={tooltip}
          >
            <InfoCircle className='w-4 h-4 text-gray-G3'></InfoCircle>
          </Tooltip> }
        </div>
        <div className={s['app-description']}>{type}</div>
      </div>
    </div>
  )
}
