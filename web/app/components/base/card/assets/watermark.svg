<svg width="112" height="100" viewBox="0 0 112 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 1321322997">
<mask id="mask0_87_53975" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="112" height="100">
<g id="&#231;&#159;&#169;&#229;&#189;&#162;&#229;&#164;&#135;&#228;&#187;&#189; 28">
<rect width="112" height="100" rx="2" fill="white"/>
<rect width="112" height="100" rx="2" fill="url(#paint0_linear_87_53975)" fill-opacity="0.5"/>
</g>
</mask>
<g mask="url(#mask0_87_53975)">
<g id="Group">
<g id="Union" filter="url(#filter0_d_87_53975)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M61.5984 22.2C55.7984 22.2 50.5984 26.7 46.6984 34.1C46.0984 35.3 45.4984 36.6 44.8984 38C41.7984 38.5 38.7984 39.1 35.8984 39.8C36.1651 38.8 36.4318 37.8889 36.6984 36.9778L36.6985 36.9776C36.8318 36.5221 36.9651 36.0666 37.0984 35.6C42.0984 19 51.2984 8 61.6984 8C72.0984 8 81.1984 19.1 86.0984 35.7C82.9984 35.1 79.7984 34.6 76.4984 34.2C72.6984 26.7 67.3984 22.2 61.5984 22.2ZM61.7 108.7C67.4 108.7 72.7 104.1 76.6 96.7C77.3 95.5 77.9 94.2 78.5 92.9C81.6 92.4 84.6 91.8 87.5 91.1C87.2 92.5 86.8 93.9 86.4 95.2C81.3 111.8 72.2 122.9 61.8 122.9C51.4 122.9 42.2 111.8 37.2 95.3C36.8667 94.3667 36.6222 93.4334 36.3778 92.5001L36.3778 92.5C36.2556 92.0333 36.1333 91.5667 36 91.1C38.9 91.8 41.9 92.4 45 92.9C45.6 94.3 46.2 95.6 46.8 96.8C50.7 104.2 55.9 108.7 61.7 108.7ZM89.8 77.1C90.2 73.3 90.4 69.4 90.4 65.4C90.4 61.4 90.2 57.5 89.8 53.7C87.5 52.5 84.9 51.5 82 50.6C82.8 55.2 83.3 60.2 83.3 65.4C83.3 70.6 82.9 75.6 82 80.2C81.7 81.8 81.4 83.3 81 84.7C83.9 84 86.6 83.1 89.1 82.1C89.4 80.4 89.6 78.8 89.8 77.1ZM32.8984 65.4C32.8984 61.4 33.0984 57.4 33.4984 53.6C33.5651 53.0334 33.6318 52.4778 33.6984 51.9223C33.8224 50.8889 33.9465 49.8554 34.0705 48.7505C32.3231 49.4383 30.7639 50.219 29.3 50.9998C22.4 54.7998 18.2 59.7998 18.2 65.3998C18.2 70.9998 22.4 75.9998 29.2 79.7998C30.6995 80.5995 32.3989 81.3993 34.0983 82.0991C33.9984 81.2994 33.8984 80.4747 33.7984 79.65C33.6984 78.825 33.5984 78 33.4984 77.2C33.0984 73.4 32.8984 69.5 32.8984 65.4ZM42.1983 84.6994C41.7983 83.2996 41.4984 81.7998 41.1984 80.2C40.3984 75.6 39.8984 70.6 39.8984 65.3C39.8984 60.1 40.3984 55.1 41.1984 50.4C41.4825 48.98 41.7665 47.5599 42.1353 46.1398C42.1901 46.1264 42.245 46.1131 42.3 46.0998C43.8 45.6998 45.4 45.3998 47.1 45.0998C51.7 44.2998 56.6 43.8998 61.7 43.8998C66.8 43.8998 71.7 44.2998 76.3 45.0998C75.7 42.3998 75.1 39.7998 74.3 37.4998C70.2 37.0998 66 36.7998 61.7 36.7998C57.4 36.7998 53.2 36.9998 49.1 37.4998C48.4 37.5998 47.725 37.6748 47.05 37.7498C46.375 37.8248 45.7 37.8998 45 37.9998C41.9 38.4998 38.9 39.0998 36 39.7998C34.4 40.1998 32.9 40.5998 31.4 41.0998C14.9 46.0998 4 55.0998 4 65.3998C4 75.6998 14.9 84.6998 31.4 89.7998C32.9 90.2998 34.4 90.6998 36 91.0998C38.9 91.7998 41.9 92.3998 45 92.8998C46.3 93.0998 47.7 93.2998 49.1 93.3998C53.2 93.7998 57.4 94.0998 61.7 94.0998C66 94.0998 70.2 93.8998 74.3 93.3998C74.7667 93.3331 75.2334 93.2776 75.7 93.222C76.6334 93.1109 77.5667 92.9998 78.5 92.7998C81.6 92.2998 84.6 91.6998 87.5 90.9998C89 90.5998 90.5 90.1998 91.9 89.7998C108.3 84.7998 119.3 75.6998 119.3 65.3998C119.3 55.0998 108.3 46.0998 91.8 40.9998C92.7 44.1998 93.4 47.4998 93.9 50.9998C100.8 54.7998 105 59.8998 105 65.3998C105 70.8998 100.8 75.9998 93.9 79.7998C92.4 80.5998 90.8 81.3998 89.2 82.0998C86.7 83.0998 84 83.9998 81.1 84.6998C79.5 85.0998 77.9 85.3998 76.2 85.6998C71.6 86.4998 66.7 86.8998 61.6 86.8998C56.5 86.8998 51.6 86.4998 47 85.6998C45.4 85.3998 43.8 85.0998 42.2 84.6998C42.1994 84.6997 42.1988 84.6995 42.1983 84.6994ZM89.1008 48.7C88.6008 45.6 88.0008 42.6 87.3008 39.8C84.4008 39.1 81.4008 38.5 78.3008 38C79.3008 40.5 80.2008 43.2 80.9008 46.1C83.9008 46.8 86.6008 47.7 89.1008 48.7Z" fill="url(#paint1_linear_87_53975)" fill-opacity="0.5" shape-rendering="crispEdges"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_87_53975" x="-4" y="-2" width="131.3" height="130.9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.192157 0 0 0 0 0.407843 0 0 0 0 0.960784 0 0 0 0.02 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_87_53975"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_87_53975" result="shape"/>
</filter>
<linearGradient id="paint0_linear_87_53975" x1="134.065" y1="113.281" x2="49.1522" y2="-13.2812" gradientUnits="userSpaceOnUse">
<stop stop-color="#1677FF" stop-opacity="0.3"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_87_53975" x1="100.5" y1="99" x2="33.5001" y2="32.4999" gradientUnits="userSpaceOnUse">
<stop stop-color="#B0D9FF" stop-opacity="0.3"/>
<stop offset="1" stop-color="#D4F0FF" stop-opacity="0.2"/>
</linearGradient>
</defs>
</svg>
