
  /* 应用卡片样式 */
  .card {
    @apply relative col-span-1 rounded flex flex-col cursor-pointer;
    border-radius: 4px;
    border: 1.5px solid #FFF;
    background: #FFF;
    padding: 16px;
  }
  .card:hover, .activeCard {
    border: 1.5px solid #4086ff;
    box-shadow: 0px 3px 8px 0px rgba(159, 176, 201, 0.50);
    background: #FFF;
  }
  .cardTag {
    @apply w-[74px] h-[20px] absolute flex items-center;
    right: 0px;
    top: 0px;
    background-image: url("./assets/label.svg");
    font-size: 0.625rem !important;
    color: #6E8BD5;
    padding: 0px 6px 0px 12px;
  }
  .cardContent {
    @apply grow text-S1 text-gray-G2 leading-H1;
    font-weight: 400;
    word-break: break-all;
    margin-top: 8px;
  }
  .cardHeader {
    @apply flex items-center grow-0 shrink-0 gap-3;
  }
  .cardFooter {
    @apply flex absolute bottom-[20px];
    visibility: hidden;
    align-items: center;
    justify-content: space-between;
    width: calc(100% - 40px);
  }

  .watermark {
    position: absolute;
    right: 0;
    bottom: 0;
    background-image: url("./assets/watermark.svg");
    width: 112px;
    height: 100px;
  }
  .tagIcon {
    background-image: url("./assets/application.svg");
    height: 12px;
    width: 12px;
    margin-right: 4px;
  }
  .headIcon {
    @apply relative shrink-0 min-w-12 min-h-12 flex items-center justify-center;
  }
  .headTitle {
    @apply truncate leading-H4 text-S4 text-gray-G1;
    font-weight: 600;
  }
  .appInfo {
    @apply w-full shrink overflow-hidden flex flex-col justify-between h-full;
  }

  .colCard {
    @apply h-[189px] p-4;
    box-shadow: 0px 1px 1px 0px rgba(159, 176, 201, 0.20);
  }
  .lineCard {
    @apply px-6 py-[19px];
  }
  .lineDescription {
    @apply text-S1 text-gray-G2 leading-H1 shrink-0;
    font-weight: 400;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }