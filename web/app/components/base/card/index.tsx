import React, { useCallback, useLayoutEffect, useState } from 'react'
import type { MouseEvent, ReactNode } from 'react'

import { debounce } from 'lodash-es'
import { createContext, useContext } from 'use-context-selector'
import s from './index.module.css'
import cn from '@/utils/classnames'

type CardContextType = {
  isActive: boolean
  setIsActive: (value: boolean) => void
}
const CardContext = createContext<CardContextType>({
  isActive: false,
  setIsActive: (value) => { },
})
const CardProvider = ({ isActive, setIsActive, children }: {
  children: React.ReactNode
} & CardContextType) => (
  <CardContext.Provider value={{
    isActive,
    setIsActive,
  }}>
    {children}
  </CardContext.Provider>
)
export const useCardContext = () => useContext(CardContext)

export type CardProps = {
  onClick?: () => void
  generateTag?: () => ReactNode
  generateHead?: () => ReactNode
  generateExtra?: () => ReactNode
  title: string | (() => ReactNode)
  description: string | (() => ReactNode)
  descriptionLine?: number
  children?: ReactNode
  className?: string
  replaceClassName?: string
  footerClassName?: string
  layout?: 'col' | 'line'
}

const Card = ({
  onClick,
  generateTag,
  generateHead,
  generateExtra,
  title,
  description,
  descriptionLine,
  replaceClassName,
  footerClassName,
  className,
  children,
  layout = 'col',
}: CardProps) => {
  // 描述文本宽度
  const [descriptionConfig, setDescriptionConfig] = useState({
    width: 0,
    font: '14px',
  })
  const [isActive, setIsActive] = useState(false)

  // 点击底部功能区
  const onClickFooter = (e: MouseEvent) => {
    e.stopPropagation()
    setIsActive(true)
  }
  // 获取卡片描述card
  const getDescriptionContent = useCallback((width: number, text?: string) => {
    const {
      font,
    } = descriptionConfig
    function getCollapseText() {
      if (!text)
        return ''
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      let truncatedText = ''
      if (context) {
        context.font = font
        let canvasText = ''
        for (let i = 0; i < text.length; i++) {
          canvasText += text[i]
          if (context.measureText(canvasText).width > width) {
            truncatedText = `${text.slice(0, i)}...`
            break
          }
        }
        if (!truncatedText)
          truncatedText = text
      }
      return truncatedText
    }
    return getCollapseText()
  }, [descriptionConfig])

  // 监听窗口变化后的带下
  useLayoutEffect(() => {
    const computedStyle = debounce(() => {
      const target = document.querySelector('.title-content')
      const font = getComputedStyle(target!).font
      const width = parseInt((target as any).offsetWidth)
      setDescriptionConfig({
        width: width * (descriptionLine || 1.8),
        font,
      })
    })
    if (description && typeof description === 'string' && layout === 'col') {
      computedStyle()
      window.addEventListener('resize', computedStyle)
      return () => window.removeEventListener('resize', computedStyle)
    }
  }, [description, descriptionLine, layout])

  return (
    <CardProvider isActive={isActive} setIsActive={setIsActive}>
      <div
        onClick={(e) => {
          e.preventDefault()
          // 点击
          onClick && onClick()
        }}
        onMouseLeave={(e) => {
          setIsActive(false)
        }}
        className={
          replaceClassName || cn(
            'group',
            s.card,
            className,
            isActive && s.activeCard,
            layout === 'col' ? s.colCard : s.lineCard,
          )}
      >
        {
          layout === 'col' && (
            <>
              {generateTag && (
                <div className={cn(s.cardTag)}>
                  {generateTag()}
                </div>
              )}
              {/* 卡片头部 */}
              <div className={s.cardHeader}>
                {generateHead && <div className={s.headIcon}>
                  {generateHead()}
                </div>}
                <div className='grow w-0'>
                  {
                    (
                      typeof title === 'string'
                        ? <div className={s.headTitle} title={title}>{title}</div>
                        : title()
                    )
                  }
                </div>
              </div>
              {/* 详细信息 */}
              {description && (typeof description === 'string'
                ? <div
                  className={cn(s.cardContent, 'title-content')}
                  title={description}
                >
                  <span className='group-hover:!block hidden'>{getDescriptionContent(descriptionConfig.width, description)}</span>
                  <span className='group-hover:!hidden block'>{getDescriptionContent(descriptionConfig.width, description)}</span>
                </div>
                : <div className={s.cardContent}>{description()}</div>
              )
              }
              {/* 底部栏 */}
              <div onClick={onClickFooter} className={cn(s.cardFooter, footerClassName, 'group-hover:!visible', isActive && '!visible')}>
                {children}
              </div>
            </>
          )
        }
        {
          layout === 'line' && (
            <>
              <div className={s.cardHeader}>
                {/* 头像 */}
                {generateHead && <div className={s.headIcon}>
                  {generateHead()}
                </div>}
                {/* 应用信息 */}
                <div className={s.appInfo}>
                  {/* 标题及野禽 */}
                  <div className='flex items-center gap-2'>
                    {
                      (
                        typeof title === 'string'
                          ? <div className={s.headTitle} title={title}>{title}</div>
                          : title()
                      )
                    }
                    {generateTag && generateTag()}
                  </div>
                  {/* 介绍信息 */}
                  {(typeof description === 'string'
                    ? <div
                      className={cn(s.lineDescription)}
                      title={description}
                    >
                      {description}
                    </div>
                    : <div className={s.lineDescription}>{description()}</div>
                  )
                  }
                </div>
                {/* 自定义功能 */}
                <div className='shrink-0'>
                  {children}
                </div>
              </div>
              {/* 额外部分 */}
              {
                generateExtra ? generateExtra() : <></>
              }
            </>
          )
        }
      </div>
    </CardProvider>
  )
}

export default Card
