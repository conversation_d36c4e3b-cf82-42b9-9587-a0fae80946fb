import { Switch } from 'antd'
import cn from '@/utils/classnames'

type Props = {
  className?: string
  title: string
  operations?: React.ReactNode
  enabled?: boolean
  onSwitch?: (enabled: boolean) => void
  disabled?: boolean
  children?: React.ReactNode
  autoFold?: boolean // 是否支持折叠
}
const ConfigCard = ({
  className,
  title,
  operations,
  enabled,
  onSwitch,
  disabled = false,
  children,
  autoFold,
}: Props) => {
  return (
    <div className={cn('flex flex-col gap-2 border border-gray-G6 rounded px-3 py-2', className)}>
      <div className='flex justify-between items-center'>
        <div className='title-14-24'>{title}</div>
        {(!operations && onSwitch)
          ? (<Switch
            value={enabled}
            onChange={onSwitch}
            size='small'
            disabled={disabled}
          />)
          : operations}
      </div>
      <div className='flex flex-col gap-2'>
        { autoFold ? (enabled && children) : children }
      </div>
    </div>
  )
}

export default ConfigCard
