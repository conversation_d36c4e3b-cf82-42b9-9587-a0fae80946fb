import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useMount } from 'ahooks'
import { clickExperience } from '../utils'
import s from './styles/index.module.css'
import cn from '@/utils/classnames'
import { fetchMarketAppspublic } from '@/service/market'
import { formatNumber2 } from '@/utils/format'
/* 公共组件 */
import Loading from '@/app/components/base/loading'
import Button from '@/app/components/base/button'
import AvatarList from '@/app/components/base/avatar/avatar-list'
import { ShareIcon } from '@/app/components/base/icons/src/public/common'
import { AppMarketFeature, AppMarketSort } from '@/types/app-market'

type BannerProps = {
  className: string
  id?: string
}

const Banner = ({ className, id }: BannerProps) => {
  const { t } = useTranslation()
  // banner图列表
  const [bannerList, setBannerList] = useState<Array<any>>([])
  // 当前激活的banner索引
  const [activeIndex, setActiveIndex] = useState(0)
  // 是否正在加载
  const [loading, setLoading] = useState(true)

  // 获取banner图列表
  const getBannerList = () => {
    fetchMarketAppspublic({
      page: 1,
      limit: 3,
      featured: AppMarketFeature.Official,
      sort_by: AppMarketSort.HOT,
    }).then((res) => {
      setBannerList(res.data.map((item) => {
        return {
          name: item.name,
          description: item.description,
          number: formatNumber2(Number(item.usages)),
          avatarList: [
            '/assets/avatar/app2.svg',
            '/assets/avatar/app4.svg',
            '/assets/avatar/app5.svg',
            '/assets/avatar/app6.svg',
            '/assets/avatar/dataset.svg',
          ],
          site: item.site_code,
          id: item.id,
          site_code: item.site_code,
          usages: item.usages,
        }
      }))
      setLoading(false)
    })
  }
  // 移入banner图
  const moveInBanner = (index: number) => {
    if (activeIndex !== index)
      setActiveIndex(index)
  }
  useMount(() => {
    getBannerList()
  })

  return (
    (loading || bannerList.length)
      ? <div id={id} className={cn(s['banner-list'], className)}>
        {
          loading
            ? <Loading type='area'></Loading>
            : bannerList.map((item, index) => {
              return (
                <div
                  onMouseEnter={() => {
                    moveInBanner(index)
                  }}
                  key={index}
                  className={cn(s['banner-item'], index === activeIndex && s['banner-item-active'])}
                >
                  {/* 标题及操作栏部分 */}
                  <div className={s['banner-item-warp']}>
                    <div className={s.title}>{item.name}</div>
                    <div className={s.description}>{item.description}</div>
                    <div className={s.operation}>
                      { index === activeIndex
                    && <Button
                      onClick={() => clickExperience(item)}
                      className={s['action-btn']}
                    >
                      { t('app.action.experience') }
                      <ShareIcon className={s['action-btn-icon']}></ShareIcon>
                    </Button> }
                      <AvatarList
                        list={item.avatarList}
                        number={item.number}
                        size={24}
                        textClass='!text-[10px]  !text-gray-G2 !font-semibold !scale-100'
                        containerClass='!bg-white !border-[2px]'
                      ></AvatarList>
                    </div>
                  </div>
                  {/* 右侧mark部分 */}
                  <div className={cn(s.mark, index === activeIndex || s['active-mark'])}></div>
                  {/* 水印部分 */}
                  <div className={s.watermark}></div>
                </div>
              )
            })
        }
      </div>
      : <></>
  )
}

export default React.memo(Banner)
