.banner-list {
  @apply relative flex gap-4;
}
.banner-item {
  @apply lg:!w-[262px] lg:!py-[30px];
  @apply w-[236px] py-[20px];
  @apply flex gap-5 relative shrink-0 rounded px-[20px];
  transition: width 0.5s ease;
  overflow: hidden;
}
.banner-item-active {
  @apply lg:!w-[calc(100%-556px)];
  @apply w-[calc(100%-500px)];
  @apply !shrink;
}
.banner-item:first-child {
  background: linear-gradient(to right, #FFF -7.64%, #9FD3FF 131.21%);
}
.banner-item:nth-child(2) {
  background: linear-gradient(to right, #FFF -7.64%, #D2C3F3 131.21%);;
}
.banner-item:last-child {
  background: linear-gradient(to right, #FFF -7.64%, #C8E7C5 131.21%);
}
.banner-item-warp {   
  @apply relative flex flex-col w-full shrink;
}

.watermark {
  @apply lg:!w-[210px] lg:!h-[150px] lg:!right-0 lg:!bottom-0;
  @apply w-[170px] h-[170px] right-[-40px] bottom-[-80px];
  @apply absolute;
  z-index: 5;
  background-size: cover;
  background-repeat: no-repeat;
}
.banner-item:first-child .watermark {
  @apply lg:!bg-[url('/assets/app-square/watermark-blue.svg')];
  @apply bg-[url('/assets/app-square/watermark-blue-small.svg')];
}
.banner-item:nth-child(2) .watermark {
  @apply lg:!bg-[url('/assets/app-square/watermark-pink.svg')];
  @apply bg-[url('/assets/app-square/watermark-pink-small.svg')];

}
.banner-item:last-child .watermark {
  @apply lg:!bg-[url('/assets/app-square/watermark-green.svg')];
  @apply bg-[url('/assets/app-square/watermark-green-small.svg')];
}
.banner-item-active .watermark {
  @apply lg:!w-[300px] lg:!h-[217px] lg:!right-[0px] lg:!bottom-[0px];
  @apply md:right-[-20px] md:bottom-0;
}

.mark {
  @apply lg:!w-[200px] lg:!h-[200px];
  @apply w-[131px] h-[131px];
  @apply absolute right-0 shrink-0;
  z-index: 10;
  background-size: cover;
  background-repeat: no-repeat;
  opacity: 1;
  transition: opacity 0.5s ease;
}
.active-mark {
  opacity: 0 !important;
}
.banner-item:first-child .mark {
  background-image: url("/assets/app-square/mark-blue.svg");
}
.banner-item:nth-child(2) .mark {
  background-image: url("/assets/app-square/mark-pink.svg");
}
.banner-item:last-child .mark {
  background-image: url("/assets/app-square/mark-green.svg");
}

.title {
  @apply lg:!text-S7 lg:!leading-H7 mb-[10px];
  @apply text-S5 leading-H5 mb-[4px];
  @apply text-gray-G1;
  font-weight: 600;
}
.description {
  @apply lg:!line-clamp-3 lg:!h-[66px];
  @apply line-clamp-2 h-[44px];
  @apply text-gray-G2 shrink;
  position: relative;
  z-index: 20;
  font-size: 14px;
  line-height: 22px; /* 157.143% */
}
.banner-item-active .description{
  @apply lg:!w-[calc(100%-240px)];
  @apply !w-[calc(100%-170px)];
}
.operation {
  @apply absolute bottom-0 flex items-center gap-3;
}
.action-btn {
  @apply lg:!h-[36px] lg:!text-S3 lg:!leading-H3 lg:!gap-8;
  @apply h-[28px] !text-S1 !leading-H1 gap-5;
  @apply !bg-gray-G1 !text-white hover:!bg-gray-G1 hover:!text-white !border-0;
}
.action-btn-icon {
  @apply lg:!w-[14px] lg:!h-[14px];
  @apply w-[12px] h-[12px];
}