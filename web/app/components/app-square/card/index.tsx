'use client'

import { useTranslation } from 'react-i18next'
import { clickExperience, goSuperAgent } from '../utils'
import s from './styles/index.module.css'
import type { AppMarket } from '@/types/app-market'
import { AppMarketPower } from '@/types/app-market'
import { formatNumber2 } from '@/utils/format'

// 公共组件
import Card from '@/app/components/base/card'
import Badge from '@/app/components/base/badge'
import Avatar from '@/app/components/base/avatar'
import Button from '@/app/components/base/button'
import { User02 } from '@/app/components/base/icons/src/vender/line/users'
import AppTag from '@/app/components/app/common/tag'

export type AppSquareCardProps = {
  app: AppMarket
}

const AppSquareCard = ({ app }: AppSquareCardProps) => {
  const { t } = useTranslation()

  return <Card
    className={s.card}
    description={app.description || ''}
    generateHead={() => {
      return <Avatar avatar={app.icon_url} size={56} className='w-[56px] h-[56px] !rounded'></Avatar>
    }}
    title={() => (
      <>
        <div className='mb-2 truncate title-16-26'>{app.name}</div>
        <div className='flex gap-2'>
          <AppTag appMode={app.mode}></AppTag>
          {
            app.acl === AppMarketPower.Copy
                && <Badge text={t('app.info.publicConfig') as string}></Badge>
          }
        </div>
        {/* //后续根据类别判断是否展示标签 */}
        {(app.category_id === '9b7bd97b-fe80-4e49-bc43-f78f614add36') && <div className={s.featureTag}>{t('app.info.superAgentTag')}</div>}
      </>
    )}
  >
    <>
      <Button
        size={'medium'}
        onClick={() => app.category_id === '9b7bd97b-fe80-4e49-bc43-f78f614add36' ? goSuperAgent(app) : clickExperience(app)}
        className='w-full hidden group-hover:flex'
        variant={'primary'}
      >
        { t('app.action.experience') }
      </Button>
      <div className={`${s['card-footer']} group-hover:!invisible`}>
        <User02 className='w-3 h-3'></User02>
        <span className='text-S1 leading-H1 text-gray-G2'>{formatNumber2(Number(app.usages))}</span>
      </div>
    </>
  </Card>
}

export default AppSquareCard
