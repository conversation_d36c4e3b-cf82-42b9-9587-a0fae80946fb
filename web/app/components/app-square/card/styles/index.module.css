.card-footer {
  @apply flex absolute bottom-0 left-0 visible items-center gap-[6px];
}
.card {
  background: linear-gradient(328deg, rgba(22, 119, 255, 0.02) 20.64%, rgba(255, 255, 255, 0.00) 64.68%), #FFF !important;
  box-shadow: 0px 1px 1px 0px rgba(159, 176, 201, 0.20) !important;
  border: 2px solid rgba(217, 220, 227, 0.20) !important;
}
.card:hover {
  border: 2px solid #4491FA !important;
  background: linear-gradient(182deg, rgba(49, 104, 245, 0.15) 2.7%, rgba(255, 255, 255, 0.08) 50.93%, rgba(255, 255, 255, 0.00) 99.16%), linear-gradient(328deg, rgba(22, 119, 255, 0.02) 20.64%, rgba(255, 255, 255, 0.00) 64.68%), #FFF !important;
  box-shadow: 0px 3px 8px 0px rgba(159, 176, 201, 0.50) !important;
}
.featureTag {
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(95.93deg, #6A59FF 0%, #A449FF 100.39%);
  color: white;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 0 4px 0 8px;
}