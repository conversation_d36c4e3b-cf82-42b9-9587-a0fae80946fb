import type { AppMarket } from '@/types/app-market'
import { experienceAppspublic } from '@/service/market'

// 立即体验
export const clickExperience = (app: AppMarket | any) => {
  experienceAppspublic(app.id)
  // const { name, app_id, usages, description } = app
  // const params = { name, app_id, usages, description }
  // const appEncoded = encodeURIComponent(JSON.stringify(app))
  window.open(`/marketChat/${app.site_code}?id=${app.id}`, '_blank')
}

// 超级智能体对话
export const goSuperAgent = (app: AppMarket | any) => {
  window.open(`/super-agent/${app.app_id}?id=${app.id}&app_id=${app.app_id}`, '_blank')
}
